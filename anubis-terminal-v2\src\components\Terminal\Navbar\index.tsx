"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import { terminalNavLinks, GlobalNavLinks, chains } from "@/utils/data";
import { usePathname } from "next/navigation";
import { FaPaste, FaUserAlt, FaEye, FaCopy, FaEyeSlash, FaQrcode, FaCaretDown, FaGift, FaWallet, FaUsers, FaQuestionCircle } from "react-icons/fa";
import { FaMagnifyingGlass } from "react-icons/fa6";
import { GiHamburgerMenu } from "react-icons/gi";
import { IoMdCog, IoMdClose } from "react-icons/io";
import { FaLink } from "react-icons/fa6";
import { motion, AnimatePresence } from "framer-motion";
import { TiTick } from "react-icons/ti";
import { useWeb3 } from "@/contexts/Web3Context";
import { IoMdNotifications, IoIosLogOut } from "react-icons/io";
import { IoDocumentTextOutline } from "react-icons/io5";
import { useAuth } from "@/contexts/AuthContext";
import { useUserData } from "@/hooks/useUserData";
import { toast } from 'react-toastify';
import { updateSettings } from "@/services/bot";
import { ImSpinner2 } from "react-icons/im"; // Add this import for the spinner
import SettingsModal from "./SettingsModal";

export type Chain = typeof chains[number];

export default function Navbar() {
    const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
    const [openSlider, setOpenSlider] = useState<boolean>(false);
    const [chainSelector, setChainSelector] = useState<boolean>(false);
    const [settingsModal, setSettingsModal] = useState<boolean>(false);
    const { selectedChain, setSelectedChain, recentlyViewed } = useWeb3();
    const { token } = useAuth();
    const { data, error } = useUserData(token);
    const [chainLoading, setChainLoading] = useState<string | null>(null); // Add loading state
    const [tokenSearch, setTokenSearch] = useState<boolean>(false);

    const pathname = usePathname();

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
        setOpenSlider(!openSlider);
    };

    useEffect(() => {
        if (error) {
            toast.error("Unable to load user data. Please refresh the page or try again later.");
        }
    }, [error]);

    const balance = data && data.balance ? Number(data?.balance) : 0;

    const filteredRecentlyViewed = recentlyViewed.filter(token => token.chain === selectedChain?.slug);

    // Write Async Call to Change Chain ID
    async function handleSettingsUpdate(selectedChain: Chain) {
        setChainLoading(selectedChain.chain); // Set loading for this chain
        const chainId = selectedChain.chainId;
        try {
            await updateSettings(token as string, { chainId });
            setSelectedChain(selectedChain);
            setChainSelector(false);
            toast.success("Network switched successfully");
        } catch (error: any) {
            if (error && error.response) {
                toast.error(error.response.data.message);
            }
        } finally {
            setChainLoading(null); // Remove loading state
        }
    }

    // Fallback UI if selectedChain is not loaded
    if (!selectedChain) {
        return (
            <section className="flex justify-center items-center h-16 w-full">
                <ImSpinner2 className="animate-spin text-white text-2xl" />
            </section>
        );
    }

    return (
        <section className="flex justify-between items-center flex-col max-w-full">
            <div className="items-center justify-start p-1 border-b border-white/10 min-w-full gap-x-2 md:flex hidden">
                <span className="text-sm font-space-grotesk font-medium text-gray-400 px-2.5 border-r border-white/30">Recent</span>
                <div className="flex gap-4 items-center px-2.5 hover:cursor-pointer">
                    {filteredRecentlyViewed.map((token, index) => {
                        const tokenData = token.data?.results?.[0];
                        if (!tokenData) return null;
                        const { details, marketData } = tokenData;
                        return (
                            <div key={index} className="flex items-center gap-2">
                                {details?.imageThumbUrl ? (
                                    <img src={details.imageThumbUrl} className="w-3 h-3 object-contain rounded-full" alt={details.symbol} />
                                ) : (
                                    <div className="w-3 h-3 bg-gray-700 rounded-full flex items-center justify-center text-[8px]">{details?.symbol?.charAt(0) ?? '?'}</div>
                                )}
                                <span className="font-space-grotesk font-medium text-sm">{details?.symbol ?? ''}</span>
                                <span className="font-space-grotesk font-medium text-[10px] text-white/60">{marketData?.priceUSD ? `$${parseFloat(marketData.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 6 })}` : '-'}</span>
                            </div>
                        );
                    })}
                </div>
            </div>
            <nav className="min-w-full border-b border-white/10 py-3 px-2 relative flex items-center justify-between">
                <div className="flex items-center justify-start gap-x-5">
                    <Link href="/terminal/trending" className="flex items-center justify-start z-10 gap-2">
                        <img src="/logo/icon.png" className="w-5 h-5 object-contain" alt="Anubis Logo" />
                        <span className="font-orbitron font-bold text-sm">Anubis</span>
                    </Link>
                    <div className="items-center gap-x-5 capitalize md:flex hidden">
                        {terminalNavLinks.map((link: GlobalNavLinks) => (
                            <Link href={link.url} key={link.title} className={`font-space-grotesk font-medium hover:text-white text-sm ${link.url.includes(pathname) ? "text-white" : "text-gray-400"} `}>
                                {link.title}
                            </Link>
                        ))}
                    </div>
                </div>
                <div className="flex items-stretch md:gap-x-2 gap-x-1 justify-end">
                    <motion.button className="text-[10px] md:text-sm  font-space-grotesk font-medium text-gray-400 hover:text-white items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer md:hidden flex"
                        initial={{
                            backgroundColor: "rgba(255, 255, 255, 1)",
                        }}
                        whileTap={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            scale: 1.03
                        }}
                    >
                        <FaLink className="text-black" />
                        <span className="text-black font-bold">Share</span>
                    </motion.button>
                    <motion.button className="text-[10px] md:text-sm  font-space-grotesk font-medium text-gray-400 hover:text-white flex items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer"
                        whileHover={{
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        }}
                        whileTap={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            scale: 1.03
                        }}
                    >
                        <FaPaste className="text-white" />
                        <span className="text-sm">Paste CA</span>
                    </motion.button>
                    <motion.button className="text-[10px] md:text-sm font-space-grotesk font-medium text-gray-400 hover:text-white flex items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer"
                        whileHover={{
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        }}
                        whileTap={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            scale: 1.03
                        }}
                        onClick={() => setTokenSearch(true)}
                        >
                        <FaMagnifyingGlass className="text-white" />
                        <span className="text-sm">Search</span>
                        <span className="text-[10px] md:text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden md:flex items-center gap-2 border border-white/30 px-2  rounded-sm hover:cursor-pointer">
                            /
                        </span>
                    </motion.button>
                    <motion.button className="text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer md:flex"
                        whileHover={{
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        }}
                        whileTap={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            scale: 1.03
                        }}
                        onClick={() => setChainSelector(true)}
                    >
                        <FaWallet className="text-white" />
                        <span>{balance.toFixed(2)}</span>
                        {selectedChain ? (
                            <img src={selectedChain.logo} className="w-5 h-5 object-contain" alt={selectedChain.chain} />
                        ) : (
                            <ImSpinner2 className="animate-spin text-white w-5 h-5" />
                        )}
                    </motion.button>
                    <motion.button className="text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer md:flex"
                        whileHover={{
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        }}
                        whileTap={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            scale: 1.03
                        }}
                        onClick={() => setSettingsModal(true)}>
                        <IoMdCog className="text-white" />
                    </motion.button>
                    <motion.button className="text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer min-h-full md:flex"
                        whileHover={{
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        }}
                        whileTap={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            scale: 1.03
                        }}
                        onClick={() => setOpenSlider(!openSlider)}
                    >
                        <FaUserAlt className="text-white" />
                    </motion.button>

                    {/* Mobile Menu Button */}
                    <motion.button
                        className="text-sm font-space-grotesk font-medium text-gray-400 hover:text-white items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer min-h-full flex md:hidden"
                        onClick={toggleMenu}
                        aria-label="Toggle menu"
                        whileTap={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                        {isMenuOpen ? <IoMdClose className="text-[10px]" /> : <GiHamburgerMenu className="text-[10px]" />}
                    </motion.button>
                </div>
            </nav>
            <AnimatePresence mode="wait">
                {openSlider && <SliderMenu openSlider={openSlider} setOpenSlider={setOpenSlider} />}
                {chainSelector && (
                    <ChainSelectorModal
                        chainSelector={chainSelector}
                        setChainSelector={setChainSelector}
                        handleSettingsUpdate={handleSettingsUpdate}
                        chainLoading={chainLoading} // Pass loading state
                    />
                )}
                {tokenSearch && (
                    <TokenSearchModal
                        tokenSearch={tokenSearch}
                        setTokenSearch={setTokenSearch}
                    />
                )}
                {settingsModal && (
                    <SettingsModal
                        openSettingsModal={settingsModal}
                        setOpenSettingsModal={setSettingsModal}
                    />
                )}
            </AnimatePresence>
        </section>
    )
}

function SliderMenu({ openSlider, setOpenSlider }: { openSlider: boolean, setOpenSlider: React.Dispatch<React.SetStateAction<boolean>> }) {
    const [openProfileDropdown, setOpenProfileDropdown] = useState<boolean>(false);
    const [profileDetailsBlurred, setProfileDetailsBlurred] = useState<boolean>(false);
    const [openWalletDropdown, setOpenWalletDropdown] = useState<boolean>(false);
    const [openRewardsDropdown, setOpenRewardsDropdown] = useState<boolean>(false);
    const [openReferralsDropdown, setOpenReferralsDropdown] = useState<boolean>(false);
    const { selectedChain, setSelectedChain, chainList } = useWeb3();
    const { logout } = useAuth();
    const { token } = useAuth();
    const { data, loading, error } = useUserData(token);


    return (
        <motion.section className="min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="absolute inset-0" onClick={() => setOpenSlider(false)} />

            <motion.nav className="inset-0 flex flex-col justify-start gap-y-3 transition-all duration-300 ease-in-out z-[60] md:min-w-[20.5%] md:max-w-[20.5%] w-[80%] border-white/10 border bg-black/80 p-3"
                initial={{ x: "100%" }}
                animate={{ x: "0%" }}
                exit={{ x: "100%" }}
                transition={{ duration: 0.3 }}
            >
                <div className="">
                    <Link href="/" className="flex items-center justify-start z-10 gap-2">
                        <img src="/logo/icon.png" className="w-8 h-8 object-contain" alt="Anubis Logo" />
                        <span className="font-orbitron font-bold text-xl">Anubis</span>
                    </Link>
                </div>
                {/* Profile Dropdown */}
                <motion.div className="flex flex-col gap-x-2 justify-between bg-black border border-white/10 p-3 rounded-sm cursor-pointer" onClick={() => setOpenProfileDropdown(!openProfileDropdown)}
                >
                    <div className="flex gap-x-2 justify-between w-full">
                        <div className="flex gap-x-2 items-center">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <FaUserAlt className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className={`font-bold ${profileDetailsBlurred ? "blur-sm" : ""}`}>@{data?.user?.telegramUsername}</span>
                                <span className={`flex items-center gap-x-1 ${profileDetailsBlurred ? "blur-sm" : ""}`}>ID: <span className="truncate w-10">{data?.user?.tgUserId}</span><FaCopy className="cursor-pointer" /></span>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-2">
                            {profileDetailsBlurred ? <FaEyeSlash onClick={(e) => {
                                e.stopPropagation();
                                setProfileDetailsBlurred(!profileDetailsBlurred)
                            }} className="text-white text-md cursor-pointer" /> : <FaEye onClick={(e) => {
                                e.stopPropagation();
                                setProfileDetailsBlurred(!profileDetailsBlurred)
                            }} className="text-white text-sm cursor-pointer" />}
                            <motion.div
                                animate={{ rotate: openProfileDropdown ? 180 : 0 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <FaCaretDown className="text-white text-sm" />
                            </motion.div>
                        </div>
                    </div>
                    <AnimatePresence mode="wait">
                        {openProfileDropdown && (
                            <motion.div className="flex flex-col gap-y-3 mt-3"
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <motion.button
                                    className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-sm"
                                    whileHover={{
                                        boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                        scale: 1.05
                                    }}
                                    whileTap={{
                                        backgroundColor: "#f0f0f0",
                                        scale: 1.03
                                    }}
                                    onClick={(e) => {
                                        e.stopPropagation()
                                    }}
                                >
                                    Select Your Wallet
                                </motion.button>
                                <hr />
                                <div className="flex justify-between text-sm">
                                    <span>Fee Discount</span> <span>0.00%</span>
                                </div>
                                <hr />
                                <div className="flex justify-between text-sm">
                                    <span>Trading Fee</span> <span>1.00%</span>
                                </div>
                            </motion.div>
                        )}</AnimatePresence>
                </motion.div>
                {/* Profile Dropdown */}

                {/* Sign in Mobile */}
                {/* <motion.div className="flex flex-col gap-x-2 justify-between bg-black border border-white/10 p-3 rounded-sm cursor-pointer" onClick={() => setOpenWalletDropdown(!openWalletDropdown)}
                >
                    <div className="flex gap-x-2 justify-between w-full">
                        <div className="flex gap-x-2 items-center">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <FaQrcode className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Sign In On Mobile</span>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-2">
                            <motion.div
                                animate={{ rotate: openWalletDropdown ? 180 : 0 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <FaCaretDown className="text-white text-sm" />
                            </motion.div>
                        </div>
                    </div>
                    <AnimatePresence mode="wait">
                        {openWalletDropdown && (
                            <motion.div className="flex flex-col gap-y-3 mt-3"
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <motion.button
                                    className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-sm"
                                    whileHover={{
                                        boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                        scale: 1.05
                                    }}
                                    whileTap={{
                                        backgroundColor: "#f0f0f0",
                                        scale: 1.03
                                    }}
                                >
                                    Show QR Code
                                </motion.button>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.div> */}
                {/* Sign in Mobile */}

                {/* Rewards */}
                <motion.div className="flex flex-col gap-x-2 gap-y-4 justify-between bg-black border border-white/10 py-3 rounded-sm cursor-pointer"
                >
                    <div className="flex gap-x-2 justify-between w-full px-3" onClick={() => setOpenRewardsDropdown(!openRewardsDropdown)}>
                        <div className="flex gap-x-2 items-center">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <FaGift className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Rewards</span>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-2">
                            <motion.div
                                animate={{ rotate: openRewardsDropdown ? 180 : 0 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <FaCaretDown className="text-white text-sm" />
                            </motion.div>
                        </div>
                    </div>
                    <AnimatePresence mode="wait">
                        {openRewardsDropdown && (
                            <motion.div className="flex flex-col gap-y-3 mt-3 w-full px-3"
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="text-[10px] bg-white/10 p-2 rounded-sm">
                                    <span className="">Enhance your trading journey and access premium perks. Your trading activity directly impacts the rewards you receive!</span>
                                </div>
                                <hr />
                                <div className="flex justify-between items-center text-sm">
                                    <span>Your volume</span> <span>0.00%</span>
                                </div>
                                <hr />
                                <div className="flex justify-between items-center text-sm">
                                    <span>Revenue</span>
                                    <motion.button
                                        className="font-orbitron font-semibold bg-white text-black px-2 py-1.5 rounded-md hover:shadow-lg hover:shadow-white/20 text-sm"
                                        whileHover={{
                                            boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                            scale: 1.05
                                        }}
                                        whileTap={{
                                            backgroundColor: "#f0f0f0",
                                            scale: 1.03
                                        }}
                                    >
                                        Claim
                                    </motion.button>
                                </div>
                            </motion.div>)}
                    </AnimatePresence>
                    <hr className="border-white/20" />
                    <div className="flex gap-x-2 justify-between w-full px-3" onClick={() => setOpenReferralsDropdown(!openReferralsDropdown)}>
                        <div className="flex gap-x-2 items-center ">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <FaUsers className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Referrals</span>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-2 ">
                            <span className="w-5 h-5 text-white bg-white/30 flex items-center justify-center text-sm border border-white/20 rounded-sm">
                                {0}
                            </span>
                            <motion.div
                                animate={{ rotate: openReferralsDropdown ? 180 : 0 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <FaCaretDown className="text-white text-sm" />
                            </motion.div>
                        </div>
                    </div>
                    <AnimatePresence mode="wait">
                        {openReferralsDropdown && (
                            <motion.div className="flex flex-col gap-y-3 mt-3 w-full px-3"
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="text-[10px] bg-white/10 p-2 rounded-sm">
                                    <span>Please select a wallet address to receive your referral rewards</span>
                                </div>
                                <motion.button
                                    className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-sm"
                                    whileHover={{
                                        boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                        scale: 1.05
                                    }}
                                    whileTap={{
                                        backgroundColor: "#f0f0f0",
                                        scale: 1.03
                                    }}
                                >
                                    Select Wallet for Rewards
                                </motion.button>
                                <hr />
                                <div className="flex justify-between items-center text-sm">
                                    <span>Your receive</span> <span>0.00%</span>
                                </div>
                                <hr />
                                <div className="flex justify-between items-center text-sm">
                                    <span>Revenue</span>
                                    <motion.button
                                        className="font-orbitron font-semibold bg-white text-black px-2 py-1.5 rounded-md hover:shadow-lg hover:shadow-white/20 text-sm"
                                        whileHover={{
                                            boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                            scale: 1.05
                                        }}
                                        whileTap={{
                                            backgroundColor: "#f0f0f0",
                                            scale: 1.03
                                        }}
                                    >
                                        Claim
                                    </motion.button>
                                </div>
                            </motion.div>)}
                    </AnimatePresence>
                </motion.div>
                {/* Rewards */}

                {/* Notifications */}
                <motion.div className="flex flex-col gap-x-2 justify-between bg-black border border-white/10 p-3 rounded-sm cursor-pointer"
                >
                    <div className="flex gap-x-2 justify-between w-full">
                        <div className="flex gap-x-2 items-center">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <IoMdNotifications className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Notifications</span>
                            </div>
                        </div>
                    </div>
                </motion.div>
                {/* Notifications */}

                {/* Support Section */}
                <motion.div className="flex flex-col gap-x-2 gap-y-4 justify-between bg-black border border-white/10 py-3 rounded-sm cursor-pointer"
                >
                    <div className="flex gap-x-2 justify-between w-full px-3">
                        <div className="flex gap-x-2 items-center">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <FaQuestionCircle className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Support</span>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-2">
                            <motion.div
                                animate={{ rotate: openRewardsDropdown ? 180 : 0 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                                <FaCaretDown className="text-white text-sm" />
                            </motion.div>
                        </div>
                    </div>
                    <AnimatePresence mode="wait">
                        {openRewardsDropdown && (
                            <motion.div className="flex flex-col gap-y-3 mt-3 w-full px-3"
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="text-[10px] bg-white/10 p-2 rounded-sm">
                                    <span className="">Enhance your trading journey and access premium perks. Your trading activity directly impacts the rewards you receive!</span>
                                </div>
                                <hr />
                                <div className="flex justify-between items-center text-sm">
                                    <span>Your volume</span> <span>0.00%</span>
                                </div>
                                <hr />
                                <div className="flex justify-between items-center text-sm">
                                    <span>Revenue</span>
                                    <motion.button
                                        className="font-orbitron font-semibold bg-white text-black px-2 py-1.5 rounded-md hover:shadow-lg hover:shadow-white/20 text-sm"
                                        whileHover={{
                                            boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                            scale: 1.05
                                        }}
                                        whileTap={{
                                            backgroundColor: "#f0f0f0",
                                            scale: 1.03
                                        }}
                                    >
                                        Claim
                                    </motion.button>
                                </div>
                            </motion.div>)}
                    </AnimatePresence>
                    <hr className="border-white/20" />
                    <div className="flex gap-x-2 justify-between w-full px-3">
                        <div className="flex gap-x-2 items-center ">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <IoDocumentTextOutline className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Privacy Policy</span>
                            </div>
                        </div>
                    </div>
                    <hr className="border-white/20" />
                    <div className="flex gap-x-2 justify-between w-full px-3" onClick={logout}>
                        <div className="flex gap-x-2 items-center ">
                            <div className="w-8 h-8 flex items-center justify-center bg-white/10 rounded-full">
                                <IoIosLogOut className="text-white text-sm" />
                            </div>
                            <div className="text-sm flex flex-col gap-y-1">
                                <span className="font-bold">Logout</span>
                            </div>
                        </div>
                    </div>
                </motion.div>
                {/* Support Section */}
            </motion.nav>
        </motion.section>
    )
}

function ChainSelectorModal({
    chainSelector,
    setChainSelector,
    handleSettingsUpdate,
    chainLoading // Receive loading state
}: {
    chainSelector: boolean,
    setChainSelector: React.Dispatch<React.SetStateAction<boolean>>,
    handleSettingsUpdate: (chain: Chain) => void,
    chainLoading?: string | null
}) {
    const { chainList, selectedChain } = useWeb3();
    return (
        <motion.section className="min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="absolute inset-0" onClick={() => setChainSelector(false)} />

            <motion.nav className="bg-black/95 backdrop-blur-sm w-full max-w-sm h-full p-4 relative z-10"
                initial={{ x: "100%" }}
                animate={{ x: "0%" }}
                exit={{ x: "100%" }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex items-center justify-between mb-6">
                    <h3 className="font-orbitron font-bold text-lg">Select Chain</h3>
                    <button
                        onClick={() => setChainSelector(false)}
                        className="p-2 hover:bg-white/10 rounded-md transition-colors"
                    >
                        <IoMdClose className="text-white text-sm" />
                    </button>
                </div>

                <div className="space-y-2">
                    {chainList.map((chain) => (
                        <motion.button
                            key={chain.chain}
                            onClick={() => handleSettingsUpdate(chain)}
                            className={`flex items-center gap-3 w-full p-3 rounded-md hover:bg-white/10 transition-colors ${selectedChain?.chain === chain.chain ? "bg-white/10" : ""}`}
                            whileTap={{ scale: 0.98 }}
                            disabled={!!chainLoading} // Disable all buttons while loading
                        >
                            <img
                                src={chain.logo}
                                alt={chain.chain}
                                className="w-5 h-5 object-contain"
                            />
                            <span className="font-space-grotesk text-sm">{chain.chain}</span>
                            {/* Spinner for the chain being loaded */}
                            {chainLoading === chain.chain ? (
                                <ImSpinner2 className="animate-spin text-white ml-auto" />
                            ) : selectedChain?.chain === chain.chain ? (
                                <TiTick className="text-green-500 ml-auto" />
                            ) : null}
                        </motion.button>
                    ))}
                </div>
            </motion.nav>
        </motion.section>
    )
}

function NotificationsModal({ }) {
    return (
        <motion.section className="min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="absolute inset-0" />
        </motion.section>
    )
}

// Implement Token Search Modal
function TokenSearchModal({ tokenSearch, setTokenSearch }: { tokenSearch: boolean, setTokenSearch: React.Dispatch<React.SetStateAction<boolean>> }){
    return (
        <motion.section className="min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="absolute inset-0" />
            <motion.nav className="bg-black/95 backdrop-blur-sm w-full max-w-sm h-full p-4 relative z-10"
                initial={{ x: "100%" }}
                animate={{ x: "0%" }}
                exit={{ x: "100%" }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex items-center justify-between mb-6">
                    <h3 className="font-orbitron font-bold text-lg">Search for a Token</h3>
                    <button
                        onClick={() => setTokenSearch(false)}
                        className="p-2 hover:bg-white/10 rounded-md transition-colors"
                    >
                        <IoMdClose className="text-white text-sm" />
                    </button>
                </div>

                <div className="space-y-2">
                    <input
                        type="text"
                        placeholder="Enter Contract Address (0x...)"
                        className="w-full p-3 rounded-md bg-white/10 text-white border border-white/20 focus:outline-none focus:ring-2 focus:ring-white/50"
                    />
                </div>
            </motion.nav>


        </motion.section>
    )
}