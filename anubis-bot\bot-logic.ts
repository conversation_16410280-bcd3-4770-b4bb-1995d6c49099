// Shared bot logic for use in both bot and API (no bot runner here)
import { getConfig, odosSupportedChains, ESupportedChains } from "./config";
import { Pair } from "./libs/gecko";
import { privateKeyToAccount } from "viem/accounts";
import { createWalletClient, createPublicClient, http, parseUnits, formatUnits, Address, getContract } from "viem";
import { getTokenContractDecimal, approveTokenAllowance } from "./libs/token";
import { getSwapQuote } from "./libs/gecko";
import { parseAbi } from "viem";

// ...other helpers/types as needed...

const ROUTE_ABI = parseAbi([
    "function processRoute(address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address to, bytes routeCode) payable returns (uint256 amountOut)",
    "function transferValueAndprocessRoute(address transferValueTo, uint256 value, address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address to, bytes routeCode) payable returns (uint256 amountOut)",
]);

// Dummy getWallet for API context
function getWallet(ctx: any) {
    const wallet = ctx.session.settings.wallets[ctx.session.walletId];
    if (!wallet) throw new Error("No wallet found");
    return wallet;
}

// Export swapTokenFor for use in both bot and API
export async function swapTokenFor(
    ctx: any,
    tokenIn: Address,
    tokenOut: Address,
    pairData: Pair,
    amount: number,
    taxAmount = getConfig(ctx.session.settings.chainId).tax.default
) {
    // ...copy the full logic from your index.ts swapTokenFor here...
    // For brevity, you can copy the latest working version from your index.ts
    // Make sure to import any helpers it needs at the top of this file
    // ...existing logic...
}
