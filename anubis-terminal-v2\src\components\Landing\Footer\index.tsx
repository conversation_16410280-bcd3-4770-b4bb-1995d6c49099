"use client"
import { motion } from "framer-motion";
import Link from "next/link";
import { Fa<PERSON><PERSON><PERSON>, FaT<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>rd, FaGith<PERSON> } from "react-icons/fa";

const telegramBotUsername = process.env.NODE_ENV === "production" ? process.env.NEXT_PUBLIC_BOT_USERNAME_PROD : process.env.NEXT_PUBLIC_BOT_USERNAME_DEV;

export default function Footer() {
  const currentYear = new Date().getFullYear();
  
  const footerLinks = [
    {
      title: "Product",
      links: [
        { name: "Features", href: "#features" },
        { name: "How It Works", href: "#how-it-works" },
        { name: "Pricing", href: "#pricing" },
        { name: "Documentation", href: "/documentation" }
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About Us", href: "/about" },
        { name: "Blog", href: "/blog" },
        { name: "Careers", href: "/careers" },
        { name: "Contact", href: "/contact" }
      ]
    },
    {
      title: "Legal",
      links: [
        { name: "Terms of Service", href: "/terms" },
        { name: "Privacy Policy", href: "/privacy" },
        { name: "Cookie Policy", href: "/cookies" }
      ]
    }
  ];
  
  const socialLinks = [
    { icon: <FaTelegram />, href: `https://t.me/${telegramBotUsername}`, label: "Telegram" },
    { icon: <FaTwitter />, href: "https://twitter.com/AnubisTerminal", label: "Twitter" },
    { icon: <FaDiscord />, href: "https://discord.gg/anubisterminal", label: "Discord" },
    { icon: <FaGithub />, href: "https://github.com/anubisterminal", label: "GitHub" }
  ];

  return (
    <footer className="bg-[#0a0a0a] border-t border-white/10 pt-16 pb-8 px-4 md:px-8 lg:px-12">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          {/* Logo and description */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center gap-2 mb-4">
              <img src="/logo/icon.png" className="w-8 h-8 object-contain" alt="Anubis Logo" />
              <span className="font-orbitron font-bold text-xl text-white">Anubis</span>
            </Link>
            <p className="text-gray-400 font-space-grotesk mb-6 max-w-md">
              The most advanced on-chain trading terminal for professional traders. Execute trades with lightning-fast speed and enhanced security.
            </p>
            
            {/* Social links */}
            <div className="flex gap-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 rounded-full bg-white/5 flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                  aria-label={social.label}
                  whileHover={{ y: -3 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </div>
          
          {/* Footer links */}
          {footerLinks.map((column, columnIndex) => (
            <div key={columnIndex}>
              <h3 className="font-orbitron font-bold text-white mb-4">{column.title}</h3>
              <ul className="space-y-3">
                {column.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link 
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors font-space-grotesk"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        {/* Bottom section */}
        <div className="pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 font-space-grotesk text-sm mb-4 md:mb-0">
            &copy; {currentYear} Anubis Terminal. All rights reserved.
          </p>
          <div className="flex gap-6">
            <Link href="/terms" className="text-gray-500 hover:text-white transition-colors font-space-grotesk text-sm">
              Terms
            </Link>
            <Link href="/privacy" className="text-gray-500 hover:text-white transition-colors font-space-grotesk text-sm">
              Privacy
            </Link>
            <Link href="/cookies" className="text-gray-500 hover:text-white transition-colors font-space-grotesk text-sm">
              Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
