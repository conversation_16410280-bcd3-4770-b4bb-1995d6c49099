{"name": "anubis-trading-teams", "private": true, "version": "0.0.0", "description": "Anubis Trading Teams", "keywords": [], "dependencies": {"@sailshq/connect-redis": "^3.2.1", "@sailshq/lodash": "^3.10.3", "@sailshq/socket.io-redis": "^5.2.0", "@telegraf/session": "^2.0.0-beta.7", "async-retry": "^1.3.3", "axios": "^1.7.7", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "grunt": "1.0.4", "nanoid": "^5.0.8", "sails": "^1.5.2", "sails-hook-apianalytics": "^2.0.6", "sails-hook-cronjob": "^1.0.5", "sails-hook-grunt": "^5.0.0", "sails-hook-orm": "^4.0.0", "sails-hook-sockets": "^2.0.0", "sails-mongo": "^2.1.2", "telegraf": "^4.16.3"}, "devDependencies": {"eslint": "5.16.0", "nodemon": "^3.1.10"}, "scripts": {"start": "NODE_ENV=production node app.js", "test": "npm run lint && npm run custom-tests && echo 'Done.'", "lint": "./node_modules/eslint/bin/eslint.js . --max-warnings=0 --report-unused-disable-directives && echo '✔  Your .js files look good.'", "custom-tests": "echo \"(No other custom tests yet.)\" && echo", "dev": "NODE_ENV=development node app"}, "main": "app.js", "repository": {"type": "git", "url": "git://github.com/anonymous node/sails user/anubis-trading-teams.git"}, "author": "anonymous node/sails user", "license": "", "engines": {"node": "^22.11"}}