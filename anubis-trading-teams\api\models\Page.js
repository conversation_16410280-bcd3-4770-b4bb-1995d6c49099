/**
 * Page.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
  attributes: {
    group: {
      model: "group",
      required: true,
    },
    displayName: {
      type: "string",
      description: "Page Title",
    },
    pageBio: {
      type: "string",
      description: "Page Description",
    },
    domainName: {
      type: "string",
      description: "Project Domain Name",
    },
    pageLogo: {
      type: "json",
      description: "Logo in Telegram Update",
      defaultsTo: null,
    },
    banner: {
      type: "json",
      description: "Banner in Telegram Update",
      defaultsTo: null,
    },
    pageTheme: {
      type: "string",
      description: "Page Theme",
      isIn: ["dark", "light"],
      defaultsTo: "dark",
    },
    pageColor: {
      type: "string",
      description: "Page Color - BG",
    },
    pageType: {
      type: "string",
      description: "Page Type",
      isIn: ["content_creator", "token_page"],
      defaultsTo: "content_creator",
    },
    socialMedia: {
      collection: "socialmedia",
      via: "page",
    },
    customSocialMedia: {
      collection: "customsocialmedia",
      via: "page",
    },
    xVerified: {
      type: "boolean",
      description: "X Verified",
      defaultsTo: false,
    },
    tokenData: {
      type: "json",
      defaultsTo: {
        contractAddress: "",
        symbol: "",
        chain: "",
        name: "",
        logo: "",
        websites: [],
        socials: [],
      },
    },
    favoriteTokens: {
      type: "json",
      defaultsTo: [
        {
          contractAddress: "",
          chain: "",
          name: "",
          logo: "",
          symbol: "",
          websites: [],
          socials: [],
        },
      ],
      description: "Favorite Tokens",
    },
  },
};
