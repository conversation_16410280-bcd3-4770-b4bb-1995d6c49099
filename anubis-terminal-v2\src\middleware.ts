import { NextResponse, NextRequest } from "next/server";
const baseURL =
    process.env.NODE_ENV == "production"
        ? "https://anubis-terminal-v2.vercel.app"
        : "http://localhost:3000";

export function middleware(request: NextRequest) {
    const url = request.url;
    if (url.includes("/login/terminal")) {
        const cookie = request.cookies.get("anubis_terminal");

        if (cookie) {
            console.log(
                `Checking For Cookie on Auth Page`,
                cookie ? "Cookie Found 🍪" : "No Cookie ❌"
            );
            return NextResponse.redirect(`${baseURL}/terminal/trending`);
        }
    }

    if (url.includes("/terminal/")) {
        const cookie = request.cookies.get("anubis_terminal");
        if (!cookie) {
            console.log(
                `Checking For Cookie on Dashboard Page`,
                cookie ? "Cookie Found 🍪" : "No Cookie ❌"
            );
            return NextResponse.redirect(`${baseURL}/login`);
        }
    }
}
