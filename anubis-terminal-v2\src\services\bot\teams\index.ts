import axios from "axios";


const baseURL = "https://teams.anubistrade.xyz"

export const getTeam = async (id: string) => {
    try {
        const response = await axios.get(`${baseURL}/page/${id}`, {
            headers: {
                Authorization: `Bearer ${process.env.NEXT_PUBLIC_ANUBIS_BEARER_TOKEN}`,
            },
        });
        return response.data;
    } catch (error) {
        console.error("Failed to fetch team:", error);
        throw error;
    }
};