const telegramUtils = require("../../../api/utils/telegram-utils");

module.exports = {
  friendlyName: "Get page",

  description: "Retrieve a page by its domain name and process any Telegram file references",

  inputs: {
    url: {
      type: "string",
      required: true,
      description: "The domain name of the page to retrieve"
    },
  },

  exits: {
    success: {
      description: "Page data retrieved successfully",
      statusCode: 200,
    },
    notFound: {
      description: "Page with the specified domain name was not found",
      statusCode: 404,
    },
    error: {
      description: "An error occurred while processing the request",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { url } = inputs;

      // Find the page by domain name
      const pageRecord = await Page.findOne({ domainName: url });

      if (!pageRecord) {
        return exits.notFound({
          message: "Page Not Found",
        });
      }

      // Find Social Media
      const socialMedia = await SocialMedia.findOne({ page: pageRecord.id });

      const logoURL = pageRecord.pageLogo ? await sails.helpers.getPhoto(pageRecord.pageLogo.file_id) : "";
      const bannerURL = pageRecord.banner ? await sails.helpers.getPhoto(pageRecord.banner.file_id) : "";

      return exits.success({
        ...pageRecord,
        pageLogo: logoURL,
        banner: bannerURL,
        socialMedia: socialMedia,
      });
    } catch (error) {
      sails.log.error('Error in get-page controller:', error);
      return exits.error({
        message: 'An error occurred while processing your request',
        error: error.message
      });
    }
  },
};
