"use client"
import { useParams } from "next/navigation"
import Trending from "@/components/Terminal/Trade/Trending";
import RecentlyViewed from "@/components/Terminal/Trade/Trending/RecentlyViewed";
import { useWeb3 } from "@/contexts/Web3Context";
import { useEffect } from "react";
import OpenPositions from "@/components/Terminal/Trade/OpenPosition";
import TokenPairData from "@/components/Terminal/Trade/TokenPairData";

export default function TradePage() {
    const { chain, address } = useParams();
    const { addRecentlyViewed } = useWeb3();

    useEffect(() => {
        // Only add if both are present and address is 0x-prefixed
        if (typeof address === 'string' && typeof chain === 'string' && address.startsWith('0x')) {
            addRecentlyViewed(address as `0x${string}`, chain);
        }
    }, [address, chain, addRecentlyViewed]);

    useEffect(() => {
        document.title = "📊 Live Trading Dashboard - Real-Time Crypto Trading | Anubis Terminal";
    }, []);

    return (
        <section className="overflow-hidden grid grid-cols-12 h-[calc(100vh-80px)]">
            <div className="col-span-2 border-r border-white/10 w-full overflow-x-hidden h-full grid grid-rows-3">
                <Trending />
                <RecentlyViewed />
                <OpenPositions />
            </div>
            <div className="col-span-8 h-full">
                {/* Main content area */}
                <TokenPairData />
            </div>
            <div className="col-span-2 border-l h-full flex flex-col bg-black/50 border border-white/10 rounded-md p-4 gap-4">
                {/* Buy/Sell Panel */}
                <div className="mb-4">
                    <div className="flex gap-2 mb-3">
                        <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors">Buy</button>
                        <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors">Sell</button>
                        <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-2 rounded-md hover:bg-white/10 transition-colors">Market</button>
                    </div>
                    <div className="mb-3">
                        <input className="w-full bg-black/40 border border-white/10 rounded-md px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-500 transition" placeholder="Total" />
                        <div className="flex gap-2 mt-2">
                            <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors">$50</button>
                            <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors">$100</button>
                            <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors">$500</button>
                            <button className="flex-1 font-orbitron font-semibold border border-white/10 bg-white/5 text-white py-1.5 rounded-md hover:bg-white/10 transition-colors">$1,000</button>
                        </div>
                    </div>
                    <div className="flex items-center gap-2 mb-3">
                        <input type="checkbox" className="accent-green-500" id="exit-strategy" />
                        <label htmlFor="exit-strategy" className="text-xs text-white/80">Exit Strategy</label>
                    </div>
                    <button className="w-full font-orbitron font-semibold bg-white text-black py-2 rounded-md shadow hover:shadow-white/20 transition-all text-base mt-2">Instant trade</button>
                </div>
                {/* Stats Panel */}
                <div className="border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40 mb-4">
                    <div className="flex justify-between mb-1">
                        <span>Gas</span>
                        <span>~0.00 ZK</span>
                    </div>
                    <div className="flex justify-between mb-1">
                        <span>Buy</span>
                        <span>--</span>
                    </div>
                    <div className="flex justify-between mb-1">
                        <span>Sell</span>
                        <span>--</span>
                    </div>
                    <div className="flex justify-between mb-1">
                        <span>Tips</span>
                        <span>0.000Ξ</span>
                    </div>
                </div>
                {/* Data & Security Warnings */}
                <div className="border border-white/10 rounded-md p-3 text-xs text-white/80 bg-black/40 flex-1 overflow-y-auto">
                    <div className="mb-2 flex items-center gap-2">
                        <span className="text-yellow-400 font-bold">Data & Security</span>
                        <span className="text-red-500">2 warnings</span>
                    </div>
                    <ul className="list-disc ml-5 space-y-1">
                        <li>Snipers: 0/0 (0.00%)</li>
                        <li>First buyers: 0/0 (0.00%)</li>
                        <li>Dev holding: --</li>
                        <li>Top 10 Holders: 93.34%</li>
                        <li>Can't Sell: --</li>
                    </ul>
                </div>
            </div>
        </section>
    )
}