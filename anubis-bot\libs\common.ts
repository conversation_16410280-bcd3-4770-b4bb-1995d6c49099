export function convertToUSD(numberString?: string | number): string {
  if (numberString === undefined || numberString === null || numberString === "null") {
    return "Not available";
  }

  const number = parseFloat((numberString ?? 0).toString());
  if (isNaN(number)) {
    return "Not available";
  }

  const usdFormat = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumSignificantDigits: 5,
  });
  return usdFormat.format(number);
}

export function convertToMaxDecimals(
  numberString: number,
  decimals: number = 4
): string {
  const format = new Intl.NumberFormat("en-US", {
    maximumFractionDigits: decimals,
  });
  return format.format(numberString);
}

export const ALL_TELEGRAM_MARKDOWN_REGEX = /[_*`\[\]()~>#+\-=|{}.!]/g;

export function escapeTelegramMarkdown(text: string): string {
  const reservedEntities = ["+", "-", "=", "|", "{", "}", ".", "!"];

  const openCloseEntities = ["_", "*", "`", "~"];

  let openParenthesis = false;

  const escapedText = text
    .split("")
    .map((char, index) => {
      if (reservedEntities.includes(char)) {
        return "\\" + char; // Escape reserved characters
      }

      if (
        openCloseEntities.includes(char) &&
        text.indexOf(char, index) === -1
      ) {
        return "\\" + char; // Escape open/close entities if they don't have a closing tag
      }

      // Handle specific entities with closing tags
      switch (char) {
        case "[":
          // Only escape if no closing "](url)"
          const hasClosingBracket =
            text.indexOf("](", index) !== -1 && text.indexOf(")", index) !== -1;
          return hasClosingBracket ? char : "\\" + char;
        case "(":
          // Only escape if no closing "](url)"
          const hasClosingParenthesis =
            text.at(index - 1) === "]" && text.indexOf(")", index) !== -1;
          openParenthesis = hasClosingParenthesis;
          return hasClosingParenthesis ? char : "\\" + char;
        case ")":
          const ret = openParenthesis ? char : "\\" + char;
          openParenthesis = false;
          return ret;
        default:
          return char;
      }
    })
    .join("");

  return escapedText;
}
