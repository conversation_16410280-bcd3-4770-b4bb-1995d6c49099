import axios from 'axios';

const baseURL = `https://pro-api.coinmarketcap.com/v2/cryptocurrency/info`;

export const getTokenDataCMC = async ({ address, symbol }: { address: string, symbol: string }) => {
    const queries = `symbol=${symbol}&address=${address}`;

    try {
        const response = await axios.get(`${baseURL}?${queries}`, {
            headers: {
                "X-CMC_PRO_API_KEY": process.env.NEXT_PUBLIC_CMC_API_KEY,
            },
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching token data:", error);
        throw error;
    }
}