module.exports = async function (req, res, proceed) {
  // Allow OPTIONS requests (CORS preflight) to pass through
  if (req.method === "OPTIONS") {
    return proceed();
  }

  if (req.header("authorization")) {
    const token = req.header("authorization").split("Bearer ")[1];

    if (
      !token ||
      token === "undefined" ||
      token !== process.env.ANUBIS_BEARER_TOKEN
    ) {
      return res.status(401).json({
        error: "Unauthorized",
        message: "Invalid Token Provided",
      });
    }

    return proceed();
  }

  return res.status(401).json({
    status: "Unauthorized",
    message: "Unauthorized Access from client side",
  });
};
