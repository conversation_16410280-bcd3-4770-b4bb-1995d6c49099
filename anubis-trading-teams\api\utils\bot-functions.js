const {
  getUser,
  createNewUser,
  createNewGroup,
  getGroup,
  getGroups,
} = require("./user-management");
const {
  mainMenu,
  page_update_msg,
  socialOptions,
  socialMediaList,
  service_configuration_msg,
  buyUI,
} = require("./message-structure");
const {
  extractContentCreatorId,
  extractCustomPageId,
  extractIdAndAction,
  infoParseQuery,
  infoParseText,
  isSocialCommand,
} = require("./lib");
const { updateTheme } = require("./pages/theme");
const { setLogo, setBannerImage } = require("./pages/image");
const { handleColorUpdate } = require("./pages/color");
const {
  weservProxy,
  getTokenData,
  isValidAddress,
  isValidChain,
  formatTokenData,
  formatDeepDecimal,
  dexscreenerChains,
} = require("./token/dexscreener");
const tradingBotUsername = sails.config.custom.tradingBotUsername;
const botUsername = sails.config.custom.botusername;
const terminalUrl = sails.config.custom.terminalUrl;

async function processInit(ctx) {
  if (ctx.command === "start" && ctx.payload && ctx.payload.includes("set")) {
    const { id: userId } = ctx.update.message.from.chat;

    const { id: pageId, action } = extractIdAndAction(ctx.payload);

    sails.log.debug(pageId, action);

    const pageRecord = await Page.findOne({ id: pageId });

    if (!pageRecord) {
      return;
    }

    const group = await Group.findOne({ id: pageRecord.group });
    const user = await getUser(userId);

    if (user && group.owner !== user.id) {
      await sails.helpers.sendMessage(
        userId,
        "❌ You are not authorized to perform this action."
      );
      return;
    }

    // Update User Record
    await User.updateOne({ chatId: userId }).set({
      mode: action,
      currentConfiguredGroup: group.groupId,
      currentConfiguredService: pageId,
    });

    if (action === "set_display_name") {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `What's Your New Display Name?\nThis name will be displayed at the top of your page\n<i>Type cancel to cancel</i>`
      );

      return;
    }

    if (action === "set_page_logo") {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `🖼 Please upload your logo.\nType 'cancel' to stop this process.\nTip: We recommend uploading your logo in a 1:1 format for better results.`
      );
    }
  }

  // After Bot is Added to Group
  if (ctx.command === "start" && ctx.payload && !ctx.payload.includes("set")) {
    if (ctx.payload.includes("configure_")) {
      const split = ctx.payload.split("_");
      const groupId = split[1];

      const group = await getGroup(groupId);
      if (!group) {
        sails.log.debug("Failed to locate group");
        return;
      }

      const user = await getUser(group.groupOwnerId);
      if (!user) {
        sails.log.debug("Failed to locate user");
        return;
      }

      if (user.chatId !== ctx.update.message.from.id) {
        await sails.helpers.sendMessage(
          ctx.update.message.from.id,
          "You are not authorized to perform this action."
        );

        return;
      }

      const pageRecords = await Page.find({ group: group.id });
      if (pageRecords.length > 0) {
        const mappedButton = pageRecords.map((page) => {
          return [
            {
              text: `${page.displayName} | ${group.groupTitle}`,
              callback_data: `select_community_${page.id}`,
            },
          ];
        });

        await sails.helpers.sendMessage(
          group.groupOwnerId,
          `Select a Community to Configure`,
          {
            inline_keyboard: [
              ...mappedButton,
              [
                {
                  text: "Create New Service",
                  callback_data: `start_configuration_${group.groupId}`,
                },
              ],
              [
                {
                  text: "➕ Add the Anubis Teams Bot to a new Group",
                  url: `https://t.me/${botUsername}?startgroup=${user.id}`,
                },
              ],
              [
                {
                  text: "Need help? Click here",
                  url: "https://anubistrade.xyz/help",
                },
              ],
              [
                {
                  text: "Go to Trading Bot",
                  url: `https://t.me/${tradingBotUsername}`,
                },
              ],
            ],
          }
        );
      } else {
        sails.log.debug("No pages found for group", group.id);
        const service_buttons = service_configuration_msg(
          group.groupId,
          user.chatId
        );

        await sails.helpers.sendMessage(
          group.groupOwnerId,
          `You don't have any currently existing services to configure yet! 🤕\n\nIf you'd like to add a buy token feature or create your own verified links page\n\nClick the button below to get started.\n\n`,
          service_buttons
        );

        return;
      }

      return;
    }

    const { id } = ctx.update.message.from; // User ID
    // Confirm User Account Exists on Bot DB
    const user = await getUser(id);
    if (!user) {
      await sails.helpers.sendMessage(
        id,
        "Oops! There was an issue with your profile, please try again later"
      );

      return;
    }

    const { id: groupId } = ctx.update.message.chat;

    let group;
    group = await Group.findOne({ groupId }); // Check for Existing Group
    if (group) {
      const pageRecords = await Page.find({ group: group.id });
      if (pageRecords.length > 0) {
        const mappedButton = pageRecords.map((page) => {
          return [
            {
              text: `${page.displayName} | ${group.groupTitle}`,
              callback_data: `select_community_${page.id}`,
            },
          ];
        });

        await sails.helpers.sendMessage(
          group.groupOwnerId,
          `Select a Community to Configure`,
          {
            inline_keyboard: [
              ...mappedButton,
              [
                {
                  text: "➕ Add the Anubis Teams Bot to a new Group",
                  url: `https://t.me/${botUsername}?startgroup=${user.id}`,
                },
              ],
              [
                {
                  text: "Need help? Click here",
                  url: "https://anubistrade.xyz/help",
                },
              ],
              [
                {
                  text: "Go to Trading Bot",
                  url: `https://t.me/${tradingBotUsername}`,
                },
              ],
            ],
          }
        );

        return;
      }
    } else {
      // Create new group

      const newGroup = await createNewGroup(
        user.id,
        ctx.update.message.from.id,
        ctx.update.message.chat.id,
        ctx.update.message.chat.title
      );

      const pageRecord = await Page.find({ group: newGroup.id });

      if (pageRecord.length > 0) {
        const mappedButton = pageRecord.map((page) => {
          return {
            text: `${page.displayName} | ${newGroup.groupTitle}`,
            callback_data: `select_community_${page.id}`,
          };
        });

        await sails.helpers.sendMessage(
          newGroup.groupOwnerId,
          `Select a Community to Configure`,
          {
            inline_keyboard: [
              [...mappedButton],
              [
                {
                  text: "➕ Add the Anubis Teams Bot to a new Group",
                  url: `https://t.me/${botUsername}?startgroup=${user.id}`,
                },
              ],
              [
                {
                  text: "Need help? Click here",
                  url: "https://anubistrade.xyz/help",
                },
              ],
              [
                {
                  text: "Go to Trading Bot",
                  url: `https://t.me/${tradingBotUsername}`,
                },
              ],
            ],
          }
        );

        return;
      }

      await sails.helpers.sendMessage(
        id,
        `Setup Anubis for ${newGroup.groupTitle}! 👋`,
        {
          inline_keyboard: [
            [
              {
                text: "Start Configuration",
                callback_data: `start_configuration_${groupId}`,
              },
            ],
          ],
        }
      );
    }

    return;
  }

  if (ctx.update.message.text === "/start") {
    sails.log.debug("Context", ctx.update.message);
    // Get User
    let userRecord = await getUser(ctx.update.message.from.id);
    sails.log.debug("User Record", userRecord);

    // If User Doesn't exist : Create New User
    if (!userRecord) {
      const { id, first_name, username } = ctx.update.message.from;
      const newUserRecord = await createNewUser(id, username, first_name);
      sails.log.debug("New User Record", newUserRecord);

      // Check if user creation failed
      if (!newUserRecord) {
        sails.log.error("Failed to create new user for ID:", id);
        await sails.helpers.sendMessage(
          id,
          "❌ Sorry, there was an error setting up your account. Please try again later or contact support."
        );
        return;
      }

      const menuBtns = await mainMenu(newUserRecord.id);

      await sails.helpers.sendMessage(
        id,
        `Welcome to Anubis Teams Bot\n1. Make sure the Anubis Bot is added to your group where it will be working.\n2. Make sure the Anubis Teams Bot is set as admin, You must also be an admin to complete the next step.\n3. If you have completed the above, type /setup within your group to get started.`,
        menuBtns
      );

      return;
    }
    const menuBtns = await mainMenu(userRecord.id);
    await sails.helpers.sendMessage(
      userRecord.chatId,
      `Welcome to Anubis Teams Bot\n1. Make sure the Anubis Bot is added to your group where it will be working.\n2. Make sure the Anubis Teams Bot is set as admin, You must also be an admin to complete the next step.\n3. If you have completed the above, type /setup within your group to get started.`,
      menuBtns
    );

    return;
  }
}

async function setupGroup(ctx) {
  if (ctx.update.message.text === "/setup@anubis_teams_bot") {
    sails.log.debug("Group Update", ctx.update);
    // Get User
    let user;
    const userRecord = await getUser(ctx.update.message.from.id);
    user = userRecord;

    // If No User : Create new User
    if (!user) {
      return;
    }

    const groupId = ctx.update.message.chat.id;
    const group = await Group.findOne({
      groupId,
      groupOwnerId: ctx.update.message.from.id,
    });

    // If Group doesn't exist
    if (!group) {
      return;
    }

    await createNewGroup(
      user.id,
      ctx.update.message.from.id,
      ctx.update.message.chat.id,
      ctx.update.message.chat.title
    );

    const { chat_id, message_id } = await sails.helpers.sendMessage(
      ctx.update.message.chat.id,
      `Hello!\nAnubistrade.xyz has been successfully added to the group!\n\nClick the button below to open group bot settings (Admins only)`,
      {
        inline_keyboard: [
          [
            {
              text: "Click Me",
              url: `https://t.me/anubis_teams_bot?start=configure_${ctx.update.message.chat.id}`,
            },
          ],
        ],
      }
    );

    setTimeout(async () => {
      await sails.helpers.deleteMessage(chat_id, message_id);
    }, 120000);

    return;
  }

  if (ctx.update.message.text === "/setup") {
    // Get User
    let user;
    const userRecord = await getUser(ctx.update.message.from.id);
    user = userRecord;
    const menuBtns = await mainMenu(user.id);

    if (!user) {
      return;
    }

    await sails.helpers.sendMessage(
      ctx.update.message.chat.id,
      `Welcome to Anubis Teams Bot\n1. Make sure the Anubis Bot is added to your group where it will be working.\n2. Make sure the Anubis Teams Bot is set as admin, You must also be an admin to complete the next step.\n3. If you have completed the above, type /setup within your group to get started.`,
      menuBtns
    );

    return;
  }
}

async function handleCallBackQuery(ctx) {
  sails.log.debug("Handle CallBack Query Fired 🔥");
  const { command, id, username } = infoParseQuery(ctx);
  sails.log.debug(
    `[Display Name Update] Command: ${command}, User ID: ${id}, Username: ${username}`
  );
  const user = await getUser(id);

  const social = isSocialCommand(command);

  if (social) {
    const { type, id } = social;

    // Find Social Media Record
    const sm = await SocialMedia.findOne({ id });

    // Send Error Message
    if (!sm) {
      await sails.helpers.sendMessage(
        user.chatId,
        "Sorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    await User.updateOne({ id: user.id }).set({
      mode: "set_page_social_media",
      currentConfiguredGroup: sm.id,
      currentConfiguredService: type,
    });

    // Tell User to Submit URL
    await sails.helpers.sendMessage(
      user.chatId,
      `Please submit the URL for ${
        type.charAt(0).toUpperCase() + type.slice(1)
      }\ntype cancel to cancel\n\nClick the button below to go back`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `load_page_${sm.page}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (command.includes("set_page_favorite_tokens")) {
    const pageId = command.split("_")[4];

    // Find Page Record
    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        user.chatId,
        "Sorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    if (
      page.favoriteTokens.length > 0 &&
      page.favoriteTokens[0].contractAddress
    ) {
      const mappedTokens = page.favoriteTokens.map((token) => {
        return [
          {
            text: `${token.symbol} | ${token.contractAddress}`,
            callback_data: `rm_token_${page.id}_${token.symbol}`,
          },
        ];
      });

      await sails.helpers.sendMessage(
        user.chatId,
        `Please select the token you'd like to remove from your favorite tokens`,
        {
          inline_keyboard: [
            ...mappedTokens,
            [
              {
                text: "➕ Add Token",
                callback_data: `add_token_${page.id}`,
              },
            ],
            [
              {
                text: "Back",
                callback_data: `load_page_${page.id}`,
              },
            ],
          ],
        }
      );

      return;
    }

    await User.updateOne({ id: user.id }).set({
      mode: "set_page_favorite_tokens_chain",
      currentConfiguredGroup: page.id,
    });

    await sails.helpers.sendMessage(
      user.chatId,
      `Please submit the chain of the token you'd like to add to your favorite tokens\ntype cancel to cancel\n\nClick the button below to go back`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `load_page_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (command.includes("rm_token_")) {
    const pageId = command.split("_")[2];
    const symbol = command.split("_")[3];
    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        user.chatId,
        "Could not find page\nSorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    const token = page.favoriteTokens.find((token) => token.symbol === symbol);

    if (!token) {
      await sails.helpers.sendMessage(
        user.chatId,
        "Could not find token\nSorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    await Page.updateOne({ id: page.id }).set({
      favoriteTokens: page.favoriteTokens.filter(
        (token) => token.symbol !== symbol
      ),
    });

    await User.updateOne({ id: user.id }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    const keyboard = page_update_msg(page.id, page.pageTheme);

    await sails.helpers.sendMessage(
      user.chatId,
      `✅ Token <code>${symbol}</code> has been removed from your favorite tokens`,
      keyboard
    );

    return;
  }

  if (command.includes("add_token")) {
    const pageId = command.split("_")[2];
    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        user.chatId,
        "Could not find page\nSorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    await User.updateOne({ id: user.id }).set({
      mode: "set_page_favorite_tokens_chain",
      currentConfiguredGroup: page.id,
    });

    await sails.helpers.sendMessage(
      user.chatId,
      `Please submit the chain of the token you'd like to add to your favorite tokens\ntype cancel to cancel\n\nClick the button below to go back`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `load_page_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (command.includes("set_page_token_")) {
    const pageId = command.split("_")[3];
    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        user.chatId,
        "Could not find page\nSorry something went wrong 🤕. Please try again later.",
        {
          inline_keyboard: [
            [
              {
                text: "Back",
                callback_data: `load_page_${page.id}`,
              },
            ],
          ],
        }
      );

      return;
    }

    if (page.tokenData.contractAddress) {
      await sails.helpers.sendMessage(
        user.chatId,
        `You already have a community token set. If you add a new one, the existing one will be replaced. Token <code>${page.tokenData.symbol}</code>`,
        {
          inline_keyboard: [
            [
              {
                text: "Back",
                callback_data: `load_page_${page.id}`,
              },
            ],
          ],
        }
      );
    }

    await User.updateOne({ id: user.id }).set({
      mode: "set_page_token_chain",
      currentConfiguredGroup: page.id,
    });

    await sails.helpers.sendMessage(
      user.chatId,
      `Please submit the chain of the token you'd like to set as your community token\ntype cancel to cancel\n\nClick the button below to go back`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `load_page_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (ctx.update.callback_query.data.includes("start_configuration")) {
    // If User Clicks on Start Configuration
    sails.log.debug(ctx);
    const { id: userId } = ctx.update.callback_query.from;
    const split = command.split("_");
    const groupId = split[2];
    const group = await Group.findOne({ groupId: groupId });

    if (!group) {
      sails.log.debug(`Failed to Find Group with id ${groupId}`);
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Sorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    if (group.groupOwnerId !== userId) {
      sails.log.debug(`User ${userId} is not the owner of group ${groupId}`);
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Sorry something went wrong 🤕. Please try again later."
      );
      return;
    }

    const keyboard = service_configuration_msg(groupId, userId);

    await sails.helpers.sendMessage(
      group.groupOwnerId,
      `Here's what Anubis Teams can do:\nCheck out our website: https://anubistrade.xyz\n\n🔗Create & Manage your own Verified Links Page\n- List Your Token\n- Claim Your Web3 LinkTree Page and so much more`,
      keyboard
    );

    return;
  }

  // If User Clicks on Create New Service
  if (ctx.update.callback_query.data.includes("create_new_service")) {
    sails.log.debug(ctx);
    const { id: userId } = ctx.update.callback_query.from;
    const split = command.split("_");
    const groupId = split[2];
    const group = await Group.findOne({ groupId: groupId });

    if (!group) {
      sails.log.debug(`Failed to Find Group with id ${groupId}`);
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Sorry something went wrong 🤕. Please try again later."
      );

      return;
    }

    if (group.groupOwnerId !== userId) {
      sails.log.debug(`User ${userId} is not the owner of group ${groupId}`);
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Sorry something went wrong 🤕. Please try again later."
      );
      return;
    }

    const keyboard = service_configuration_msg(groupId, userId);

    await sails.helpers.sendMessage(
      group.groupOwnerId,
      `Here's what Anubis Teams can do:
Check out our website: https://anubistrade.xyz

🔗Create & Manage your own Verified Links Page
- List Your Token
- Claim Your Web3 LinkTree Page and so much more`,
      keyboard
    );

    return;
  }

  // If User Replies with Confirm Contract
  if (
    command.includes("confirm_contract") &&
    user.mode === "confirm_buy_token_contract"
  ) {
    const split = command.split("_");
    const contractAddress = split[2];

    if (!isValidAddress(contractAddress)) {
      await sails.helpers.sendMessage(
        user.chatId,
        "❌ Invalid Address. Please enter a valid address.\nType cancel to cancel."
      );

      return;
    }

    // Get Current Configured Group From User
    const group = await Group.findOne({ id: user.currentConfiguredGroup });

    if (!group) {
      await sails.helpers.sendMessage(
        user.chatId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`
      );
      return;
    }

    // Get Temp Stored Contract Address from User
    const tempContractAddress = user.currentConfiguredService;

    if (!tempContractAddress) {
      await sails.helpers.sendMessage(
        user.chatId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`
      );
      return;
    }

    // Make API call to Dex Screener to get Address, Chain & Symbol
    const dexData = await getTokenData(contractAddress, group.buyToken.chain);

    if (!dexData || dexData.length === 0) {
      await sails.helpers.sendMessage(
        user.chatId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`
      );
      return;
    }

    // Update Group Buy Token
    await Group.updateOne({
      id: group.id,
    }).set({
      buyToken: {
        contractAddress: contractAddress,
        symbol:
          dexData[0] && dexData[0].baseToken && dexData[0].baseToken.symbol,
        chain: group.buyToken.chain,
        name: dexData[0] && dexData[0].baseToken && dexData[0].baseToken.name,
        logo: dexData[0] && dexData[0].info && dexData[0].info.imageUrl,
        websites:
          dexData[0] && dexData[0].baseToken && dexData[0].baseToken.websites,
        socials:
          dexData[0] && dexData[0].baseToken && dexData[0].baseToken.socials,
      },
    });

    // Clear Conversation
    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    await sails.helpers.sendMessage(
      user.chatId,
      `✅ Buy Token has been set.\nToken Name: ${dexData[0].baseToken.name}\nToken Symbol: ${dexData[0].baseToken.symbol}\nToken Chain: ${group.buyToken.chain}\nToken Address: ${contractAddress}`,
      {
        inline_keyboard: [
          [
            {
              text: "View Token on DexScreener",
              url: dexData[0].url,
            },
          ],
        ],
      }
    );

    return;
  }

  if (
    ctx.update.callback_query.data.includes("get_content_creator") ||
    ctx.update.callback_query.data.includes("get_custom_page")
  ) {
    sails.log.debug("Handle Get Content Creator or Custom Page Fired 🔥");
    sails.log.debug("Query Data", ctx.update.callback_query.data);
    const id =
      extractContentCreatorId(ctx.update.callback_query.data) ||
      extractCustomPageId(ctx.update.callback_query.data); // Extracts the Group ID from the callback query data
    const currentConfiguredService = ctx.update.callback_query.data.includes(
      "get_content_creator"
    )
      ? "content_creator"
      : "token_page";

    const group = await getGroup(id);

    if (!group) {
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Anubis can't seem to locate your Group. Please try again later.\nClick /start to get started."
      );
      return;
    }

    const user = await getUser(group.groupOwnerId);

    if (!user) {
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Anubis can't seem to locate your Group. Please try again later.\nClick /start to get started."
      );
      return;
    }

    // Check if Group already has an existing page
    const page = await Page.findOne({ group: group.id });

    if (page) {
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "This group already has a page.\n\nWould you like to configure it?",
        {
          inline_keyboard: [
            [
              {
                text: "Configure Existing Page",
                callback_data: `select_community_${page.id}`,
              },
            ],
            [
              {
                text: "Back",
                callback_data: `get_groups_${user.id}`,
              },
            ],
          ],
        }
      );

      return;
    }

    const newPage = await Page.create({
      group: group.id,
      owner: user.id,
      displayName: `${group.groupTitle} ${
        currentConfiguredService === "content_creator"
          ? "Community"
          : "Token Page"
      }`,
      pageType: currentConfiguredService,
    }).fetch();

    const keyboard = page_update_msg(
      newPage.id,
      newPage.pageTheme,
      newPage.pageType
    );

    await sails.helpers.sendMessage(
      user.chatId,
      `✅ Successfully created ${
        currentConfiguredService === "content_creator"
          ? "Content Creator"
          : "Token"
      } page for ${group.groupTitle}!\n\nCustomize your page below:`,
      keyboard
    );

    return;
  }

  if (
    ctx.update.callback_query.data.includes("select_group") &&
    user.mode !== "set_buy_token"
  ) {
    const user = await getUser(id); // Get User
    // Grab Group Record ID from data
    const groupRecordId = ctx.update.callback_query.data.split("_")[2];
    const group = await Group.findOne({ id: groupRecordId, owner: user.id });

    if (!group) {
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "Sorry something went wrong 🤕. Please try again later."
      );
      return;
    }

    // Check if a page already exists for this group with the service type
    const existingPage = await Page.findOne({
      group: group.id,
      pageType: user.currentServiceType,
    });

    if (existingPage) {
      await sails.helpers.sendMessage(
        user.chatId,
        `A ${
          user.currentServiceType === "content_creator"
            ? "Content Creator"
            : "Token"
        } page already exists for this group. Would you like to configure it?`,
        {
          inline_keyboard: [
            [
              {
                text: "Configure Existing Page",
                callback_data: `select_community_${existingPage.id}`,
              },
            ],
            [
              {
                text: "Back to Group Selection",
                callback_data: `get_${user.currentServiceType}_${user.id}`,
              },
            ],
          ],
        }
      );
      return;
    }

    // Create new page with the selected service type
    const newPage = await Page.create({
      group: group.id,
      displayName: `${group.groupTitle} ${
        user.currentServiceType === "content_creator"
          ? "Community"
          : "Token Page"
      }`,
      pageType: user.currentServiceType,
    }).fetch();

    // Reset user state
    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
      currentServiceType: "",
    });

    const keyboard = page_update_msg(
      newPage.id,
      newPage.pageTheme,
      newPage.pageType
    );

    await sails.helpers.sendMessage(
      user.chatId,
      `✅ Successfully created ${
        user.currentServiceType === "content_creator"
          ? "Content Creator"
          : "Token"
      } page for ${group.groupTitle}!\n\nCustomize your page below:`,
      keyboard
    );

    return;
  }

  if (command.includes("select_community")) {
    const communityId = command.split("_")[2]; // Page ID

    const user = await getUser(ctx.update.callback_query.from.id);

    // Find Page
    const page = await Page.findOne({ id: communityId });

    if (!page) {
      return;
    }

    // Filter through all groups to find the group that matches the page
    const group = await Group.findOne({ id: page.group });

    if (!group) {
      return;
    }

    const keyboard = page_update_msg(page.id, page.pageTheme, page.pageType);

    await sails.helpers.sendMessage(
      user.chatId,
      `Hello @${user.username}\nCustomize ${page.displayName || "Community"}`,
      keyboard
    );

    return;
  }

  if (command.includes("load_page")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];

    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, "Server Process Error ❌");

      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    const ui = page_update_msg(page.id, page.pageTheme, page.pageType);
    const user = await User.findOne({ chatId });

    await sails.helpers.sendMessage(
      chatId,
      `Hello @${user.username}\nCustomize ${page.displayName || "Community"}`,
      ui
    );

    return;
  }

  if (command.includes("set_display_name")) {
    // Update Record
    const chatId = id;
    const split = command.split("_");
    const pageId = split[split.length - 1]; // Page Record Id

    if (!pageId) {
      await sails.helpers.sendMessage(
        chatId,
        `System Error ❌\n\nPlease report this to @sunami_trade_support`
      );

      return;
    }
    // Find Page Record
    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_display_name",
      currentConfiguredGroup: page.group,
      currentConfiguredService: pageId,
    });

    await sails.helpers.sendMessage(
      chatId,
      `${
        page.displayName ? `${page.displayName}\n` : "Community"
      }Please provide a display name for your page. This will be shown to visitors and help them identify your community.\n\nExample: 'My Awesome Community'\n\nType 'cancel' to abort this operation.`
    );

    return;
  }

  if (command.includes("set_page_bio")) {
    const chatId = id;
    const split = command.split("_");
    const pageId = split[split.length - 1]; // Page Record Id

    if (!pageId) {
      await sails.helpers.sendMessage(
        chatId,
        `System Error ❌\n\nPlease report this to @sunami_trade_support`
      );

      return;
    }

    // Find Page Record
    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_page_bio",
      currentConfiguredGroup: page.group,
      currentConfiguredService: pageId,
    });

    await sails.helpers.sendMessage(
      chatId,
      `${
        page.pageBio ? `<b>Current Value</b>: ${page.pageBio}\n` : ""
      }Please provide a bio for your page. This will help visitors learn more about your community or project.\n\nExample: 'We are a passionate community focused on blockchain education and innovation.'\n\nType 'cancel' to abort this operation.`
    );

    return;
  }

  if (command.includes("set_page_theme")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];
    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, `Server Process Error ❌`);

      return;
    }

    try {
      await updateTheme(pageId, ctx);
    } catch (error) {
      sails.log.error(error);
    }
  }

  if (command.includes("set_page_logo")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];

    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, `Server Process Error ❌`);

      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_page_logo",
      currentConfiguredGroup: page.group,
      currentConfiguredService: pageId,
    });

    if (page.pageLogo && page.pageLogo.file_id) {
      await sails.helpers.sendPhoto(
        chatId,
        `This is the current hub logo. If you want to change it, please either: \n• Reply with the logo you wish to use \n• Type 'cancel' to cancel \n• Type 'delete' to delete current hub logo`,
        page.pageLogo.file_id
      );

      return;
    }

    await sails.helpers.sendMessage(
      chatId,
      `🖼 Please upload your logo.\nType 'cancel' to stop this process.\nTip: We recommend uploading your logo in a 1:1 format for better results.`
    );

    return;
  }

  if (command.includes("set_page_banner")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];

    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, `Server Process Error ❌`);

      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_page_banner",
      currentConfiguredGroup: page.group,
      currentConfiguredService: pageId,
    });

    if (page.banner && page.banner.file_id) {
      await sails.helpers.sendPhoto(
        chatId,
        `This is the current hub banner. If you want to change it, please either: \n• Reply with the banner you wish to use \n• Type 'cancel' to cancel \n• Type 'delete' to delete current hub banner`,
        page.banner.file_id
      );

      return;
    }

    await sails.helpers.sendMessage(
      chatId,
      `🖼 Update your current banner\n<i>Upload a new banner or type 'delete' or 'cancel'</i>`
    );

    return;
  }

  if (command.includes("set_page_color")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];

    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, `Server Process Error ❌`);

      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_page_color",
      currentConfiguredGroup: page.group,
      currentConfiguredService: pageId,
    });

    await sails.helpers.sendMessage(
      chatId,
      `🌈 Edit page accent color\nUse hexadecimal values, e.g. #5E38F4 or type ‘cancel’ or ‘delete’${
        page.pageColor ? `\n\nCurrent Value: ${page.pageColor}` : ""
      }`
    );

    return;
  }

  if (command.includes("set_page_social_media")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];

    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, "Server Process Error ❌");

      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    const socialOptionsUI = socialOptions(page.id);

    await sails.helpers.sendMessage(
      chatId,
      `Create, edit, delete and re-order your links.\nPlease select which type you want to edit.`,
      socialOptionsUI
    );

    return;
  }

  if (command.includes("add_buy_token")) {
    const id = ctx.update.callback_query.data.split("_")[3]; // Extract Group ID
    const group = await Group.findOne({ groupId: id }); // Get Group
    const user = await User.findOne({
      chatId: ctx.update.callback_query.from.id,
    }); // Get User

    const ui = mainMenu(user.id);

    if (!group) {
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "You don't have a group. Please add the Anubis Teams Bot to a group first.",
        ui
      );
      return;
    }

    // If Group Buy Token Data is already set, Warn User that this action will override existing data
    if (group.buyToken.contractAddress) {
      await sails.helpers.sendMessage(
        ctx.update.callback_query.from.id,
        "This group already has a buy token set.\n\nProceed to override existing data?"
      );
    }

    // const groupList = groups.map((group) => {
    //   return {
    //     text: group.groupTitle,
    //     callback_data: `select_group_${group.id}`,
    //   };
    // });

    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "set_buy_token",
    });

    await sails.helpers.sendMessage(
      ctx.update.callback_query.from.id,
      `You are adding a buy token to ${group.groupTitle}`,
      {
        inline_keyboard: [
          [
            {
              text: "Confirm",
              callback_data: `select_group_${group.id}`,
            },
          ],
          [
            {
              text: "Back",
              callback_data: `start_configuration_${group.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (command.includes("select_group") && user.mode === "set_buy_token") {
    const split = command.split("_");
    const groupId = split[split.length - 1];
    const { id: chatId } = infoParseQuery(ctx);

    // Check if Group Exists
    const group = await Group.findOne({ id: groupId });

    if (!group) {
      await sails.helpers.sendMessage(
        chatId,
        "Server Process Failed!❌\n\nPlease report this to @sunami_trade_support."
      );
      return;
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_buy_token_chain",
      currentConfiguredGroup: group.id,
    });

    // Send UX centered message telling user to type the name of chain
    await sails.helpers.sendMessage(
      chatId,
      "Type the name of chain\n\nExample: Ethereum, BSC, Polygon\n\nType 'cancel' to abort this operation.",
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `add_buy_token_${id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (command.includes("set_custom_url_link")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];
    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, "Server Process Error ❌");
      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        chatId,
        "Server Process Failed!❌\n\nPlease report this to @sunami_trade_support."
      );
      return;
    }

    // If Domain is already set
    if (page.domainName) {
      await sails.helpers.sendMessage(
        chatId,
        `You already have a custom URL set.\n\nCurrent URL: ${page.domainName}\n\nProceed with caution as this will override your current custom URL.\n\nType 'cancel' to abort this operation.`
      );
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "set_custom_url_link",
      currentConfiguredGroup: page.group,
      currentConfiguredService: pageId,
    });

    await sails.helpers.sendMessage(
      chatId,
      `🔗 Set Custom URL Link\n${
        page.domainName ? `Current: ${page.domainName}\n` : ""
      }\nPlease provide a custom URL link\n\nExample: anubistrade.xyz/yourcustompage\n\nType 'cancel' to abort this operation.`
    );

    return;
  }

  if (command.includes("get_social_links")) {
    const split = command.split("_");
    const pageId = split[split.length - 1];

    const { id: chatId } = infoParseQuery(ctx);

    if (!pageId) {
      await sails.helpers.sendMessage(chatId, "Server Process Error ❌");

      return;
    }

    const page = await Page.findOne({ id: pageId });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    const existingSMRecord = await SocialMedia.findOne({ page: page.id });
    if (existingSMRecord) {
      const ui = socialMediaList(existingSMRecord);

      await sails.helpers.sendMessage(
        chatId,
        `🔗 Customize your socials.\n<i>📱 All settings Auto-Applied to your App</i>`,
        ui
      );

      return;
    }

    const sm = await SocialMedia.create({
      page: page.id,
    }).fetch();

    const ui = socialMediaList(sm);

    await sails.helpers.sendMessage(
      chatId,
      `🔗 Customize your socials.\n<i>📱 All settings Auto-Applied to your App</i>`,
      ui
    );

    return;
  }

  if (command.includes("get_groups") || command.includes("get_pages")) {
    const split = command.split("_");
    const id = split[split.length - 1];
    const entity = split[split.length - 2];

    if (entity === "groups") {
      const groups = await Group.find({ owner: id });

      if (groups.length === 0) {
        const menuBtns = await mainMenu(id);

        await sails.helpers.sendMessage(
          id,
          `You don't have any groups yet! 🤕\n\nIf you'd like to add a buy token feature or create your own verified links page\n\nClick the button below to get started.`,
          menuBtns
        );

        return;
      }

      const mappedButton = groups.map((group) => {
        return [
          {
            text: group.groupTitle,
            callback_data: `start_configuration_${group.id}`,
          },
        ];
      });

      await sails.helpers.sendMessage(id, `Select a Group to Configure`, {
        inline_keyboard: [
          ...mappedButton,
          [
            {
              text: "Back",
              callback_data: `get_groups_${id}`,
            },
          ],
        ],
      });

      return;
    }

    if (entity === "pages") {
      const pages = await Page.find({ owner: id });

      if (pages.length === 0) {
        const menuBtns = await mainMenu(id);

        await sails.helpers.sendMessage(
          id,
          `You don't have any groups/pages yet! 🤕\n\nIf you'd like to add a buy token feature or create your own verified links page\n\nClick the button below to get started.`,
          menuBtns
        );

        return;
      }
      const mappedButton = pages.map((page) => {
        return [
          {
            text: page.displayName,
            callback_data: `select_community_${page.id}`,
          },
        ];
      });

      await sails.helpers.sendMessage(id, `Select a Page to Configure`, {
        inline_keyboard: [
          ...mappedButton,
          [
            {
              text: "Back",
              callback_data: `get_pages_${id}`,
            },
          ],
        ],
      });

      return;
    }
  }
}

async function handleTextMessage(ctx) {
  const user = await getUser(ctx.update.message.from.id);
  const { command, id: chatId, groupMember, type } = infoParseText(ctx);

  if (user.mode === "set_page_favorite_tokens_chain") {
    const page = await Page.findOne({ id: user.currentConfiguredGroup });

    if (!page) {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Sorry something went wrong 🤕. Please try again later.`,
        {
          inline_keyboard: [
            [
              {
                text: "Back",
                callback_data: `load_page_${page.id}`,
              },
            ],
          ],
        }
      );

      return;
    }

    const chain = ctx.update.message.text;

    if (!isValidChain(chain)) {
      const chains = dexscreenerChains.join(", ");
      await sails.helpers.sendMessage(
        chatId,
        `❌ Invalid Chain!\n\nPlease try again.\n\nType cancel to abort this operation. \n\nValid chains: ${chains}`
      );

      return;
    }

    await User.updateOne({ id: user.id }).set({
      mode: "set_page_favorite_tokens_address",
      currentConfiguredGroup: chain,
      currentConfiguredService: page.id,
      currentServiceType: page.pageType,
    });

    await sails.helpers.sendMessage(
      chatId,
      `Please submit the address of the token you'd like to add to your favorite tokens\ntype cancel to cancel\n\nClick the button below to go back`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `set_page_favorite_tokens_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (user.mode === "set_page_token_chain") {
    const page = await Page.findOne({ id: user.currentConfiguredGroup });

    if (!page) {
      // await User.updateOne({ id: user.id }).set({
      //   mode: "conversation",
      //   currentConfiguredGroup: "",
      //   currentConfiguredService: "",
      //   currentServiceType: "",
      // });

      await sails.helpers.sendMessage(
        chatId,
        `Please select a chain\nSorry something went wrong 🤕. Please try again.\n\nClick the button below to go back\ntype cancel to abort this operation`,
        {
          inline_keyboard: [
            [
              {
                text: "Back",
                callback_data: `load_page_${page.id}`,
              },
            ],
          ],
        }
      );

      return;
    }

    const chain = ctx.update.message.text;

    if (!isValidChain(chain)) {
      const chains = dexscreenerChains.join(", ");
      await sails.helpers.sendMessage(
        chatId,
        `❌ Invalid Chain!\n\nPlease try again.\n\nType cancel to abort this operation. \n\nValid chains: ${chains}`
      );

      return;
    }

    await User.updateOne({ id: user.id }).set({
      mode: "set_page_token_address",
      currentConfiguredGroup: chain,
      currentConfiguredService: page.id,
      currentServiceType: page.pageType,
    });

    await sails.helpers.sendMessage(
      chatId,
      `Please submit the address of the token you'd like to add to your favorite tokens\ntype cancel to cancel\n\nClick the button below to go back`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `set_page_token_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  if (user.mode === "set_page_token_address") {
    const chain = user.currentConfiguredGroup;
    const page = await Page.findOne({ id: user.currentConfiguredService });

    if (!page) {
      // await User.updateOne({ id: user.id }).set({
      //   mode: "conversation",
      //   currentConfiguredGroup: "",
      //   currentConfiguredService: "",
      //   currentServiceType: "",
      // });

      await sails.helpers.sendMessage(
        chatId,
        `Please select a chain\ntype cancel to abort this operation\n\nSorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const address = ctx.update.message.text;

    if (!isValidAddress(address)) {
      await sails.helpers.sendMessage(
        chatId,
        `❌ Token Address <code>${address}</code> is not valid\ntype cancel to abort this operation\n\nSorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const tokenData = await getTokenData(address, chain);

    if (!tokenData || tokenData.length === 0) {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Please select a chain\ntype cancel to abort this operation\n\nSorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const formatedData = {
      contractAddress: address,
      chain,
      name: tokenData[0].baseToken.name,
      logo: tokenData[0].info.imageUrl,
      symbol: tokenData[0].baseToken.symbol,
      websites: tokenData[0].info.websites,
      socials: tokenData[0].info.socials,
    };

    const tokenExists = page.tokenData.contractAddress === address;

    if (tokenExists) {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Token <code>${formatedData.symbol}</code> already exists in your favorite tokens`
      );

      return;
    }

    await Page.updateOne({ id: page.id }).set({
      tokenData: formatedData,
    });

    await User.updateOne({ id: user.id }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
      currentServiceType: "",
    });

    await sails.helpers.sendMessage(
      chatId,
      `Community Token Added Successfully!`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `load_page_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }
  if (user.mode === "set_page_favorite_tokens_address") {
    // If User Tries to Submit Token Address for Favorite Token
    const chain = user.currentConfiguredGroup;
    const page = await Page.findOne({ id: user.currentConfiguredService });

    if (!page) {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Sorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const address = ctx.update.message.text;

    if (!isValidAddress(address)) {
      await sails.helpers.sendMessage(
        chatId,
        `❌ Token Address <code>${address}</code> is not valid\nSorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const tokenData = await getTokenData(address, chain);

    if (!tokenData || tokenData.length === 0) {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Sorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const formatedData = {
      contractAddress: address,
      chain,
      name: tokenData[0].baseToken.name,
      logo: tokenData[0].info.imageUrl,
      symbol: tokenData[0].baseToken.symbol,
      websites: tokenData[0].info.websites,
      socials: tokenData[0].info.socials,
    };

    // Check if First Array is Empty/Malformed
    if (
      page.favoriteTokens.contractAddress === "" ||
      page.favoriteTokens.length === 0 ||
      page.favoriteTokens === null
    ) {
      await Page.updateOne({ id: page.id }).set({
        favoriteTokens: [formatedData],
      });
    } else {
      // Check if Token Already Exists
      const tokenExists = page.favoriteTokens.find(
        (token) => token.symbol === formatedData.symbol
      );

      if (tokenExists) {
        await User.updateOne({ id: user.id }).set({
          mode: "conversation",
          currentConfiguredGroup: "",
          currentConfiguredService: "",
          currentServiceType: "",
        });

        await sails.helpers.sendMessage(
          chatId,
          `Token <code>${formatedData.symbol}</code> already exists in your favorite tokens`
        );

        return;
      }

      await Page.updateOne({ id: page.id }).set({
        favoriteTokens: [...page.favoriteTokens, formatedData],
      });
    }

    await User.updateOne({ id: user.id }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
      currentServiceType: "",
    });

    await sails.helpers.sendMessage(
      chatId,
      `Favorite Token Added Successfully!`,
      {
        inline_keyboard: [
          [
            {
              text: "Back",
              callback_data: `load_page_${page.id}`,
            },
          ],
        ],
      }
    );

    return;
  }

  // If User Tries to Set Social Media Pages
  if (user.mode === "set_page_social_media") {
    const sm = await SocialMedia.findOne({ id: user.currentConfiguredGroup });
    const page = await Page.findOne({ id: sm.page });

    // If Social Media Record does not exist, Reply with error
    if (!sm || !page) {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Sorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    // If User Tries to Cancel
    if (command === "cancel") {
      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      await sails.helpers.sendMessage(
        chatId,
        `Sorry something went wrong 🤕. Please try again later.`
      );

      return;
    }

    const ui = socialMediaList(sm);

    // Verifies Command is a URL
    if (!command.startsWith("http://") && !command.startsWith("https://")) {
      await sails.helpers.sendMessage(
        chatId,
        `Invalid URL! Please include http:// or https://\n\nPlease try again.\n\nCancel to abort this operation.`,
        ui
      );

      return;
    }

    // Update Social Media Record
    const updatedSM = await SocialMedia.updateOne({ id: sm.id }).set({
      [user.currentConfiguredService]: command,
    });

    await User.updateOne({ id: user.id }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
      currentServiceType: "",
    });

    const updatedUI = socialMediaList(updatedSM);

    await sails.helpers.sendMessage(
      chatId,
      `Social Media Updated Successfully!`,
      updatedUI
    );

    return;
  }

  // Check if Message is from Group
  if (groupMember && type !== "private") {
    const group = await getGroup(ctx.update.message.chat.id); // Check if Group Exists in Database

    if (!group) {
      sails.log.debug("Group Not Found");

      return;
    }

    // Handle Messages from Group
    if (command === `/ca@${botUsername}`) {
      // Use Group to Get Token Data
      if (group.buyToken.contractAddress && group.buyToken.chain) {
        const tokenData = await getTokenData(
          group.buyToken.contractAddress,
          group.buyToken.chain
        );

        await sails.helpers.sendMessage(
          groupMember,
          "🔍️ Fetching token data... This might take a few seconds. ⏳️"
        );

        if (!tokenData || tokenData.length === 0) {
          await sails.helpers.sendMessage(
            groupMember,
            "Couldn't retrieve token data. Please try again later."
          );

          return;
        }

        const ui = buyUI(group.buyToken.contractAddress, group.buyToken.chain);

        // Format the token data with our new formatter
        const caption = formatTokenData(tokenData[0]);
        const imgURL = weservProxy(tokenData[0].info.openGraph);

        await sails.helpers.sendPhoto(groupMember, caption, imgURL, ui);
        return;
      } else {
        return;
      }
    }
  }

  let keyboard;
  // Implement Cancel
  if (ctx.update.message.text && ctx.update.message.text.includes("cancel")) {
    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
      currentServiceType: "",
    });

    await sails.helpers.sendMessage(
      ctx.update.message.from.id,
      "Operation Canceled"
    );

    return;
  }

  // Implemente Delete
  if (
    (command === "delete" && user.mode === "set_page_logo") ||
    (command === "delete" && user.mode === "set_page_banner")
  ) {
    const page = await Page.findOne({ id: user.currentConfiguredService });

    if (!page) {
      await sails.helpers.sendMessage(chatId, `Server Process Error ❌`);

      return;
    }

    if (user.mode === "set_page_logo") {
      await Page.updateOne({ id: page.id }).set({
        pageLogo: {},
      });
    } else {
      await Page.updateOne({ id: page.id }).set({
        banner: {},
      });
    }

    await User.updateOne({
      chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    await sails.helpers.sendMessage(chatId, `✅ Media Deleted Successfully!`);

    return;
  }

  if (user && user.mode === "set_display_name") {
    const pageRecord = await Page.findOne({
      id: user.currentConfiguredService,
    });

    const group = await Group.findOne({
      id: pageRecord.group,
    });

    if (!pageRecord) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await Page.updateOne({
      id: pageRecord.id,
    }).set({
      displayName: ctx.update.message.text,
    });

    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    const keyboard = page_update_msg(
      pageRecord.id,
      pageRecord.pageTheme,
      pageRecord.pageType
    );

    await sails.helpers.sendMessage(
      group.groupOwnerId,
      `Your display name has been set to ${ctx.update.message.text}.`,
      keyboard
    );

    return;
  }

  if (user && user.mode === "set_page_bio") {
    const page = await Page.findOne({
      id: user.currentConfiguredService,
    });

    const group = await Group.findOne({ id: page.group });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    await Page.updateOne({
      id: page.id,
    }).set({
      pageBio: ctx.update.message.text,
    });

    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    const keyboard = page_update_msg(page.id, page.pageTheme, page.pageType);

    await sails.helpers.sendMessage(
      group.groupOwnerId,
      `Your Bio has been set to\n ${ctx.update.message.text}.`,
      keyboard
    );

    return;
  }

  // If User Replies with Custom URL Link
  if (user && user.mode === "set_custom_url_link") {
    const page = await Page.findOne({
      id: user.currentConfiguredService,
    });

    const group = await Group.findOne({ id: page.group });

    if (!page) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`
      );
      return;
    }

    const url = ctx.update.message.text.trim();

    // Check If URL is not taken
    const pageRecord = await Page.findOne({ domainName: url });

    if (pageRecord) {
      await sails.helpers.sendMessage(
        user.chatId,
        "❌ This domain is already taken. Please choose a different domain name."
      );

      return;
    }

    await Page.updateOne({
      id: page.id,
    }).set({
      domainName: url,
    });

    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    const keyboard = page_update_msg(page.id, page.pageTheme, page.pageType);

    await sails.helpers.sendMessage(
      user.chatId,
      `✅ Custom URL has been set to: ${process.env.FRONTEND_URL}/${url}`,
      keyboard
    );

    return;
  }

  // If User Replies with Token Chain
  if (user && user.mode === "set_buy_token_chain") {
    const chain = ctx.update.message.text.trim().toLowerCase();

    if (chain === "cancel") {
      await User.updateOne({
        chatId: user.chatId,
      }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
      });

      await sails.helpers.sendMessage(
        user.chatId,
        "✅ Setting Buy Token Chain has been cancelled."
      );

      return;
    }

    const group = await Group.findOne({ id: user.currentConfiguredGroup });

    if (!group) {
      await sails.helpers.sendMessage(
        user.chatId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`
      );
      return;
    }

    if (!isValidChain(chain)) {
      const chains = dexscreenerChains.join(", ");
      await sails.helpers.sendMessage(
        user.chatId,
        `❌ Invalid Chain. Please choose a valid chain.\nType cancel to cancel.\n\nValid chains: ${chains}`
      );
      return;
    }

    await Group.updateOne({
      id: group.id,
    }).set({
      buyToken: {
        chain: chain,
      },
    });

    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "set_buy_token_contract",
    });

    // Send Message Confirming Chain and requesting user sends token address
    await sails.helpers.sendMessage(
      user.chatId,
      `✅ Buy Token Chain has been set to: ${chain}\n\nPlease send the token address of the token you want to set as the buy token.`
    );

    return;
  }

  // If User Replies with Cancel
  if (command === "cancel_buy_token") {
    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "conversation",
      currentConfiguredGroup: "",
      currentConfiguredService: "",
    });

    await sails.helpers.sendMessage(
      user.chatId,
      "✅ Setting Buy Token has been cancelled."
    );

    return;
  }

  // If User Replies with Token Address
  if (user && user.mode === "set_buy_token_contract") {
    const contractAddress = ctx.update.message.text.trim();

    if (contractAddress === "cancel") {
      await User.updateOne({
        chatId: user.chatId,
      }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
      });

      await sails.helpers.sendMessage(
        user.chatId,
        "✅ Setting Buy Token Contract has been cancelled."
      );

      return;
    }

    if (!isValidAddress(contractAddress)) {
      await sails.helpers.sendMessage(
        user.chatId,
        "❌ Invalid Address. Please enter a valid address.\nType cancel to cancel."
      );
      return;
    }

    // Find Current Configured Group
    const group = await Group.findOne({ id: user.currentConfiguredGroup });

    if (!group) {
      await sails.helpers.sendMessage(
        user.chatId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`
      );
      return;
    }

    // Send Loading Message
    await sails.helpers.sendMessage(
      user.chatId,
      "🔄 Loading... Please wait..."
    );

    // Fetch Token Data
    const tokenData = await getTokenData(contractAddress, group.buyToken.chain);

    if (!tokenData || !tokenData[0]) {
      await sails.helpers.sendMessage(
        user.chatId,
        `Failed to fetch token data from dexscreener for ${contractAddress} on chain ${group.buyToken.chain}\nPlease confirm the token address and try again.\nType cancel to cancel`
      );
      return;
    }

    sails.log.debug("Token Data", tokenData[0]);

    // Update User Record to Temp Store Contract Address
    await User.updateOne({
      chatId: user.chatId,
    }).set({
      mode: "confirm_buy_token_contract",
      currentConfiguredService: contractAddress,
    });

    // Send UX Centered Confirmation Message with DexScreener Data
    const dexData = await getTokenData(contractAddress, group.buyToken.chain);
    const message =
      `<b>🔍 Token Verification 🔍</b>\n\n` +
      `<b>${tokenData[0].baseToken.name} (${tokenData[0].baseToken.symbol})</b>\n` +
      `<b>Chain:</b> ${group.buyToken.chain.toUpperCase()}\n` +
      `<b>Price (USD):</b> $${formatDeepDecimal(
        (dexData[0] && dexData[0].priceUsd) || 0
      )}
` +
      `<b>Liquidity (USD):</b> $${(
        (dexData[0] && dexData[0].liquidity && dexData[0].liquidity.usd) ||
        0
      ).toLocaleString()}
` +
      `<b>24h Volume:</b> $${(
        (dexData[0] && dexData[0].volume && dexData[0].volume.h24) ||
        0
      ).toLocaleString()}\n` +
      `<b>24h Price Change:</b> ${
        (dexData[0] && dexData[0].priceChange && dexData[0].priceChange.h24) > 0
          ? "🟢"
          : "🔴"
      } ${Math.abs(
        (dexData[0] && dexData[0].priceChange && dexData[0].priceChange.h24) ||
          0
      )}%
` +
      `Market Cap : $${
        (dexData[0] && dexData[0].marketCap && dexData[0].marketCap) || 0
      }
` +
      `<b>Contract Address:</b>\n<code>${contractAddress}</code>`;

    await sails.helpers.sendMessage(user.chatId, message, {
      inline_keyboard: [
        [
          {
            text: "✅ Confirm",
            callback_data: `confirm_contract_${contractAddress}`,
          },
          {
            text: "❌ Cancel",
            callback_data: "cancel_buy_token",
          },
        ],
        [
          {
            text: "🔗 View on DexScreener",
            url: (dexData[0] && dexData[0].url) || "https://dexscreener.com/",
          },
        ],
      ],
    });

    return;
  }

  if (ctx.update.message.text === "Hello") {
    // Simple Reply to Hello
    await sails.helpers.sendMessage(ctx.update.message.from.id, `Hello!`);
    return;
  }

  // If New User Joins
  if (
    ctx.update.message.new_chat_member &&
    !ctx.update.message.new_chat_member.is_bot
  ) {
    const group = await Group.findOne({ groupId: ctx.update.message.chat.id });
    const page = await Page.findOne({ group: group.id });

    if (!page) {
      const { chat_id, message_id } = await sails.helpers.sendMessage(
        group.groupId,
        `👋 Welcome to ${group.groupTitle}, @${ctx.update.message.new_chat_member.username}!

🌐 You've joined a web3 community powered by Anubis Trade. Get ready to explore the future of decentralized finance together!`,
        {
          inline_keyboard: [
            [
              {
                text: "🚀 Start Trading on Anubis",
                url: "https://www.anubistrade.xyz",
              },
            ],
          ],
        }
      );

      setTimeout(async () => {
        await sails.helpers.deleteMessage(chat_id, message_id);
      }, 120000);

      return;
    }

    // Prepare social media buttons if available
    const socialButtons =
      page.links && page.links.length > 0
        ? page.links.slice(0, 2).map((link) => ({
            text: `🔗 ${link.platform}`,
            url: link.link,
          }))
        : [];

    const welcomeMessage = `👋 Welcome to ${page.displayName}, @${
      ctx.update.message.new_chat_member.username
    }!

${page.pageBio || "A web3 community powered by Anubis Trade"}

Get started with your web3 journey today!`;

    const { chat_id, message_id } = await sails.helpers.sendMessage(
      group.groupId,
      welcomeMessage,
      {
        inline_keyboard: [
          ...(socialButtons.length > 0 ? [socialButtons] : []),
          [
            {
              text: `🌐 Visit ${page.displayName}`,
              url: `${terminalUrl}/${page.domainName}`,
            },
          ],
          [
            {
              text: "🚀 Trade on Anubis",
              url: terminalUrl,
            },
          ],
        ],
      }
    );

    setTimeout(async () => {
      await sails.helpers.deleteMessage(chat_id, message_id);
    }, 120000);

    return;
  }

  // Implement Handling Images
  if (ctx.message.photo && user.mode === "set_page_logo") {
    const page = await Page.findOne({
      id: user.currentConfiguredService,
    });

    try {
      await setLogo(page.id, ctx);
    } catch (error) {
      sails.log.error(error);
    }
  }

  if (ctx.message.photo && user.mode === "set_page_banner") {
    const page = await Page.findOne({
      id: user.currentConfiguredService,
    });

    try {
      await setBannerImage(page.id, ctx);
    } catch (error) {
      sails.log.error(error);
    }
  }

  // Implement Page Color Accent Change
  if (user.mode === "set_page_color") {
    const pageRecord = await Page.findOne({
      id: user.currentConfiguredService,
    });

    const group = await Group.findOne({
      id: pageRecord.group,
    });

    if (!pageRecord) {
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `Server Process Failed!❌\n\nPlease report this to @sunami_trade_support.`,
        keyboard
      );

      return;
    }

    try {
      await handleColorUpdate(pageRecord.id, ctx);
    } catch (error) {
      sails.log.error(error);
    }
  }

  // Implement Warning Message for If Bot is Removed from Managed Group
  if (
    ctx.update.message.left_chat_member &&
    ctx.update.message.left_chat_member.is_bot &&
    ctx.update.message.left_chat_member.username === botUsername
  ) {
    try {
      const group = await Group.findOne({
        groupId: ctx.update.message.chat.id,
      });

      if (!group) {
        sails.log.warn(
          `Group not found for groupId: ${ctx.update.message.chat.id} when bot was removed.`
        );
        // Optionally, notify a support/admin channel here
        return;
      }

      // UX-centered message with group name fallback
      const groupName = group.groupTitle || `ID: ${group.groupId}`;
      await sails.helpers.sendMessage(
        group.groupOwnerId,
        `⚠️ The Anubis Teams Bot has been removed from your group "${groupName}".\n\nTo continue using the bot's features, please add it back to the group.`
      );
    } catch (error) {
      sails.log.error(
        `Error handling bot removal from group (chat id: ${ctx.update.message.chat.id}):`,
        error
      );
      // Optionally, notify a support/admin channel here
    }
  }
}

async function handleGroupAppEdit(ctx) {
  if (ctx.update.message.text === "/app@anubis_teams_bot") {
    sails.log.debug("Handle Group App Edit Fired 🔥");
    sails.log.debug("Context Message", ctx.update.message);
    const user = await getUser(ctx.update.message.from.id);
    const group = await getGroup(ctx.update.message.chat.id);
    const page = await Page.findOne({ group: isAdmin.id });

    if (!isAdmin) {
      return;
    }

    if (isAdmin && page) {
      await sails.helpers.sendMessage(
        isAdmin.groupId,
        `🆔 Customize your Community Page: ${page.displayName}`
      );

      return;
    }
  }
}

module.exports = {
  processInit,
  setupGroup,
  handleCallBackQuery,
  handleTextMessage,
  handleGroupAppEdit,
};
