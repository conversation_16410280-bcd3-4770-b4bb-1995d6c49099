"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useCookies } from 'react-cookie';
import { useRouter } from 'next/navigation';
import { getUser, getWallets, getSettings, getNativeBalance, getUserPortfolio } from "@/services/bot";

interface AuthContextType {
    data: any;
    isLoggedIn: boolean;
    token: string | null;
    user: null;
    loading: boolean;
    logout: () => void;
    storeCookie: (token: string) => void;
    cacheProfile: (user: any) => void;
    deleteProfile: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [cookies, setCookie, removeCookie] = useCookies(['anubis_terminal']);
    const [token, setToken] = useState<string | null>(null);
    const [user, setUser] = useState<any | null>(null);
    const [data, setData] = useState<any | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const { push } = useRouter();

    function storeCookie(token: string) {
        setCookie('anubis_terminal', token, { path: '/', maxAge: 3600 * 24 * 7 }); // 7 days

        setToken(token);
        setIsLoggedIn(true);
    }

    function logout() {
        removeCookie('anubis_terminal', { path: '/' });
        setToken(null);
        setIsLoggedIn(false);
        push('/login');
    }

    function cacheProfile(user: any) {
        window.localStorage.setItem("anubis_user", JSON.stringify(user));
        setUser(user);
    }

    function deleteProfile() {
        window.localStorage.removeItem("anubis_user");
        setUser(null);
    }

    useEffect(() => {
        if (cookies.anubis_terminal) {
            setToken(cookies.anubis_terminal);
            setIsLoggedIn(true);
        }

        const user = window.localStorage.getItem("anubis_user");
        if (user) {
            setUser(JSON.parse(user));
        }

        // Make API Calls to Grab Info
        if (isLoggedIn) {
            setLoading(true);
            (async () => {
                try {
                    const [userData, wallets, settings, balance, portfolio] = await Promise.all([
                        getUser(token as string),
                        getWallets(token as string),
                        getSettings(token as string),
                        getNativeBalance(token as string),
                        getUserPortfolio(token as string)
                    ]);

                    console.log(
                        userData,
                        wallets,
                        settings,
                        balance,
                        portfolio
                    )

                    setData({
                        userData,
                        wallets,
                        settings,
                        balance,
                        portfolio
                    });
                } catch (error) {
                    console.error("Error fetching user data", error);
                } finally {
                    setLoading(false);
                }
            })();
        }

    }, [cookies.anubis_terminal])


    return (
        <AuthContext.Provider value={{ data, isLoggedIn, token, user, loading, logout, storeCookie, cacheProfile, deleteProfile }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};