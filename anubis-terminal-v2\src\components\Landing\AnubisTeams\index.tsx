"use client"
import { motion } from "framer-motion";
import {
    <PERSON>aTelegram,
    FaUsers,
    FaPalette,
    FaLink,
    FaChartLine,
    FaCog,
    FaRocket,
    FaShieldAlt,
    FaGlobe,
    FaMobile
} from "react-icons/fa";
import { BiBot } from "react-icons/bi";
import { MdAutoAwesome } from "react-icons/md";
import Link from 'next/link'

interface FeatureCardProps {
    icon: React.ReactNode;
    title: string;
    description: string;
    index: number;
    gradient: string;
}

const FeatureCard = ({ icon, title, description, index, gradient }: FeatureCardProps) => {
    return (
        <motion.div
            className={`${gradient} p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 relative overflow-hidden group`}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true, margin: "-50px" }}
            whileHover={{
                scale: 1.02,
                transition: { duration: 0.2 }
            }}
        >
            {/* Animated background glow */}
            <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                initial={{ x: -100 }}
                whileHover={{ x: 100 }}
                transition={{ duration: 0.6 }}
            />

            <div className="relative z-10">
                <motion.div
                    className="mb-4 text-4xl text-white relative"
                    initial={{ scale: 0, rotate: -180 }}
                    whileInView={{ scale: 1, rotate: 0 }}
                    transition={{
                        type: "spring",
                        stiffness: 200,
                        damping: 15,
                        delay: 0.2 + index * 0.1
                    }}
                    viewport={{ once: true, margin: "-50px" }}
                    whileHover={{
                        scale: 1.1,
                        filter: "drop-shadow(0 0 20px rgba(255, 255, 255, 0.6))",
                        transition: { duration: 0.2 }
                    }}
                >
                    <motion.div
                        className="absolute inset-0 bg-white/20 rounded-full blur-xl"
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.6, 0.3],
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    />
                    {icon}
                </motion.div>

                <h3 className="text-xl font-orbitron font-bold mb-3 text-white group-hover:text-white/90 transition-colors">
                    {title}
                </h3>
                <p className="text-gray-300 font-space-grotesk leading-relaxed">
                    {description}
                </p>
            </div>
        </motion.div>
    );
};

const FloatingIcon = ({ icon, delay = 0, position = 1 }: { icon: React.ReactNode; delay?: number; position?: number }) => {
    return (
        <motion.div
            className={`absolute text-white/20 text-2xl floating-icon-${position}`}
            animate={{
                y: [0, -20, 0],
                rotate: [0, 5, -5, 0],
                scale: [1, 1.1, 1],
            }}
            transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay
            }}
            whileHover={{
                scale: 1.2,
                color: "rgba(255, 255, 255, 0.8)",
                filter: "drop-shadow(0 0 10px rgba(255, 255, 255, 0.5))",
                transition: { duration: 0.2 }
            }}
        >
            {icon}
        </motion.div>
    );
};

export default function AnubisTeams() {
    const features = [
        {
            icon: <FaTelegram />,
            title: "Telegram Bot Integration",
            description: "Seamlessly integrate with your Telegram groups. Create custom pages and manage your community with powerful bot commands.",
            gradient: "bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a]"
        },
        {
            icon: <FaUsers />,
            title: "Community Management",
            description: "Set up and manage your trading community with custom branding, member onboarding, and automated welcome messages.",
            gradient: "bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a]"
        },
        {
            icon: <FaPalette />,
            title: "Custom Branding",
            description: "Personalize your community page with custom logos, banners, color themes, and unique URL slugs for a professional look.",
            gradient: "bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a]"
        },
        {
            icon: <FaLink />,
            title: "Social Media Integration",
            description: "Connect all your social media platforms. Share your community page with integrated links to Twitter, Discord, and more.",
            gradient: "bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a]"
        },
        {
            icon: <FaChartLine />,
            title: "Token Integration",
            description: "Configure your community's token with real-time price data from DexScreener. Use /ca command for instant token information.",
            gradient: "bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a]"
        },
        {
            icon: <BiBot />,
            title: "Smart Automation",
            description: "Automated welcome messages, token alerts, and community management tools that work 24/7 to grow your community.",
            gradient: "bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a]"
        }
    ];

    const highlights = [
        {
            icon: <FaRocket />,
            title: "Quick Setup",
            description: "Get your community page live in minutes with our guided setup process."
        },
        {
            icon: <FaShieldAlt />,
            title: "Secure & Reliable",
            description: "Built on Sails.js with enterprise-grade security and reliability."
        },
        {
            icon: <FaGlobe />,
            title: "Public Pages",
            description: "Create beautiful public-facing pages that showcase your community to the world."
        },
        {
            icon: <FaMobile />,
            title: "Mobile Optimized",
            description: "Perfect experience across all devices with responsive design."
        }
    ];

    return (
        <section className="py-20 px-4 md:px-8 lg:px-12 relative overflow-hidden" id="anubis-teams">
            {/* Floating background icons */}
            <FloatingIcon icon={<FaTelegram />} delay={0} position={1} />
            <FloatingIcon icon={<BiBot />} delay={1} position={2} />
            <FloatingIcon icon={<FaUsers />} delay={2} position={3} />
            <FloatingIcon icon={<FaChartLine />} delay={3} position={4} />

            <div className="max-w-7xl mx-auto relative z-10">
                {/* Header Section */}
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: -30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <motion.div
                        className="inline-flex items-center gap-3 mb-6 px-4 py-2 bg-white/5 rounded-full border border-white/10"
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        viewport={{ once: true }}
                    >
                        <motion.div
                            animate={{
                                rotate: [0, 360],
                            }}
                            transition={{
                                duration: 3,
                                repeat: Infinity,
                                ease: "linear"
                            }}
                        >
                            <BiBot className="text-2xl text-white" />
                        </motion.div>
                        <span className="font-space-grotesk text-white/80 text-sm">Telegram Bot</span>
                    </motion.div>

                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-400 to-purple-400">
                        Anubis Teams
                    </h2>
                    <p className="text-gray-400 font-space-grotesk max-w-3xl mx-auto text-lg leading-relaxed">
                        Create and manage your trading community with our powerful Telegram bot.
                        Build custom pages, integrate tokens, and grow your community with professional tools.
                    </p>
                </motion.div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
                    {features.map((feature, index) => (
                        <FeatureCard
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            description={feature.description}
                            index={index}
                            gradient={feature.gradient}
                        />
                    ))}
                </div>

                {/* Highlights Section */}
                <motion.div
                    className="bg-gradient-to-r from-white/5 to-white/10 rounded-2xl p-8 border border-white/10"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <div className="text-center mb-8">
                        <h3 className="text-2xl md:text-3xl font-orbitron font-bold text-white mb-4">
                            Why Choose Anubis Teams?
                        </h3>
                        <p className="text-gray-400 font-space-grotesk">
                            Everything you need to build and grow your trading community
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {highlights.map((highlight, index) => (
                            <motion.div
                                key={index}
                                className="text-center"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                viewport={{ once: true }}
                            >
                                <motion.div
                                    className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-full mb-4 mx-auto"
                                    whileHover={{
                                        scale: 1.1,
                                        backgroundColor: "rgba(255, 255, 255, 0.2)",
                                        transition: { duration: 0.2 }
                                    }}
                                >
                                    <motion.div
                                        className="text-2xl text-white"
                                        animate={{
                                            filter: ["drop-shadow(0 0 5px rgba(255, 255, 255, 0.3))", "drop-shadow(0 0 15px rgba(255, 255, 255, 0.6))", "drop-shadow(0 0 5px rgba(255, 255, 255, 0.3))"],
                                        }}
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                            ease: "easeInOut"
                                        }}
                                    >
                                        {highlight.icon}
                                    </motion.div>
                                </motion.div>
                                <h4 className="font-orbitron font-semibold text-white mb-2">{highlight.title}</h4>
                                <p className="text-gray-400 font-space-grotesk text-sm">{highlight.description}</p>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>

                {/* Demo Section */}
                <motion.div
                    className="mt-16 bg-gradient-to-r from-white/5 to-white/10 rounded-2xl p-8 border border-white/10"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <div className="text-center mb-8">
                        <h3 className="text-2xl md:text-3xl font-orbitron font-bold text-white mb-4">
                            See It In Action
                        </h3>
                        <p className="text-gray-400 font-space-grotesk">
                            Watch how easy it is to set up and manage your community
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <motion.div
                            className="space-y-4"
                            initial={{ opacity: 0, x: -30 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <div className="flex items-center gap-3 p-4 bg-white/5 rounded-lg border border-white/10">
                                <FaTelegram className="text-2xl text-blue-400" />
                                <div>
                                    <h4 className="font-orbitron font-semibold text-white">Bot Commands</h4>
                                    <p className="text-gray-400 text-sm">Use /start, /setup, /app, /ca</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-3 p-4 bg-white/5 rounded-lg border border-white/10">
                                <FaCog className="text-2xl text-green-400" />
                                <div>
                                    <h4 className="font-orbitron font-semibold text-white">Easy Setup</h4>
                                    <p className="text-gray-400 text-sm">Configure your community in minutes</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-3 p-4 bg-white/5 rounded-lg border border-white/10">
                                <MdAutoAwesome className="text-2xl text-purple-400" />
                                <div>
                                    <h4 className="font-orbitron font-semibold text-white">Auto Welcome</h4>
                                    <p className="text-gray-400 text-sm">New members get welcomed automatically</p>
                                </div>
                            </div>
                        </motion.div>

                        <motion.div
                            className="relative"
                            initial={{ opacity: 0, x: 30 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                            viewport={{ once: true }}
                        >
                            <div className="bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a] rounded-xl p-6 border border-white/10">
                                <div className="flex items-center gap-3 mb-4">
                                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span className="text-gray-400 text-sm ml-2">Community Page Preview</span>
                                </div>
                                <div className="space-y-3">
                                    <div className="h-4 bg-white/10 rounded"></div>
                                    <div className="h-3 bg-white/5 rounded w-3/4"></div>
                                    <div className="h-3 bg-white/5 rounded w-1/2"></div>
                                    <div className="flex gap-2 mt-4">
                                        <div className="w-8 h-8 bg-blue-500/20 rounded"></div>
                                        <div className="w-8 h-8 bg-green-500/20 rounded"></div>
                                        <div className="w-8 h-8 bg-purple-500/20 rounded"></div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </motion.div>

                {/* CTA Section */}
                <motion.div
                    className="text-center mt-16"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <Link href="https://t.me/anubis_teams_bot" target="_blank">
                        <motion.button
                            className="font-orbitron font-semibold bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-xl text-lg hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
                            whileHover={{
                                scale: 1.05,
                                boxShadow: "0 0 30px rgba(59, 130, 246, 0.5)",
                            }}
                            whileTap={{
                                scale: 0.95
                            }}
                        >
                            <span className="flex items-center gap-2">
                                <FaTelegram className="text-xl" />
                                Get Started with Anubis Teams
                            </span>
                        </motion.button>
                    </Link>
                    <p className="text-gray-500 font-space-grotesk mt-4 text-sm">
                        Free to start • No credit card required
                    </p>
                </motion.div>
            </div>
        </section>
    );
} 