"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useWeb3 } from '@/contexts/Web3Context';
import { chains } from '@/utils/data';
import { X } from 'lucide-react';
import { IoMdSettings } from 'react-icons/io';
import { FaWallet, FaExchangeAlt, FaCog } from 'react-icons/fa';
import { TiTick } from 'react-icons/ti';
import { updateSettings, setPrimaryWallet } from '@/services/bot';
import { toast } from 'react-toastify';
import { useAuth } from '@/contexts/AuthContext';
import { useUserData } from '@/hooks/useUserData';
import { IoChevronDown } from 'react-icons/io5';
import { Loader2 } from 'lucide-react';

type SettingsTab = 'general' | 'wallet' | 'trading';

export default function SettingsModal({
    openSettingsModal,
    setOpenSettingsModal
}: {
    openSettingsModal: boolean,
    setOpenSettingsModal: React.Dispatch<React.SetStateAction<boolean>>
}) {
    const { selectedChain, setSelectedChain } = useWeb3();
    const { token } = useAuth();
    const { data, refetch } = useUserData(token as string);
    const [activeTab, setActiveTab] = useState<SettingsTab>('general');
    const [slippage, setSlippage] = useState<"0.1" | "0.5" | "1" | string>('0.5');
    const [gasPrice, setGasPrice] = useState<string>('10');
    const [newPrimaryWallet, setNewPrimaryWallet] = useState<`0x${string}`>('0x');

    const gasPriceOptions = [
        { value: '10', label: 'Slow' },
        { value: '20', label: 'Standard' },
        { value: '30', label: 'Fast' }
    ];
    const [selectedWalletAddress, setSelectedWalletAddress] = useState<string>('');
    const [showWallets, setShowWallets] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);

    if (!openSettingsModal) return null;

    const tabIcons = {
        general: <FaCog className="mr-2" />,
        wallet: <FaWallet className="mr-2" />,
        trading: <FaExchangeAlt className="mr-2" />
    };

    const handleSlippageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        if (value === '' || /^\d*\.?\d*$/.test(value)) {
            setSlippage(value);
        }
    };

    const handleChainChange = (chainId: number) => {
        const chain = chains.find(c => c.chainId === chainId);
        if (chain) setSelectedChain(chain);
    };

    const handleSaveSettings = async () => {
        try {
            setLoading(true);
            const response = await updateSettings(token as string, {
                slippage: Number(slippage),
                gasPrice: Number(gasPrice),
                chainId: selectedChain?.chainId,
            });
            console.log("newPrimaryWallet", newPrimaryWallet);
            if(newPrimaryWallet !== "0x") {
                await setPrimaryWallet(token as string, newPrimaryWallet);
            }

            console.log('Settings saved:', response);
            refetch();
            toast.success('Settings saved successfully');
        } catch (error) {
            console.error('Error saving settings:', error);
            toast.error('Failed to save settings');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (data) {
            setSlippage(data.settings.slippage.toString());
            setGasPrice(data.settings.gasPrice.toString());
        }
    }, [data]);

    return (
        <AnimatePresence>
            <motion.div
                className="fixed inset-0 z-50 flex items-center justify-center p-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
            >
                <motion.div
                    className="fixed inset-0 bg-black/80 backdrop-blur-sm"
                    onClick={() => setOpenSettingsModal(false)}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                />

                <motion.div
                    className="bg-black/95 backdrop-blur-sm w-full max-w-md h-fit md:h-auto overflow-hidden shadow-2xl relative z-10 border border-white/10"
                    initial={{ x: '100%', opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    exit={{ x: '100%', opacity: 0 }}
                    transition={{ duration: 0.3, ease: 'easeInOut' }}
                    onClick={(e) => { e.stopPropagation() }}
                >
                    {/* Header */}
                    <div className="flex items-center justify-between p-4 border-b border-white/10">
                        <div className="flex items-center">
                            <IoMdSettings className="text-blue-400 mr-2 text-xl" />
                            <h3 className="text-lg font-orbitron font-bold text-white">Settings</h3>
                        </div>
                        <button
                            onClick={() => setOpenSettingsModal(false)}
                            className="p-2 hover:bg-white/10 rounded-md transition-colors"
                        >
                            <X size={20} className="text-white" />
                        </button>
                    </div>

                    {/* Tabs */}
                    <div className="flex border-b border-white/10 bg-black/50">
                        {(Object.keys(tabIcons) as SettingsTab[]).map((tab) => (
                            <button
                                key={tab}
                                onClick={() => setActiveTab(tab)}
                                className={`flex-1 py-3 text-sm font-space-grotesk font-medium transition-colors ${activeTab === tab
                                    ? 'text-blue-400 border-b-2 border-blue-400 bg-gradient-to-b from-blue-400/5 to-transparent'
                                    : 'text-white/60 hover:text-white/90 hover:bg-white/5'
                                    }`}
                            >
                                <div className="flex items-center justify-center gap-2">
                                    {tabIcons[tab]}
                                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                                </div>
                            </button>
                        ))}
                    </div>

                    {/* Tab Content */}
                    <div className="p-4 space-y-4 overflow-y-auto max-h-[60vh] md:max-h-[50vh]">
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={activeTab}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                transition={{ duration: 0.2 }}
                                className="space-y-4"
                            >
                                {activeTab === 'general' && (
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-space-grotesk font-medium text-white/80 mb-2">
                                                Network
                                            </label>
                                            <div className="grid grid-cols-2 gap-2">
                                                {chains.map((chain) => (
                                                    <motion.button
                                                        key={chain.chainId}
                                                        onClick={() => handleChainChange(chain.chainId)}
                                                        className={`flex items-center gap-2 p-3 rounded-md transition-colors ${selectedChain?.chainId === chain.chainId ? 'bg-white/10' : 'hover:bg-white/5'}`}
                                                        whileTap={{ scale: 0.98 }}
                                                    >
                                                        <img src={chain.logo} alt={chain.chain} className="w-5 h-5 object-contain" />
                                                        <span className="font-space-grotesk text-sm">{chain.chain}</span>
                                                        {selectedChain?.chainId === chain.chainId && (
                                                            <TiTick className="text-blue-400 ml-auto" />
                                                        )}
                                                    </motion.button>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {activeTab === 'wallet' && (
                                    <div className="space-y-4 h-fit">
                                        <div className="mb-2">
                                            <h3 className="text-sm font-space-grotesk font-medium text-white/90 mb-1">
                                                Primary Wallet
                                            </h3>
                                            <p className="text-xs text-white/60">Your primary wallet for all transactions</p>
                                        </div>

                                        <div className="relative">
                                            <button
                                                type="button"
                                                className="w-full flex items-center justify-between px-4 py-3 bg-white/5 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200"
                                                onClick={() => setShowWallets(!showWallets)}
                                                aria-haspopup="listbox"
                                                aria-expanded={showWallets}
                                            >
                                                <div className="flex items-center gap-2">
                                                    <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse" />
                                                    <span className="text-white font-mono text-sm">
                                                        {selectedWalletAddress
                                                            ? `${selectedWalletAddress.substring(0, 6)}...${selectedWalletAddress.slice(-4)}`
                                                            : 'Select a wallet'}
                                                    </span>
                                                </div>
                                                <motion.div
                                                    animate={{ rotate: showWallets ? 180 : 0 }}
                                                    transition={{ duration: 0.2 }}
                                                >
                                                    <IoChevronDown className="text-white/60 text-lg" />
                                                </motion.div>
                                            </button>

                                            <AnimatePresence>
                                                {showWallets && data?.wallets && data.wallets.length > 0 && (
                                                    <motion.div
                                                        initial={{ opacity: 0, y: -10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        exit={{ opacity: 0, y: -10 }}
                                                        transition={{ duration: 0.15 }}
                                                        className="fixed z-[100] mt-1 w-[calc(100%-3rem)] max-w-[28rem] bg-gray-900/95 backdrop-blur-sm rounded-lg border border-white/10 shadow-xl overflow-hidden h-fit"
                                                        onClick={(e) => e.stopPropagation()}
                                                    >
                                                        <div className="max-h-[50vh] overflow-y-auto custom-scrollbar">
                                                            {data.wallets.map((wallet: { address: string }, index: number) => (
                                                                <button
                                                                    key={index}
                                                                    className={`w-full text-left px-4 py-3 text-sm font-mono transition-colors flex items-center ${selectedWalletAddress === wallet.address
                                                                        ? 'bg-blue-500/20 text-blue-400'
                                                                        : 'text-white/80 hover:bg-white/5'}`}
                                                                    onClick={() => {
                                                                        setSelectedWalletAddress(wallet.address);
                                                                        setShowWallets(false);
                                                                        setNewPrimaryWallet(wallet.address as `0x${string}`);
                                                                    }}
                                                                >
                                                                    <span className="truncate">
                                                                        {wallet.address}
                                                                    </span>
                                                                    {index === data?.user?.walletId && (
                                                                        <TiTick className="ml-auto text-blue-400" size={18} />
                                                                    )}
                                                                </button>
                                                            ))}
                                                        </div>
                                                    </motion.div>
                                                )}
                                            </AnimatePresence>
                                        </div>

                                        {showWallets && (
                                            <div
                                                className="fixed inset-0 z-10"
                                                onClick={() => setShowWallets(false)}
                                            />
                                        )}
                                    </div>
                                )}

                                {activeTab === 'trading' && (
                                    <div className="space-y-6">
                                        <div>
                                            <label className="block text-sm font-space-grotesk font-medium text-white/80 mb-2">
                                                Slippage Tolerance
                                            </label>
                                            <div className="flex items-center space-x-3">
                                                <div className="relative flex-1">
                                                    <div className="relative">
                                                        <input
                                                            type="text"
                                                            value={slippage}
                                                            onChange={handleSlippageChange}
                                                            className="w-full bg-white/5 text-white rounded-lg border border-white/10 px-4 py-3 text-sm focus:ring-2 focus:ring-blue-400/50 focus:border-transparent pr-10"
                                                            readOnly
                                                        />
                                                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50">%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex space-x-2 mt-3">
                                                {['0.1', '0.5', '1.0'].map((value) => (
                                                    <motion.button
                                                        key={value}
                                                        type="button"
                                                        onClick={() => setSlippage(value)}
                                                        whileHover={{ scale: 1.03 }}
                                                        whileTap={{ scale: 0.98 }}
                                                        className={`px-4 py-2 text-xs rounded-lg font-space-grotesk font-medium transition-colors ${slippage === value
                                                            ? 'bg-blue-500 text-white'
                                                            : 'bg-white/5 text-white/70 hover:bg-white/10'
                                                            }`}
                                                    >
                                                        {value}%
                                                    </motion.button>
                                                ))}
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-space-grotesk font-medium text-white/80 mb-2">
                                                Gas Price
                                            </label>
                                            <div className="space-y-2">
                                                {gasPriceOptions.map((option) => (
                                                    <motion.button
                                                        key={option.value}
                                                        onClick={() => setGasPrice(option.value)}
                                                        className={`w-full flex items-center justify-between p-3 rounded-md transition-colors ${gasPrice === option.value ? 'bg-blue-500/10 border border-blue-500/30' : 'hover:bg-white/5 border border-white/5'}`}
                                                        whileTap={{ scale: 0.98 }}
                                                    >
                                                        <span className="font-space-grotesk text-sm">
                                                            {option.label}
                                                        </span>
                                                        {gasPrice === option.value && (
                                                            <TiTick className="text-blue-400" />
                                                        )}
                                                    </motion.button>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </motion.div>
                        </AnimatePresence>
                    </div>

                    {/* Footer */}
                    <div className="p-4 border-t border-white/10 flex justify-end space-x-3 bg-black/30">
                        <motion.button
                            onClick={() => setOpenSettingsModal(false)}
                            whileTap={{ scale: 0.98 }}
                            className="px-4 py-2 text-sm font-space-grotesk font-medium text-white/80 hover:text-white hover:bg-white/10 rounded-md transition-colors"
                        >
                            Cancel
                        </motion.button>
                        <motion.button
                            onClick={handleSaveSettings}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className="px-4 py-2 text-sm font-orbitron font-medium text-white bg-blue-500 hover:bg-blue-500 rounded-md transition-colors 
                            flex gap-x-3 items-center"
                        >
                            <span>Save Settings</span>
                            {loading && <Loader2 className="w-4 h-4 animate-spin" />}
                        </motion.button>
                    </div>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
}