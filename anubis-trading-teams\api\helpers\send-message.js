const axios = require("axios");
const retry = require("async-retry");

module.exports = {
  friendlyName: "Send Telegram message",

  description: "Send Message Via Bot",

  inputs: {
    chatId: {
      type: "string",
      description: "User Chat ID",
      required: true,
    },
    text: {
      type: "string",
      description: "Message to send to user",
      required: true,
    },
    keyboard: {
      type: "json",
      description: "Keyboard Markup",
    },
    enableWebPagePreview: {
      type: "boolean",
      description: "Disable WebPage",
      defaultsTo: false,
    },
  },

  exits: {
    success: {
      description: "All done.",
    },
  },

  fn: async function ({ chatId, text, keyboard, enableWebPagePreview }) {
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;
    const TELEGRAM_API = `https://api.telegram.org/bot${TOKEN}`;

    try {
      const res = await retry(
        async () => {
          const response = await axios.post(`${TELEGRAM_API}/sendMessage`, {
            chat_id: chatId,
            text: text,
            reply_markup: keyboard,
            parse_mode: "HTML",
            disable_web_page_preview: !enableWebPagePreview,
          });

          if (response.data.ok) {
            return response;
          } else {
            throw new Error(
              `Error sending message: ${response.data.description}`
            );
          }
        },
        {
          retries: 5,
          minTimeout: 500,
          maxTimeout: 2000,
        }
      );

      sails.log.info(res.data);
      
      return {
        chat_id: res.data.result.chat.id,
        message_id: res.data.result.message_id,
      };
    } catch (error) {
      sails.log.error("Error sending message:", error);
      throw error;
    }
  },
};

