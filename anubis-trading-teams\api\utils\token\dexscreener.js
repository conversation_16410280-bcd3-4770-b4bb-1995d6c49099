const axios = require("axios");

const retry = require("async-retry");

async function getTokenData(contractAddress, chainId) {
  const url = `https://api.dexscreener.com/token-pairs/v1/${chainId}/${contractAddress}`;
  return retry(
    async () => {
      const response = await axios.get(url);
      sails.log.debug("DEX SCREENER DATA", response.data[0]);
      return response.data;
    },
    {
      retries: 3,
      minTimeout: 3000,
      onRetry: (err, attempt) => {
        sails.log.warn(
          `Failed to fetch token data from dexscreener for ${contractAddress} on chain ${chainId}, retrying (attempt ${attempt})`
        );
      },
    }
  ).catch((err) => {
    sails.log.error(
      `Failed to fetch token data from dexscreener for ${contractAddress} on chain ${chainId} after 3 attempts`
    );
    return null;
  });
}

 const dexscreenerChains = [
   "ethereum",
   "bsc", // Binance Smart Chain
   "polygon", // Matic
   "avalanche", // Avalanche C‑Chain
   "fantom",
   "arbitrum",
   "optimism",
   "base",
   "zksync",
   "linea",
   "scroll",
   "mantle",
   "blast",
   "moonbeam",
   "moonriver",
   "celo",
   "harmony",
   "cronos",
   "dogechain",
   "metis",
   "kava",
   "core",
   "opbnb",
   "pulsechain",
   "shibarium",
   "xdai", // Gnosis Chain
   "solana", // Non‑EVM
   "tron", // Non‑EVM
   "ton", // Non‑EVM
   "near", // Non‑EVM
   "aurora", // NEAR EVM layer
   "boba",
   "klaytn",
   "okxchain",
   "vela1", // Custom label sometimes seen
   "bitgert",
   "zora",
   "xai",
   "polygonzkevm",
   "canto",
   "velas",
   "berachain", // Added
   "unichain", // Added
 ];


function isValidChain(chain){
  return dexscreenerChains.includes(chain);
}

function isValidAddress(address){
  const regex = /^0x[a-fA-F0-9]{40}$/;
  return regex.test(address);
}

function stripURL(url){
  const stripSignedKey = (url) => url.split("?")[0];
  return stripSignedKey(url);
}

/**
 * Re‑hosts an image through images.weserv.nl so Telegram can fetch it.
 *
 * @param {string} src    Original image URL (http or https).
 * @param {Object} [params]  Optional Weserv query params, e.g. { w:256, h:256, fit:'cover' }.
 * @returns {string}      Fully‑formed Weserv proxy URL.
 *
 * Example:
 *   const url = weservProxy(
 *     'https://dd.dexscreener.com/ds-data/tokens/ethereum/0x95af...ce.png?key=0c97c5',
 *     { w: 128, h: 128, fit: 'cover' }
 *   );
 *   // → "https://images.weserv.nl/?url=dd.dexscreener.com/ds-data/tokens/ethereum/0x95af...ce.png&w=128&h=128&fit=cover"
 */
function weservProxy(src, params = {}) {
  if (typeof src !== 'string' || !src.startsWith('http')) {
    throw new TypeError('weservProxy: “src” must be a valid http/https URL string');
  }

  // 1️⃣  Parse once so we can manipulate parts safely
  const url = new URL(src);

  // 2️⃣  Drop DexScreener’s ?key=… signature (and any other query entirely)
  url.search = '';

  // 3️⃣  Remove protocol so Weserv gets “dd.dexscreener.com/…”
  const hostPath = `${url.host}${url.pathname}`.replace(/\/{2,}/g, '/');

  // 4️⃣  Build Weserv base
  const weserv = new URL('https://images.weserv.nl/');
  weserv.searchParams.set('url', hostPath);

  // 5️⃣  Append any extra Weserv parameters (width, height, etc.)
  Object.entries(params).forEach(([k, v]) => weserv.searchParams.set(k, v));

  return weserv.toString();
}

/**
 * Format token data for display in Telegram
 * @param {Object} tokenData - Token data from DexScreener
 * @returns {string} Formatted message string
 */
const formatTokenData = (tokenData) => {
  if (!tokenData) return '<b>No token data available</b>';

  const formatChange = (change) => {
    if (change === undefined || change === null) return 'N/A';
    const isPositive = change >= 0;
    const emoji = isPositive ? '🟢' : '🔴';
    const sign = isPositive ? '+' : '';
    return `<b>${sign}${parseFloat(change).toFixed(2)}% ${emoji}</b>`;
  };

  const capitalize = (s) => s.charAt(0).toUpperCase() + s.slice(1);


  const formatSocials = (socials) => {
    if (!socials?.length) return '• Socials: N/A\n';
    return (
      socials
        .map(
          (s) =>
            `• ${capitalize(s.type)}: <a href="${s.url}">${capitalize(
              s.type
            )}</a>`
        )
        .join("\n") + "\n"
    );
  };

  const formatWebsites = (websites) => {
    if (!websites?.length) return '• Website: N/A\n';
    return websites
      .map((w) => `• <a href="${w.url}">${w.label}</a>`)
      .join('\n') + '\n';
  };

  const priceChange = tokenData.priceChange || {};
  const volume = tokenData.volume || {};
  const txns = tokenData.txns?.h24 || {};
  const liquidity = tokenData.liquidity?.usd || 0;
  const buySellRatio = txns.buys / (txns.sells || 1);
  const chainName = tokenData.chainId ? tokenData.chainId.charAt(0).toUpperCase() + tokenData.chainId.slice(1) : 'N/A';
  const dexName = tokenData.dexId ? tokenData.dexId.charAt(0).toUpperCase() + tokenData.dexId.slice(1) : 'N/A';
  const createdDate = tokenData.pairCreatedAt ? new Date(tokenData.pairCreatedAt).toLocaleDateString() : 'N/A';

  return `
<b>${tokenData.baseToken?.name || 'N/A'} (${tokenData.baseToken?.symbol || 'N/A'})</b>

<code>Contract:</code> <code>${tokenData.baseToken?.address || 'N/A'}</code>
<code>Chain:</code> ${chainName}
<code>DEX:</code> ${dexName}

<b>🏦 Market Data</b>
<code>Price:</code> $${tokenData.priceUsd ? formatDeepDecimal(tokenData.priceUsd) : '0.00000000'}
<code>Market Cap:</code> $${tokenData.marketCap?.toLocaleString() || 'N/A'}
<code>Liquidity:</code> $${liquidity.toLocaleString()}

<b>📈 Price Change</b>
<code>5m:</code> ${formatChange(priceChange.m5)}
<code>1h:</code> ${formatChange(priceChange.h1)}
<code>6h:</code> ${formatChange(priceChange.h6)}
<code>24h:</code> ${formatChange(priceChange.h24)}

<b>📊 24h Stats</b>
<code>Volume:</code> $${volume.h24?.toLocaleString() || '0'}
<code>Buys:</code> ${txns.buys || 0} ✅
<code>Sells:</code> ${txns.sells || 0} ❌
<code>Buy/Sell Ratio:</code> ${buySellRatio.toFixed(2)}

<b>🔗 Links</b>
${formatWebsites(tokenData.info?.websites)}${formatSocials(tokenData.info?.socials)}

<code>Created:</code> ${createdDate}
<a href="${tokenData.url || '#'}">View on DexScreener ↗</a>
`.trim();
};

/**
 * Compresses long strings of fractional zeros by inserting a subscript
 * count after the first two zeros.
 *
 * 0.00000005  →  0.00₆5
 * 0.00001     →  0.00₄1
 *
 * Returns the original string unchanged if it doesn’t match the pattern.
 *
 * @param {number|string} value  A positive or negative decimal like 0.00000005
 * @returns {string}             The formatted string
 */
function formatDeepDecimal(value) {
  // Superscript/subscript digits 0‑9 in an array for quick lookup
  const subs = ['₀','₁','₂','₃','₄','₅','₆','₇','₈','₉'];

  // Normalise to string and strip leading + sign
  let str = (typeof value === 'number' ? value.toString() : String(value));
  const sign = str.startsWith('-') ? '-' : '';
  if (sign) str = str.slice(1);

  // Match 0.<zeros><non‑zero digits>, e.g. "0.00000005"
  const m = str.match(/^0\.(0+)([1-9]\d*)$/);
  if (!m) return sign + str;                 // leave numbers like 0.05 untouched

  const zeroCount = m[1].length;             // how many zeros after the dot
  const rest      = m[2];                    // the significant part

  // Convert the zeroCount to subscript digits
  const subCount = String(zeroCount)
    .split('')
    .map(d => subs[d])
    .join('');

  return `${sign}0.00${subCount}${rest}`;
}


module.exports = {
  getTokenData,
  isValidChain,
  isValidAddress,
  stripURL,
  weservProxy,
  formatTokenData,
  formatDeepDecimal,
  dexscreenerChains,
};
