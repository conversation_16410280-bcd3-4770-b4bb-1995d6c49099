/**
 * Extract the ID from a Telegram callback query context object
 * @param {string} input - the callback query context object
 * @returns {string} - the ID
 */
function extractConfigureId(input) {
  const configureRegex = /configure_(.*)/;
  const match = input.match(configureRegex);
  return match && match[1];
}

/**
 * Extract the Telegram group ID from a Telegram callback query context object
 * @param {string} input - the callback query context object
 * @returns {string} - the Telegram group ID
 */
function extractGroupId(input) {
  const configureRegex = /start_configuration_(.*)/;
  const match = input.match(configureRegex);
  return match && match[1];
}

/**
 * Extract the Telegram content creator ID from a Telegram callback query context object
 * @param {string} input - the callback query context object
 * @returns {string} - the Telegram content creator ID
 */
function extractContentCreatorId(input) {
  const split = input.split("_");
  return split[3];
}

/**
 * Extract the custom page ID from a Telegram callback query context object
 * @param {string} input - the callback query context object
 * @returns {string} - the custom page ID
 */
function extractCustomPageId(input) {
  const split = input.split("_");
  return split[3];
}

function extractIdAndAction(input) {
  const regex = /^configure_([\w]+)_set_(.+)$/;
  const match = input.match(regex);

  if (match) {
    const id = match[1];
    const action = `set_${match[2]}`; // Ensure the full action is captured
    return { id, action };
  } else {
    throw new Error("Input string does not match the expected format.");
  }
}

/**
 * Parses information from a Telegram callback query context object
 *
 * @param {Object} ctx - The context object from a Telegram callback query
 * @param {Object} ctx.update - The update object containing callback query data
 * @param {Object} ctx.update.callback_query - The callback query object
 * @param {Object} ctx.update.callback_query.from - The user who initiated the callback
 * @param {string} ctx.update.callback_query.from.id - The user's Telegram ID
 * @param {string} ctx.update.callback_query.from.username - The user's Telegram username
 * @param {string} ctx.update.callback_query.data - The callback data string
 * @returns {Object} An object containing parsed user information
 * @returns {string} return.id - The user's Telegram ID
 * @returns {string} return.username - The user's Telegram username
 * @returns {string} return.command - The callback data string
 */
function infoParseQuery(ctx) {
  return {
    id: ctx.update.callback_query.from.id,
    username: ctx.update.callback_query.from.username,
    command: ctx.update.callback_query.data,
  };
}

/**
 * Parses information from a Telegram text message context object
 *
 * @param {Object} ctx - The context object from a Telegram text message
 * @param {Object} ctx.message - The message object containing text data
 * @param {Object} ctx.message.chat - The chat object where message was sent
 * @param {string} ctx.message.chat.id - The chat's Telegram ID
 * @param {string} ctx.message.chat.username - The chat's Telegram username
 * @param {string} ctx.message.text - The message text content
 * @param {Array} ctx.message.photo - Array of photo objects if message contains photos
 * @returns {Object} An object containing parsed message information
 * @returns {string} return.id - The chat's Telegram ID
 * @returns {string} return.username - The chat's Telegram username
 * @returns {string} return.command - The message text content (empty string if no text)
 * @returns {Object} return.photo - The highest resolution photo object (empty object if no photo)
 */

function infoParseText(ctx) {
  return {
    id: ctx.message.chat.id,
    username: ctx.message.chat.username,
    command: ctx.message.text ? ctx.message.text : ctx.update.message.text,
    photo: ctx.message.photo
      ? ctx.message.photo[ctx.message.photo.length - 1]
      : {},
    groupMember:ctx.update.message.chat.id,
    type:ctx.update.message.chat.type
  };
}

function isSocialCommand(command) {
  const sociallist = [
    "website",
    "twitter",
    "instagram",
    "telegram",
    "tiktok",
    "discord",
    "coinmarketcap",
    "coingecko",
    "youtube",
  ];

  const split = command.split("_");
  const socialMedia = split[1];

  if(!sociallist.includes(socialMedia)) {
    return null;
  }

  return {
    type: socialMedia,
    id: split[2],
  };
}

module.exports = {
  extractConfigureId,
  extractGroupId,
  extractContentCreatorId,
  extractCustomPageId,
  extractIdAndAction,
  infoParseQuery,
  infoParseText,
  isSocialCommand,
};
