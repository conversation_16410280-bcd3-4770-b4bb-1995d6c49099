import { motion } from 'framer-motion';

export default function OpenPositions() {
    return (
        <div className="row-span-1 w-full p-3 min-h-[30%] overflow-auto scrollbar-hidden border-b border-white/20">
            <h5 className="font-space-grotesk text-xs font-bold">Open Positions</h5>
            <div className='flex flex-col gap-3 h-full justify-center items-center'>
                <p className="text-white/50">No open positions</p>
                <motion.button
                    className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-xs"
                    whileHover={{
                        boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                        scale: 1.05
                    }}
                    whileTap={{
                        backgroundColor: "#f0f0f0",
                        scale: 1.03
                    }}
                >
                    Trade or Deposit
                </motion.button>
            </div>
        </div>
    )
}