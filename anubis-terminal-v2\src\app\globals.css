@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-space-grotesk);
}

/* Font utility classes - using Next.js font variables */
.font-orbitron {
  font-family: var(--font-orbitron);
}

.font-space-grotesk {
  font-family: var(--font-space-grotesk);
}

/* Font weight utility classes */
.font-light {
  font-weight: 300;
}

.font-regular {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

/* Heading styles with Orbitron font */
.heading-primary {
  font-family: var(--font-orbitron);
  font-weight: 700;
  letter-spacing: 0.02em;
}

.heading-secondary {
  font-family: var(--font-orbitron);
  font-weight: 600;
  letter-spacing: 0.01em;
}

/* Body text styles with Space Grotesk font */
.body-text {
  font-family: var(--font-space-grotesk);
  font-weight: 400;
}

.body-text-bold {
  font-family: var(--font-space-grotesk);
  font-weight: 600;
}

/* Font size utility classes */
.text-display {
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
}

.text-h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  line-height: 1.2;
}

.text-h2 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  line-height: 1.3;
}

.text-h3 {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  line-height: 1.4;
}

.text-body-large {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.5;
}

.text-body {
  font-size: 1rem;
  line-height: 1.5;
}

.text-body-small {
  font-size: 0.875rem;
  line-height: 1.5;
}

.text-caption {
  font-size: 0.75rem;
  line-height: 1.5;
}

.debug {
  border: 1px solid red;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: #171717;
}

::-webkit-scrollbar-thumb {
  background: #fff;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Hide scrollbar but allow scrolling */
.scrollbar-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Floating icons positioning for Anubis Teams section */
.floating-icon-1 {
  top: 10%;
  left: 5%;
}

.floating-icon-2 {
  top: 20%;
  right: 10%;
}

.floating-icon-3 {
  bottom: 30%;
  left: 15%;
}

.floating-icon-4 {
  bottom: 20%;
  right: 5%;
}

/* Star shape for particles */
.clip-path-star {
  clip-path: polygon(
    50% 0%,
    61% 35%,
    98% 35%,
    68% 57%,
    79% 91%,
    50% 70%,
    21% 91%,
    32% 57%,
    2% 35%,
    39% 35%
  );
}
