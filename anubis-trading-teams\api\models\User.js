/**
 * User.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
  attributes: {
    chatId: {
      type: "number",
      description: "Telegram Chat ID for Tagging User in Communications",
      required: true,
      unique: true,
    },
    username: {
      type: "string",
      description: "User's Telegram Username",
    },
    firstName: {
      type: "string",
      description: "User's Telegram First Name",
    },
    mode: {
      type: "string",
      defaultsTo: "conversation",
      isIn: [
        "conversation",
        "set_group",
        "set_display_name",
        "set_custom_url_link",
        "set_page_logo",
        "set_page_banner",
        "set_page_bio",
        "set_page_theme",
        "set_page_color",
        "set_page_social_media",
        "set_page_favorite_tokens",
        "set_buy_token",
        "set_buy_token_chain",
        "set_buy_token_contract",
        "confirm_buy_token_contract",
        "set_page_favorite_tokens_chain",
        "set_page_favorite_tokens_address",
        "set_page_token_chain",
        "set_page_token_address",
      ],
    },
    currentConfiguredGroup: {
      type: "string",
      description: "ID of the currently configured group",
    },
    currentConfiguredService: {
      type: "string",
      description:
        "ID of the currenty configured service, custom page or buy token",
    },
    currentServiceType: {
      type: "string",
      description: "Current Service Type",
      isIn: ["content_creator", "token_page"],
    },
    group: {
      collection: "group",
      via: "owner",
    },
    twitterHandle: {
      type: "string",
      description: "Twitter Handle",
    },
  },
};
