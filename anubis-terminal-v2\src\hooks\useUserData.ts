import { useState, useEffect, useCallback } from 'react';
import { getUser, getWallets, getSettings, getNativeBalance, getUserPortfolio } from '@/services/bot';

// Cache configuration
const CACHE_KEY_PREFIX = 'anubis_user_data_';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface TokenData {
    address: string;
    name: string | null;
    symbol: string | null;
    balance: string;
    priceUsd: number | null;
    priceNative: number | null;
    fdv: number | null;
    liquidity: number | null;
    volume24h: number | null;
    priceChange24h: number | null;
    url: string | null;
}

interface Wallet {
    address: string;
    phrase?: string;
    privateKey?: string;
}

interface Settings {
    chainId: number;
    slippage: number;
    gasPrice: number;
    wallets: Wallet[];
    viewedOnboarding: boolean;
}

interface TradeValue {
    [chainId: string]: number;
}

interface UserData {
    user: {
        tgUserId: string;
        walletId: number;
        data: {
            tokens: string[];
            tradeValueInETH: TradeValue;
            referrals: any[];
            referralRewardInETH: TradeValue;
            snipeValueInETH: TradeValue;
            snipes: any[];
        };
        settings: Settings;
        token: string;
        portfolioPage: number;
        telegramUsername: string;
    };
    wallets: Wallet[];
    settings: Settings;
    balance: string;
    portfolio: TokenData[];
}

function getCacheKey(token: string): string {
    return `${CACHE_KEY_PREFIX}${token}`;
}

function getCachedUserData(token: string): UserData | null {
    try {
        const cacheKey = getCacheKey(token);
        const cachedData = localStorage.getItem(cacheKey);
        if (!cachedData) return null;

        const { data, timestamp } = JSON.parse(cachedData);
        const now = new Date().getTime();

        // Check if cache is still valid
        if (now - timestamp < CACHE_DURATION) {
            return data;
        }
    } catch (error) {
        console.error('Error reading from cache:', error);
    }
    return null;
}

function cacheUserData(token: string, data: UserData): void {
    try {
        const cacheKey = getCacheKey(token);
        const cacheData = {
            data,
            timestamp: new Date().getTime()
        };
        localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
        console.error('Error writing to cache:', error);
    }
}

export function useUserData(token: string | null) {
    const [data, setData] = useState<UserData | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    const fetchUserData = useCallback(async () => {
        console.log("Fetching user data...");
        if (!token) {
            setData(null);
            setError(null);
            return;
        }

        // Try to get data from cache first
        const cachedData = getCachedUserData(token);
        if (cachedData) {
            console.log("Using cached user data");
            setData(cachedData);
            setLoading(false);
            return;
        }

        setLoading(true);
        setError(null);
        
        try {
            const [userData, wallets, settings, balance, portfolio] = await Promise.all([
                getUser(token),
                getWallets(token),
                getSettings(token),
                getNativeBalance(token),
                getUserPortfolio(token)
            ]);
            
            const userDataResponse = { ...userData, ...wallets, ...settings, ...balance, ...portfolio };
            
            // Cache the response
            cacheUserData(token, userDataResponse);
            setData(userDataResponse);

        } catch (err: any) {
            console.error('Error fetching user data:', err);
            setError(err);
            setData(null);
        } finally {
            setLoading(false);
        }
    }, [token]);

    const refetch = useCallback(async () => {
        if (!token) return null;
        
        setLoading(true);
        setError(null);
        
        try {
            const [userData, wallets, settings, balance, portfolio] = await Promise.all([
                getUser(token),
                getWallets(token),
                getSettings(token),
                getNativeBalance(token),
                getUserPortfolio(token)
            ]);
            
            const userDataResponse = { ...userData, ...wallets, ...settings, ...balance, ...portfolio };
            
            // Update cache with fresh data
            cacheUserData(token, userDataResponse);
            setData(userDataResponse);
            return userDataResponse;
        } catch (err) {
            console.error('Error refetching user data:', err);
            setError(err as Error);
            return null;
        } finally {
            setLoading(false);
        }
    }, [token]);

    useEffect(() => {
        // Only set loading to true if we don't have cached data
        if (!token || !getCachedUserData(token)) {
            setLoading(true);
        }
        setError(null);
        fetchUserData();
    }, [token, fetchUserData]);

    return { data, loading, error, refetch };
}
