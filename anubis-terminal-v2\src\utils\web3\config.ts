import { createPublicClient, http } from 'viem'
import { avalanche, base, unichain, berachain, sonic } from 'viem/chains'

const systemClient = (chain: any) => {
    return createPublicClient({
        chain,
        transport: http()
    })
}

export const avalancheClient = systemClient(avalanche)
export const baseClient = systemClient(base)
export const unichainClient = systemClient(unichain)
export const berachainClient = systemClient(berachain)
export const sonicClient = systemClient(sonic)
