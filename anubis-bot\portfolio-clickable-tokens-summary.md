# Portfolio Enhancement: Clickable Token Names

## Overview

The portfolio display has been enhanced to make token names directly clickable, opening the trade inquiry flow for the selected token. This creates a more intuitive and streamlined user experience.

## Changes Made

### 1. Clickable Token Names in Portfolio

- Modified the portfolio display to render token names as clickable deep links
- Each token name now opens the trade inquiry flow when clicked
- Added a clear instruction: "Click on a token name to view details and trade options"

### 2. Deep Linking Implementation

- Created deep links using the format: `https://t.me/bot_username?start=token_address`
- Updated the start command handler to process token deep links
- Implemented token lookup and display logic when a deep link is clicked

### 3. Fixed Price Display Issues

- Enhanced price display logic to handle null, undefined, and "null" string values
- Implemented proper fallbacks to show "Price unavailable" instead of "$null"
- Added additional checks to ensure consistent price display

## Implementation Details

### Clickable Token Names in Portfolio

```typescript
// Extract token name from the info string
const match = tokenInfo.match(/🔹 ([^:]+):/);
let tokenName = match && match[1] ? match[1].trim() : `Token ${index + 1}`;

// Extract price and balance info
const priceMatch = tokenInfo.match(/: (.+) - (.+)$/);
const priceAndBalance = priceMatch ? priceMatch[0] : '';

// Create a deep link to open this token
const deepLink = `https://t.me/${ctx.me.username}?start=token_${token.replace('0x', '')}`;

// Add the token as a clickable link
messageLines.push(`🔹 [${tokenName}](${deepLink})${priceAndBalance}`);
```

### Deep Link Handler in Start Command

```typescript
// Check if this is a deep link with a token parameter
const startParam = ctx.message?.text?.split(' ')[1];

if (startParam && startParam.startsWith('token_')) {
  // Extract token address from the parameter
  const tokenAddressWithout0x = startParam.replace('token_', '');
  const tokenAddress = `0x${tokenAddressWithout0x}` as Address;

  // Find token index in the session data
  let tokenIndex = ctx.session.data.tokens.findIndex(t => t.toLowerCase() === tokenAddress.toLowerCase());

  // If token not found in session, add it
  if (tokenIndex === -1) {
    ctx.session.data.tokens.push(tokenAddress);
    tokenIndex = ctx.session.data.tokens.length - 1;
  }

  // Set the token in the session
  ctx.session.token = tokenIndex.toString();

  // Show token info with trading options
  const msg = await send(ctx, ["🔍 Loading token info..."]);
  if (msg !== true) {
    sendTokenInfo(
      ctx,
      {
        chat: { id: msg?.chat?.id },
        message_id: msg?.message_id ?? 0,
      } as Message,
      tokenAddress
    );
  }
  return;
}
```

### Improved Price Display Logic

```typescript
// Handle case when priceUsd is undefined, null, or "null" but priceNative is available
let priceDisplay;
if (tokenMeta.priceUsd !== undefined && tokenMeta.priceUsd !== null && tokenMeta.priceUsd !== "null") {
  priceDisplay = `$${tokenMeta.priceUsd}`;
} else if (tokenMeta.priceNative !== undefined && tokenMeta.priceNative !== null && tokenMeta.priceNative !== "null") {
  // Use priceNative and format it properly
  const priceNative = parseFloat(tokenMeta.priceNative);
  if (!isNaN(priceNative)) {
    priceDisplay = `$${priceNative.toFixed(6)}`;
  } else {
    priceDisplay = "Price unavailable";
  }
} else {
  priceDisplay = "Price unavailable";
}
```

## Benefits

1. **Improved User Experience**:
   - Direct access to token trading options with a single click
   - No need to copy and paste token addresses
   - Clear visual indication that token names are clickable

2. **Streamlined Workflow**:
   - Reduced steps to access token trading
   - Intuitive navigation from portfolio to trading
   - Faster access to token details

3. **Enhanced Functionality**:
   - Proper handling of all price display edge cases
   - Consistent user experience across different token types
   - Reliable deep linking implementation
