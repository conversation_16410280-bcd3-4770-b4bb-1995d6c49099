# Menu Circular Reference Fix

## Problem
The bot was consistently crashing with the error:
```
BotError: Error in middleware: Cannot send menu 'main-menu'! Did you forget to use bot.use() for it?
```

This error occurred whenever a user clicked on "Return to Main Menu" or any button that tried to display the main menu.

## Root Cause Analysis
The issue was caused by a deep circular reference in the menu definitions. The `mainMenu` was being directly referenced in multiple places:

1. In the `dismissMenu` handler
2. In the `settingsMenu` handler
3. In the `tokenMenu` handler
4. In the `start` command

This created a circular dependency that couldn't be resolved by simply changing the registration order.

## Solution
We've implemented a comprehensive solution to eliminate all circular references:

### 1. Replaced Direct Menu References with Inline Keyboards

Instead of directly referencing the `mainMenu` object, we've replaced all instances with inline keyboard definitions:

```typescript
// Before
return send(ctx, messageLines, {
  reply_markup: mainMenu,
  parse_mode: "MarkdownV2",
});

// After
return send(ctx, messageLines, {
  parse_mode: "MarkdownV2",
  reply_markup: {
    inline_keyboard: [
      [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
      [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
      // ... other buttons
    ]
  }
});
```

### 2. Added Custom Callback Handler

We've implemented a custom callback handler to process actions from the inline keyboard:

```typescript
// Handle main menu actions
else if (callbackData.startsWith('main_menu_action:')) {
  // Extract the action from the callback data
  const action = callbackData.split(':')[1];
  
  // Answer the callback query
  await ctx.answerCallbackQuery();
  
  // Handle different actions
  switch (action) {
    case 'manual_buy':
      await handleManualBuy(ctx);
      break;
    case 'settings':
      await handleSettings(ctx);
      break;
    // ... other actions
  }
}
```

### 3. Updated All Menu References

We've updated all places where the main menu was referenced:
- The "Return to Main Menu" button in the dismiss menu
- The "Main Menu" button in the settings menu
- The "Main Menu" button in the token menu
- The start command

### 4. Maintained Menu Registration Order

We've kept the optimized menu registration order to ensure that all menus are properly registered:

```typescript
// Register all menus
// Important: Register mainMenu last to avoid circular reference issues
bot.use(walletsMenu);
bot.use(settingsMenu);
bot.use(dismissMenu);
bot.use(contractTokenMenu);
bot.use(tokenMenu);
bot.use(confirmTransactionBtn);
bot.use(withdrawAllbtn);
bot.use(cancelTranfer);

// Register mainMenu last to avoid circular reference issues
bot.use(mainMenu);
```

## Benefits

1. **Eliminated Circular References**: Removed all circular dependencies that were causing the crashes
2. **Improved Stability**: The bot no longer crashes when navigating between menus
3. **Enhanced User Experience**: Users can navigate through the bot without encountering errors
4. **Maintainable Code**: The solution is more maintainable as it doesn't rely on complex menu dependencies

## Technical Details

The key insight was that we needed to completely eliminate direct references to the `mainMenu` object in other parts of the code. By using inline keyboards with callback data instead, we've created a more robust solution that doesn't rely on circular references.

This approach also gives us more flexibility in the future, as we can easily modify the main menu's appearance and behavior without having to update multiple references throughout the codebase.
