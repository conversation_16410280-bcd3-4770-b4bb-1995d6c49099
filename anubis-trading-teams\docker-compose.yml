version: '3.8'

services:
  anubis-teams-api:
    container_name: anubis-teams-api
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "1339:80"  # Map host port 1339 to container port 80
    environment:
      - NODE_ENV=production
      - PORT=80
      - MONGO_DB=${MONGO_DB_PROD}
      - ANUBIS_BEARER_TOKEN=${ANUBIS_BEARER_TOKEN}
    env_file:
      - .env
    # Remove volume mounts temporarily to test
    # volumes:
    #   - .:/app
    #   - /app/node_modules
    restart: unless-stopped
    networks:
      - anubis-network
    # Explicitly set the command
    command: ["npm", "start"]

networks:
  anubis-network:
    driver: bridge
    name: anubis-network
