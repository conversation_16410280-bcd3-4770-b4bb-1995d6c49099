"use client"
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function Hero() {
    return (
        <section className="mt-5 grid grid-cols-12 mx-auto py-5 px-4 md:px-8 lg:px-12 gap-2">
            <motion.div
                className="col-span-12 md:col-span-5 lg:col-span-4 flex justify-center items-center min-h-[40vh] md:min-h-[50vh] lg:min-h-[60vh] border border-white/10 rounded-md relative overflow-hidden cursor-pointer mb-6 md:mb-0"
                initial={{ background: "linear-gradient(to bottom right, #0a0a0a, #1a1a1a)" }}
                animate={{
                    background: [
                        "linear-gradient(to bottom right, #0a0a0a, #1a1a1a)",
                        "linear-gradient(to bottom right, #0f0f0f, #1f1f1f)",
                        "linear-gradient(to bottom right, #0a0a0a, #1a1a1a)"
                    ],
                }}
                transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut"
                }}
            >
                {/* Animated background glow */}
                <motion.div
                    className="absolute w-full h-full max-w-[300px] max-h-[300px] rounded-full bg-white/10 blur-3xl"
                    animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.2, 0.3, 0.2],
                    }}
                    transition={{
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut"
                    }}
                />

                {/* Animated logo with white glow */}
                <motion.div
                    className="relative z-10"
                    initial={{ y: 0, rotate: 0 }}
                    animate={{
                        y: [0, -15, 0],
                        rotate: [0, 5, 0, -5, 0],
                    }}
                    transition={{
                        y: {
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                        },
                        rotate: {
                            duration: 6,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }
                    }}
                >
                    <motion.img
                        src="/logo/icon_no_bg.png"
                        className="w-32 h-32 sm:w-40 sm:h-40 md:w-44 md:h-44 object-contain cursor-pointer drop-shadow-[0_0_32px_white]"
                        alt="Anubis Logo"
                        whileHover={{
                            scale: 1.07,
                            filter: "drop-shadow(0 0 48px white)",
                            transition: { duration: 0.3 }
                        }}
                        whileTap={{
                            scale: 0.9,
                            rotate: [0, -10, 10, -10, 0],
                            transition: { duration: 0.5 }
                        }}
                        animate={{
                            filter: [
                                "drop-shadow(0 0 16px white)",
                                "drop-shadow(0 0 32px white)",
                                "drop-shadow(0 0 16px white)"
                            ],
                        }}
                        transition={{
                            filter: {
                                duration: 3,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }
                        }}
                    />
                </motion.div>

                {/* Animated particles */}
                <AnimatedParticles />
            </motion.div>
            <motion.div
                className="col-span-12 md:col-span-7 lg:col-span-8 flex items-center min-h-[30vh] md:min-h-[50vh] lg:min-h-[60vh] bg-gradient-to-b from-[#fff]/10 to-[#fff]/5 rounded-md"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1 }}
            >
                <div className="space-y-4 sm:space-y-6 md:space-y-8 p-4 sm:p-6 md:p-8 w-full">
                    <motion.h3
                        className="font-orbitron uppercase font-bold text-xl sm:text-2xl md:text-4xl lg:text-6xl bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400 leading-tight drop-shadow-[0_0_16px_white]"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                    >
                        Snipe and Sell tokens<br className="hidden sm:block" /> on-chain instantly
                    </motion.h3>

                    <motion.p
                        className="font-space-grotesk text-gray-200 text-sm sm:text-base md:text-lg lg:text-xl max-w-2xl drop-shadow-[0_0_8px_white]"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.6 }}
                    >
                        Experience lightning-fast trading with Anubis, the most advanced on-chain trading terminal.
                    </motion.p>

                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.9 }}
                        className="pt-2 sm:pt-4"
                    >
                        <Link href="/login">
                            <motion.button
                                className="font-orbitron font-semibold bg-white text-black px-6 sm:px-8 py-2 sm:py-3 rounded-md text-sm sm:text-base hover:shadow-lg hover:shadow-white/20 w-full sm:w-auto drop-shadow-[0_0_16px_white]"
                                whileHover={{
                                    boxShadow: "0 0 32px white",
                                    scale: 1.05
                                }}
                                whileTap={{
                                    backgroundColor: "#f0f0f0",
                                    scale: 0.97
                                }}
                            >
                                Start Trading
                            </motion.button>
                        </Link>
                    </motion.div>
                </div>
            </motion.div>
        </section>
    )
}

// Animated particles component
function AnimatedParticles() {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        // Check if window is available (client-side)
        if (typeof window !== 'undefined') {
            // Set initial value
            setIsMobile(window.innerWidth < 768);

            // Add event listener for window resize
            const handleResize = () => {
                setIsMobile(window.innerWidth < 768);
            };

            window.addEventListener('resize', handleResize);

            // Clean up
            return () => {
                window.removeEventListener('resize', handleResize);
            };
        }
    }, []);

    // Create an array of particles with different types - fewer on mobile for better performance
    const particleCount = isMobile ? 10 : 20;

    const particles = Array.from({ length: particleCount }).map((_, i) => ({
        id: i,
        type: Math.random() > 0.7 ? 'star' : 'circle',
        opacity: Math.random() * 0.3 + 0.1, // Random opacity between 0.1 and 0.4
    }));

    return (
        <>
            {particles.map((particle) => {
                // Random position and size for each particle - smaller on mobile
                const sizeFactor = isMobile ? 0.7 : 1;
                const size = (Math.random() * 5 + (particle.type === 'star' ? 4 : 2)) * sizeFactor;
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const duration = Math.random() * 15 + (isMobile ? 15 : 10); // Slower on mobile for better performance
                const delay = Math.random() * 5;

                // Set opacity based on particle property
                const colorClass = `bg-white/[${particle.opacity}]`;

                // Different shapes based on particle type
                const shapeClass = particle.type === 'star'
                    ? 'clip-path-star'
                    : 'rounded-full';

                return (
                    <motion.div
                        key={particle.id}
                        className={`absolute ${colorClass} ${shapeClass}`}
                        style={{
                            width: size,
                            height: size,
                            left: `${x}%`,
                            top: `${y}%`,
                            filter: 'drop-shadow(0 0 8px white)',
                        }}
                        animate={{
                            y: [0, -10, 0],
                            opacity: [particle.opacity, particle.opacity * 1.2, particle.opacity],
                        }}
                        transition={{
                            duration,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay
                        }}
                    />
                );
            })}
        </>
    );
}