import {
  AlphaRouter,
  SwapOptions,
  SwapOptionsSwapRouter02,
  SwapRoute,
  SwapType,
} from "@uniswap/smart-order-router";
import {
  TradeType,
  Currency,
  CurrencyAmount,
  Percent,
  Token,
} from "@uniswap/sdk-core";
import { Address, parseUnits } from "viem";
import { getProvider } from "./provider";
import { getGeckoTokenInfo } from "./gecko";
import { ESupportedChains } from "../config";

export async function getTokens(chainId: number, addresses: Address[]) {
  const { data: tokens } = await getGeckoTokenInfo(addresses, chainId);
  return (
    tokens?.map(
      (token) =>
        new Token(
          chainId,
          token.attributes.address,
          token.attributes.decimals,
          token.attributes.symbol,
          token.attributes.name
        )
    ) ?? []
  );
}

export async function generateUniversalRoute(
  tokenIn: Address,
  amountIn: number,
  tokenOut: Address,
  to: Address,
  chainId: number
) {
  const options: SwapOptions = {
    recipient: to,
    slippageTolerance: new Percent(50, 10_000),
    type: SwapType.UNIVERSAL_ROUTER,
  };

  const [tokenFrom, tokenTo] = await getTokens(chainId, [tokenIn, tokenOut]);

  if (!tokenFrom || !tokenTo) {
    return null;
  }

  return generateRouteWithOptions(
    tokenFrom,
    amountIn,
    tokenTo,
    chainId,
    options
  );
}

export async function generateV2Route(
  tokenIn: Address,
  amountIn: number,
  tokenOut: Address,
  to: Address,
  chainId: number
) {
  const options: SwapOptionsSwapRouter02 = {
    recipient: to,
    slippageTolerance: new Percent(50, 10_000),
    deadline: Math.floor(Date.now() / 1000 + 1800),
    type: SwapType.SWAP_ROUTER_02,
  };

  const [tokenFrom, tokenTo] = await getTokens(chainId, [tokenIn, tokenOut]);

  if (!tokenFrom || !tokenTo) {
    return null;
  }

  return generateRouteWithOptions(
    tokenFrom,
    amountIn,
    tokenTo,
    chainId,
    options
  );
}

export async function generateRouteWithOptions(
  tokenFrom: Currency,
  amountIn: number,
  tokenTo: Currency,
  chainId: number,
  options: SwapOptions
): Promise<SwapRoute | null> {
  const router = new AlphaRouter({
    chainId,
    provider: getProvider(chainId),
  });

  const route = await router.route(
    CurrencyAmount.fromRawAmount(
      tokenFrom,
      parseUnits(amountIn.toString(), tokenFrom.decimals).toString()
    ),
    tokenTo,
    TradeType.EXACT_INPUT,
    options
  );

  return route;
}

interface MethodParameters {
  calldata: Address;
  value: Address;
  to: Address;
}

interface Quote {
  methodParameters: MethodParameters;
  blockNumber: string;
  amount: string;
  amountDecimals: string;
  quote: string;
  quoteDecimals: string;
  quoteGasAdjusted: string;
  quoteGasAdjustedDecimals: string;
  quoteGasAndPortionAdjusted: string;
  quoteGasAndPortionAdjustedDecimals: string;
  gasUseEstimateQuote: string;
  gasUseEstimateQuoteDecimals: string;
  gasUseEstimate: string;
  gasUseEstimateUSD: string;
  simulationStatus: string;
  simulationError: boolean;
  gasPriceWei: string;
  route: {
    type: "v3-pool";
    address: Address;
    tokenIn: {
      chainId: ESupportedChains;
      decimals: string;
      address: Address;
      symbol: string;
    };
    tokenOut: {
      chainId: ESupportedChains;
      decimals: string;
      address: Address;
      symbol: string;
    };
    fee: string;
    liquidity: string;
    sqrtRatioX96: string;
    tickCurrent: string;
    amountIn: string;
    amountOut: string;
  }[][];
  routeString: string;
  quoteId: string;
  hitsCachedRoutes: boolean;
  portionBips: number;
  portionRecipient: string;
  portionAmount: string;
  portionAmountDecimals: string;
  requestId: string;
  permitData: {
    domain: {
      name: "Permit2";
      chainId: ESupportedChains;
      verifyingContract: Address;
    };
    types: {
      PermitSingle: [
        { name: "details"; type: "PermitDetails" },
        { name: "spender"; type: "address" },
        { name: "sigDeadline"; type: "uint256" }
      ];
      PermitDetails: [
        { name: "token"; type: "address" },
        { name: "amount"; type: "uint160" },
        { name: "expiration"; type: "uint48" },
        { name: "nonce"; type: "uint48" }
      ];
    };
    values: {
      details: {
        token: Address;
        amount: string;
        expiration: string;
        nonce: string;
      };
      spender: Address;
      sigDeadline: string;
    };
  };
  tradeType: "EXACT_INPUT" | "EXACT_OUTPUT";
  slippage: number;
}

interface AllQuotes {
  routing: "CLASSIC" | "UNIVERSAL" | "MIXED";
  quote: Quote;
}

interface ResponseObject {
  routing: "CLASSIC" | "UNIVERSAL" | "MIXED";
  quote: Quote;
  requestId: string;
  allQuotes: AllQuotes[];
}

export async function getUniswapRawQuote(
  tokenInChainId: number,
  tokenIn: Address,
  tokenOutChainId: number,
  tokenOut: Address,
  amount: string,
  recipient: Address,
  swapper: Address
) {
  const requestConfig = {
    url: "https://interface.gateway.uniswap.org/v2/quote",
    method: "POST",
    headers: {
      accept: "*/*",
      "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
      "content-type": "text/plain;charset=UTF-8",
      dnt: "1",
      origin: "https://app.uniswap.org",
      priority: "u=1, i",
      referer: "https://app.uniswap.org/",
      "sec-ch-ua": '"Not/A)Brand";v="8", "Chromium";v="126"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "macOS",
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-site",
      "user-agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "x-request-source": "uniswap-web",
    },
    body: JSON.stringify({
      tokenInChainId,
      tokenIn,
      tokenOutChainId,
      tokenOut,
      amount,
      sendPortionEnabled: true,
      type: "EXACT_INPUT",
      intent: "quote",
      configs: [
        {
          enableUniversalRouter: true,
          protocols: ["V2", "V3", "MIXED"],
          routingType: "CLASSIC",
          recipient,
          enableFeeOnTransferFeeFetching: true,
        },
      ],
      useUniswapX: true,
      swapper,
    }),
  };

  try {
    const response = await fetch(requestConfig.url, {
      method: requestConfig.method,
      headers: requestConfig.headers,
      body: requestConfig.body,
    });
    return (await response.json()) as ResponseObject;
  } catch (error) {
    console.error(error);
    return undefined;
  }
}

export async function getUniswapQuote(
  tokenIn: Address,
  tokenOut: Address,
  amount: string,
  recipient: Address,
  chainId: number
) {
  const quote = await getUniswapRawQuote(
    chainId,
    tokenIn,
    chainId,
    tokenOut,
    amount,
    recipient,
    recipient
  );

  return quote?.quote;
}
