{"name": "anubis-terminal-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.9.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "ethers": "^6.14.3", "framer-motion": "^12.10.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.514.0", "next": "15.3.1", "react": "^19.0.0", "react-cookie": "^8.0.1", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-loader-spinner": "^6.1.6", "react-toastify": "^11.0.5", "react-use": "^17.6.0", "tailwind-merge": "^3.3.0", "viem": "^2.30.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}