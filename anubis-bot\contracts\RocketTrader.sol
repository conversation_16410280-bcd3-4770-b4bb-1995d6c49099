// SPDX-License-Identifier: MIT
pragma solidity 0.8.25;

import "@openzeppelin/contracts/access/Ownable.sol";

interface IERC20 {
    function transfer(
        address recipient,
        uint256 amount
    ) external returns (bool);

    function approve(address spender, uint256 amount) external returns (bool);

    function balanceOf(address account) external view returns (uint256);

    function allowance(
        address owner,
        address spender
    ) external view returns (uint256);
}

interface IRouteProcessor {
    function processRoute(
        address tokenIn,
        uint256 amountIn,
        address tokenOut,
        uint256 amountOutMin,
        address to,
        bytes memory routeCode
    ) external payable returns (uint256 amountOut);
}

contract RocketTrader is Ownable {
    uint8 private tax = 2;
    address private constant NATIVE_TOKEN =
        ******************************************;
    address private treasuryWallet = ******************************************;

    uint private unlocked = 1;
    modifier lock() {
        require(unlocked == 1, "RouteProcessor is locked");
        unlocked = 2;
        _;
        unlocked = 1;
    }

    constructor() Ownable(msg.sender) {}

    /// @notice For native unwrapping
    receive() external payable {}

    function processRoute(
        address routeAddress,
        address tokenIn,
        uint256 amountIn,
        address tokenOut,
        uint256 amountOutMin,
        address to,
        bytes memory routeCode
    ) external payable lock returns (uint256 amountOut) {
        // Get the pool contract
        IRouteProcessor routeProcessor = IRouteProcessor(routeAddress);

        if (tokenIn == NATIVE_TOKEN) {
            uint256 taxAmount = calculateTax(amountIn);
            require(msg.value >= amountIn + taxAmount, "N-IFP");
            // If the tokenIn is the native token
            // Transfer the tax amount to the treasury wallet
            payable(treasuryWallet).transfer(msg.value - amountIn);

            // Process the route
            amountOut = routeProcessor.processRoute{value: amountIn}(
                tokenIn,
                amountIn,
                tokenOut,
                amountOutMin,
                to,
                routeCode
            );
        } else {
            // If the tokenIn is not the native token
            uint256 balance = IERC20(tokenIn).balanceOf(msg.sender);
            uint256 taxAmount = calculateTax(amountIn);
            require(balance >= amountIn + taxAmount, "T-IFP");

            uint256 allowance = IERC20(tokenIn).allowance(
                msg.sender,
                address(this)
            );
            require(allowance >= amountIn + taxAmount, "T-IFA");
            // Transfer the tax amount to the treasury wallet
            bool taxed = IERC20(tokenIn).transfer(treasuryWallet, taxAmount);
            require(taxed, "T-TTF");

            // Approve routeAddress to spend amountIn
            bool approval = IERC20(tokenIn).approve(
                routeAddress,
                amountIn + taxAmount
            );
            require(approval, "T-ATS");

            // Process the route
            amountOut = routeProcessor.processRoute(
                tokenIn,
                amountIn,
                tokenOut,
                amountOutMin,
                to,
                routeCode
            );
        }
    }

    function setTax(uint8 _tax) external onlyOwner {
        tax = _tax;
    }

    function setTreasuryWallet(address _treasuryWallet) external onlyOwner {
        treasuryWallet = _treasuryWallet;
    }

    function calculateTax(uint256 amount) private view returns (uint256) {
        // Calculate the tax amount based on the swapped amounts
        return (amount * tax) / 100; // 2% tax
    }
}
