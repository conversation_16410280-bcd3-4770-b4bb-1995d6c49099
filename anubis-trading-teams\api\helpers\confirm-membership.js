const axios = require("axios");

module.exports = {
  friendlyName: "Confirm membership",

  description: "",

  inputs: {
    groupChatId: {
      type: "string",
      description: "Chat ID",
      required: true,
    },
    botId: {
      type: "string",
      description: "Bot ID",
      required: true,
    },
  },

  exits: {
    isMember: {
      description: "User is a member of the group",
      outputType: "boolean",
    },
  },

  fn: async function (inputs) {
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;
    const TELEGRAM_API = `https://api.telegram.org/bot${TOKEN}`;

    try {
      const response = await axios.post(`${TELEGRAM_API}/getChatMember`, {
        chat_id: inputs.groupChatId,
        user_id: inputs.botId,
      });
      sails.log.info("Chat member info:", response.data);
      return response.data.result.status === "member";
    } catch (error) {
      sails.log.error("Error getting chat member:", error.message);
      if (error.response) {
        sails.log.error("Error response data:", error.response.data);
      }
      return false;
    }
  },
};