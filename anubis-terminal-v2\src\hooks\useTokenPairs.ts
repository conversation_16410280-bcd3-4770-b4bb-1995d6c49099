import axios, { AxiosError } from 'axios';
import { useState, useCallback, useEffect } from 'react';
import { getTokenPairs } from '../services/api/dexscreener';
import { getTokenData } from '@/services/api/flooz';

type UseTokenPairsReturn = {
    data: any | null;
    tokenData: any | null;
    loading: boolean;
    error: AxiosError | null;
    fetchTokenPairs: () => Promise<void>;
};

export const useTokenPairs = (tokenAddress: `0x${string}`, network: string): UseTokenPairsReturn => {
    const [data, setData] = useState<any | null>(null);
    const [tokenData, setTokenData] = useState<any | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<AxiosError | null>(null);

    const fetchTokenPairs = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const allResults = await Promise.all([
                getTokenData(network, tokenAddress),
                getTokenPairs(tokenAddress, network),
            ]);
            setData(allResults[1]);
            setTokenData(allResults[0].results[0]);
        } catch (error) {
            setError(error as AxiosError);
            setData(null);
        } finally {
            setLoading(false);
        }
    }, [tokenAddress, network]);

    useEffect(() => {
        if (tokenAddress && network) {
            fetchTokenPairs();
        }
    }, [fetchTokenPairs]);

    return {
        data,
        tokenData,
        loading,
        error,
        fetchTokenPairs,
    };
}; 