"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import AnimatedBackground from "@/components/ui/AnimatedBackground";
import GlitchText from "@/components/ui/GlitchText";
import { useRouter } from "next/navigation";
import { terminalAuth } from "@/services/bot";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "react-toastify";
import { useParams } from "next/navigation";

export default function VerifyOtpPage() {
    const params = useParams();
    const tgId = (params as { tgId: string }).tgId;
    const [otp, setOtp] = useState("");
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState("");
    const [success, setSuccess] = useState(false);
    const { push } = useRouter();
    const { storeCookie } = useAuth();

    useEffect(() => {
        document.title = "🔒 Enter Your Secure Code | Anubis Terminal Access";
    }, [tgId])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (/^\d{0,6}$/.test(e.target.value)) {
            setOtp(e.target.value);
            setError("");
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError("");
        setSuccess(false);
        try {
            const data = await terminalAuth(otp, tgId as string);
            storeCookie(data.token);
            setSuccess(true);
            setTimeout(() => {
                push("/terminal/trending")
            }, 1000);
            toast.success("OTP Verified! Redirecting...");
        } catch (error: any) {
            console.error(error);
            setError("Failed to verify OTP. Please try again.");
            if (error && error.response) {
                toast.error(error.response.data.message);
                return;
            }
            toast.error("Failed to verify OTP. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden px-4">
            <AnimatedBackground className="z-0" />
            <motion.div
                className="relative z-10 flex flex-col items-center justify-center gap-8 max-w-4xl mx-auto text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
            >
                <motion.div
                    initial={{ y: -50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 260, damping: 20, delay: 0.2 }}
                    className="mb-6 relative"
                >
                    <motion.div
                        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full bg-white/5 blur-xl"
                        animate={{ scale: [1, 1.2, 1], opacity: [0.3, 0.5, 0.3] }}
                        transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                    />
                    <motion.div
                        animate={{ y: [0, -10, 0] }}
                        transition={{ y: { duration: 3, repeat: Infinity, ease: "easeInOut" } }}
                    >
                        <motion.img
                            src="/logo/icon_no_bg.png"
                            alt="Anubis Logo"
                            className="w-32 h-32 object-contain mx-auto relative z-10"
                            animate={{
                                filter: [
                                    "drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))",
                                    "drop-shadow(0 0 20px rgba(255, 255, 255, 0.5))",
                                    "drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))",
                                ]
                            }}
                            transition={{ filter: { duration: 3, repeat: Infinity, ease: "easeInOut" } }}
                        />
                    </motion.div>
                </motion.div>
                <GlitchText text="Verify OTP to Access Terminal" className="text-2xl md:text-4xl mb-2" />
                <motion.div
                    className="w-full max-w-md bg-black/50 border border-white/20 rounded-md p-6 font-mono text-sm text-left shadow-lg"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                >
                    <form onSubmit={handleSubmit} className="flex flex-col gap-6">
                        <div>
                            <label htmlFor="otp" className="block text-white/80 font-space-grotesk mb-2 text-base">
                                Enter the 6-digit OTP sent to your Telegram
                            </label>
                            <input
                                id="otp"
                                type="text"
                                inputMode="numeric"
                                autoComplete="one-time-code"
                                maxLength={6}
                                value={otp}
                                onChange={handleChange}
                                disabled={loading || success}
                                className="w-full px-4 py-3 rounded-md bg-black/70 border border-white/20 text-white text-xl tracking-widest font-mono focus:outline-none focus:ring-2 focus:ring-white/30 transition-all placeholder:text-white/30 text-center"
                                placeholder="------"
                            />
                        </div>
                        {error && <div className="text-red-500 text-sm font-medium text-center">{error}</div>}
                        {success && <div className="text-green-500 text-sm font-medium text-center">OTP Verified! Redirecting...</div>}
                        <button
                            type="submit"
                            disabled={loading || otp.length !== 6 || success}
                            className="w-full font-orbitron font-semibold bg-white text-black py-3 rounded-md shadow hover:shadow-white/20 transition-all text-lg disabled:opacity-60 disabled:cursor-not-allowed"
                        >
                            {loading ? "Verifying..." : "Verify & Access Terminal"}
                        </button>
                    </form>
                </motion.div>
            </motion.div>
        </div>
    )
}

