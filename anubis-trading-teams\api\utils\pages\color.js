const { infoParseText } = require("../lib");

/**
 * Validates if a string is a valid hexadecimal color code
 *
 * @param {string} color - The color string to validate
 * @returns {boolean} True if valid hex color, false otherwise
 */
function isValidHexColor(color) {
  // Regular expression to match hex color codes
  // Supports both 3-digit (#RGB) and 6-digit (#RRGGBB) formats
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;

  // Test if the input matches the regex pattern
  return hexColorRegex.test(color);
}

async function handleColorUpdate(pageId, ctx) {
  const { id: chatId, command } = infoParseText(ctx);

  if (!isValidHexColor(command)) {
    await sails.helpers.sendMessage(
      chatId,
      `${command} is not a valid hexadecimal value\nUse hexadecimal values, e.g. #5E38F4 or type ‘cancel’ or ‘delete`
    );

    return;
  }

  const updatedRecord = await Page.updateOne({ id: pageId }).set({
    pageColor: command,
  });

  await sails.helpers.sendMessage(
    chatId,
    `Successfully set page accent color to ${updatedRecord.pageColor}`
  );
}

module.exports = { handleColorUpdate };
