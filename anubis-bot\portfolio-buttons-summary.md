# Portfolio Improvements with Interactive Buttons

## Overview

The portfolio display has been enhanced to provide a more interactive and user-friendly experience. Instead of requiring users to paste token addresses, we've implemented clickable buttons for each token that directly open the trade inquiry flow.

## Changes Made

### 1. Modified Portfolio Display

- Removed the "Paste a token address to view more details" instruction
- Added interactive buttons for each token in the portfolio
- Added an "Add New Token" button for easy portfolio expansion

### 2. Implemented Callback Handlers

- Added a callback query handler to process button clicks
- Created a trade flow that opens when a token button is clicked
- Implemented an add token flow for the "Add New Token" button

### 3. Improved Token Information Extraction

- Enhanced the token name extraction from portfolio data
- Ensured proper display of token names in the buttons

## Implementation Details

### Portfolio Display with Buttons

```typescript
// Create inline keyboard with buttons for each token
const inlineKeyboard = [];

// Add a row for each token
ctx.session.data.tokens.forEach((token, index) => {
  // Get token name or address for display
  let tokenName = `Token ${index + 1}`;
  try {
    // Try to find the token name in the tokensWithInfo array
    const tokenInfo = tokensWithInfo[index];
    if (tokenInfo) {
      // Extract token name from the info string
      const match = tokenInfo.match(/🔹 ([^:]+):/);
      if (match && match[1]) {
        tokenName = match[1].trim();
      }
    }
  } catch (error) {
    console.error("Error getting token name:", error);
  }
  
  // Add a button for this token
  inlineKeyboard.push([{
    text: `Trade ${tokenName}`,
    callback_data: `trade::${index}`
  }]);
});

// Add a row with a button to add a new token
inlineKeyboard.push([{
  text: "Add New Token",
  callback_data: "add_token"
}]);
```

### Callback Query Handler

```typescript
// Add handler for callback queries
bot.on('callback_query:data', async (ctx) => {
  const callbackData = ctx.callbackQuery.data;
  
  // Handle trade button clicks
  if (callbackData.startsWith('trade::')) {
    // Extract token index from callback data
    const tokenIndex = parseInt(callbackData.split('::')[1]);
    if (isNaN(tokenIndex) || tokenIndex < 0 || tokenIndex >= ctx.session.data.tokens.length) return;
    
    const tokenAddress = ctx.session.data.tokens[tokenIndex];
    
    // Set the token in the session
    ctx.session.token = tokenIndex.toString();
    
    // Answer the callback query to stop loading indicator
    await ctx.answerCallbackQuery();
    
    // Show token info with trading options
    const msg = await send(ctx, ["🔍 Loading token info..."]);
    if (msg !== true) {
      sendTokenInfo(
        ctx,
        {
          chat: { id: msg?.chat?.id },
          message_id: msg?.message_id ?? 0,
        } as Message,
        tokenAddress
      );
    }
  }
  // Handle add token button
  else if (callbackData === 'add_token') {
    // Answer the callback query
    await ctx.answerCallbackQuery();
    
    // Show message asking for token address
    await send(ctx, [
      "🔍 Add New Token",
      "",
      "Please paste a token contract address:"
    ]);
  }
});
```

## Benefits

1. **Improved User Experience**:
   - No need to copy and paste token addresses
   - One-click access to token trading options
   - Intuitive interface with clear action buttons

2. **Streamlined Workflow**:
   - Reduced steps to trade tokens
   - Easier portfolio management
   - Faster access to token information

3. **Enhanced Functionality**:
   - Quick addition of new tokens
   - Clear visual indication of available actions
   - Consistent interaction pattern throughout the bot
