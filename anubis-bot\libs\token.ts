import {
  Address,
  erc20<PERSON>bi,
  getC<PERSON><PERSON>t,
  createPublic<PERSON><PERSON>,
  formatUnits,
  http,
  WalletClient,
  parseUnits,
} from "viem";
import { MaxUint256 } from "@uniswap/sdk-core";
import { ESupportedChains, getConfig } from "../config";

// Cache for token balances
const balanceCache = new Map<string, any>();
const BALANCE_CACHE_TTL = 30 * 1000; // 30 seconds

// Cache for token contract info
const tokenContractInfoCache = new Map<string, any>();
const TOKEN_CONTRACT_INFO_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Cache for token decimals
const tokenDecimalsCache = new Map<string, any>();
const TOKEN_DECIMALS_CACHE_TTL = 10 * 60 * 1000; // 10 minutes

// Client cache to avoid creating new clients for each request
const clientCache = new Map<ESupportedChains, any>();

// Function to get a cached client
function getCachedClient(chainId: ESupportedChains) {
  if (clientCache.has(chainId)) {
    return clientCache.get(chainId);
  }

  const client = createPublicClient({
    chain: getConfig(chainId).chain,
    transport: http(),
  });

  clientCache.set(chainId, client);
  return client;
}

export async function getBalance(
  wallet: Address,
  address: Address,
  chainId: ESupportedChains
): Promise<string> {
  // Create a cache key
  const cacheKey = `balance_${chainId}_${wallet}_${address}`;

  // Check if we have a valid cached response
  const cachedResponse = balanceCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - BALANCE_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached balance for ${address}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching balance for ${address}`);
  const client = getCachedClient(chainId);

  let result;
  if (!!address.match(getConfig(chainId).nativeCurrencyAddress)) {
    const balance = await client.getBalance({ address: wallet });
    result = formatUnits(balance, 18).slice(0, 6);
  } else {
    const contract = getContract({ address, abi: erc20Abi, client });
    const balance = await contract.read
      .balanceOf([wallet])
      .catch(() => undefined);
    if (!balance) {
      result = "0";
    } else {
      // Get cached decimals or fetch them
      let decimals;
      const decimalsKey = `decimals_${chainId}_${address}`;
      const cachedDecimals = tokenDecimalsCache.get(decimalsKey);

      if (cachedDecimals && cachedDecimals.timestamp > Date.now() - TOKEN_DECIMALS_CACHE_TTL) {
        decimals = cachedDecimals.data;
      } else {
        decimals = Number((await contract.read.decimals()) ?? 18);
        tokenDecimalsCache.set(decimalsKey, {
          data: decimals,
          timestamp: Date.now()
        });
      }

      result = formatUnits(balance, decimals).slice(0, 6);
    }
  }

  // Cache the result
  balanceCache.set(cacheKey, {
    data: result,
    timestamp: Date.now()
  });

  return result;
}

export async function approveTokenAllowance(
  token: Address,
  spender: Address,
  amount: bigint,
  wallet: WalletClient,
  chainId: ESupportedChains
): Promise<void> {
  const client = getCachedClient(chainId);
  const allowance = await client.readContract({
    abi: erc20Abi,
    address: token,
    functionName: "allowance",
    args: [wallet.account?.address!, spender],
  });
  if (allowance < amount) {
    await wallet.writeContract({
      address: token,
      abi: erc20Abi,
      functionName: "approve",
      args: [spender, amount],
      chain: getConfig(chainId).chain,
      account: wallet.account!,
    });
  }
}

export async function getTokenContractInfo(
  token: Address,
  chainId: ESupportedChains
) {
  // Create a cache key
  const cacheKey = `token_info_${chainId}_${token}`;

  // Check if we have a valid cached response
  const cachedResponse = tokenContractInfoCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - TOKEN_CONTRACT_INFO_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached token info for ${token}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching token info for ${token}`);
  const client = getCachedClient(chainId);
  const contract = getContract({ address: token, abi: erc20Abi, client });

  const name = await contract.read.name().catch(() => "");
  const symbol = await contract.read.symbol().catch(() => "");
  if (!name || !symbol) {
    return undefined;
  }

  // Get cached decimals or fetch them
  let decimals;
  const decimalsKey = `decimals_${chainId}_${token}`;
  const cachedDecimals = tokenDecimalsCache.get(decimalsKey);

  if (cachedDecimals && cachedDecimals.timestamp > Date.now() - TOKEN_DECIMALS_CACHE_TTL) {
    decimals = cachedDecimals.data;
  } else {
    decimals = Number((await contract.read.decimals()) ?? 18);
    tokenDecimalsCache.set(decimalsKey, {
      data: decimals,
      timestamp: Date.now()
    });
  }

  const result = { name, symbol, decimals };

  // Cache the result
  tokenContractInfoCache.set(cacheKey, {
    data: result,
    timestamp: Date.now()
  });

  return result;
}

export async function getTokenContractDecimal(
  token: Address,
  chainId: ESupportedChains
) {
  if (token === getConfig(chainId).nativeCurrencyAddress) {
    return 18;
  }

  // Create a cache key
  const cacheKey = `decimals_${chainId}_${token}`;

  // Check if we have a valid cached response
  const cachedResponse = tokenDecimalsCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - TOKEN_DECIMALS_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached token decimals for ${token}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching token decimals for ${token}`);
  const client = getCachedClient(chainId);
  const contract = getContract({ address: token, abi: erc20Abi, client });
  const decimals = Number((await contract.read.decimals()) ?? 18);

  // Cache the result
  tokenDecimalsCache.set(cacheKey, {
    data: decimals,
    timestamp: Date.now()
  });

  return decimals;
}


export async function transferNativeToken(
  amount: string | number,
  toAddress: Address,
  wallet: WalletClient,
  chainId: ESupportedChains
): Promise<Address> {
  const userConfig = getConfig(chainId);
  const client = getCachedClient(chainId);

  try {
    // Get sender's balance first
    const senderBalance = await client.getBalance({
      address: wallet.account!.address,
    });

    // Handle the special "withdraw-all" case
    let amountBigInt: bigint;
    if (amount === "withdraw-all") {
      // We'll calculate the actual amount later after determining gas fees
      amountBigInt = senderBalance; // Temporary value
    } else {
      // Normal case - parse the amount
      amountBigInt = parseUnits(amount.toString(), 18);
    }
    // Get current gas price
    const gasPrice = await client.getGasPrice();
    const gasPriceWithBuffer = (gasPrice * BigInt(120)) / BigInt(100); // 20% buffer

    // Set a fixed gas limit for native transfers (21000 is standard for ETH transfers)
    const gasLimit = BigInt(21000);

    // Calculate gas fee
    const gasFee = gasLimit * gasPriceWithBuffer;

    // Calculate final amount to transfer
    let finalAmount: bigint;
    if (amount === "withdraw-all") {
      // For withdraw-all, subtract gas fee from balance
      finalAmount = senderBalance - gasFee;
      console.log(`Adjusted "withdraw-all" amount to leave gas fee: ${formatUnits(finalAmount, 18)} ${userConfig.chain.nativeCurrency.symbol}`);
    } else {
      finalAmount = amountBigInt;

      // Calculate total cost (amount + gas fees)
      const totalCost = finalAmount + gasFee;

      // Ensure sender has enough balance
      if (senderBalance < totalCost) {
        const shortfall = formatUnits(totalCost - senderBalance, 18);
        throw new Error(
          `Insufficient funds. You need an additional ${shortfall} ${userConfig.chain.nativeCurrency.symbol} to cover the transfer and gas fees.`
        );
      }
    }

    // Make sure we're not trying to send a negative or zero amount
    if (finalAmount <= BigInt(0)) {
      throw new Error(
        `Insufficient funds to cover gas fees. Cannot complete the transfer.`
      );
    }

    // Execute the transfer with explicit gas parameters
    const txHash = await wallet.sendTransaction({
      to: toAddress,
      value: finalAmount,
      account: wallet.account!,
      chain: userConfig.chain,
      gas: gasLimit,
      gasPrice: gasPriceWithBuffer
    });

    // Wait for transaction confirmation
    const receipt = await client.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60_000, // 1-minute timeout
    });

    if (receipt.status === "reverted") {
      throw new Error("Transaction reverted");
    }

    return txHash;
  } catch (error: any) {
    console.error("Transfer error details:", error);
    throw new Error(
      error?.details ?? error?.message ?? "Transfer failed. Please check your balance and gas fees."
    );
  }
}
