"use client";
import { FaCircle } from "react-icons/fa6";
import { motion } from "framer-motion";

interface FeedStatusType {
    status: "LIVE" | "OFFLINE" | "UPDATING";
}


export default function FeedStatus({ status }: FeedStatusType) {
    const statusMap = {
        LIVE: <div className="flex gap-x-1 items-center">
            <motion.span
                animate={{ scale: [0.8, 1.1, 0.8] }}
                transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
            >
                <FaCircle className="text-green-500 text-xs" />
            </motion.span>
            <span className="text-green-500 text-xs">LIVE</span>
        </div>,
        OFFLINE: <div className="flex gap-x-1 items-center">
            <motion.span
            >
                <FaCircle className="text-red-500 text-xs" />
            </motion.span>
            <span className="text-red-500 text-xs">OFFLINE</span>
        </div>,
        UPDATING: <div className="flex gap-x-1 items-center">
            <motion.span
                animate={{ scale: [0.8, 1.1, 0.8] }}
                transition={{ duration: 1.2, repeat: Infinity, ease: "easeInOut" }}
            >
                <FaCircle className="text-yellow-500 text-xs" />
            </motion.span>
            <span className="text-yellow-500 text-xs">UPDATING</span>
        </div>
    };

    return statusMap[status];
}