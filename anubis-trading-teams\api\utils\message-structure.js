const botUsername = sails.config.custom.botusername;
const tradingBotUsername = sails.config.custom.tradingBotUsername;
const terminalUrl = sails.config.custom.terminalUrl;

const mainMenu = async (userId) => {
  const groups = await Group.find({ owner: userId });

  if (groups.length === 0) {
    return {
      inline_keyboard: [
        [
          {
            text: "➕ Add the Anubis Teams Bot to a new Group",
            url: `https://t.me/${botUsername}?startgroup=${userId}`,
          },
        ],
        [
          {
            text: "Need help? Click here",
            url: "https://anubistrade.xyz/help",
          },
        ],
        [
          {
            text: "Go to Trading Bot",
            url: `https://t.me/${tradingBotUsername}`,
          },
        ],
      ],
    };
  } else {
    const mappedGroupButtons = groups.map((group) => {
      return [
        {
          text: group.groupTitle,
          callback_data: `start_configuration_${group.id}`,
        },
      ];
    });

    return {
      inline_keyboard: [
        ...mappedGroupButtons,
        [
          {
            text: "View All Groups",
            callback_data: "get_groups",
          },
        ],
        [
          {
            text: "➕ Add the Anubis Teams Bot to a new Group",
            url: `https://t.me/${botUsername}?startgroup=${userId}`,
          },
        ],
        [
          {
            text: "Need help? Click here",
            url: "https://anubistrade.xyz/help",
          },
        ],
        [
          {
            text: "Go to Trading Bot",
            url: `https://t.me/${tradingBotUsername}`,
          },
        ],
      ],
    };
  }
};

const service_configuration_msg = (id) => {
  return {
    inline_keyboard: [
      [
        {
          text: "Add Buy Token",
          callback_data: `add_buy_token_${id}`,
        },
      ],
      [
        {
          text: "I am a Content Creator",
          callback_data: `get_content_creator_${id}`,
        },
      ],
      [
        {
          text: "Get your Custom Token page",
          callback_data: `get_custom_page_${id}`,
        },
      ],
    ],
  };
};

const page_update_msg = (id, theme = "dark", pageType = "content_creator") => {
  if (pageType === "content_creator") {
    return {
      inline_keyboard: [
        [
          {
            text: "👤 Update Name",
            callback_data: `set_display_name_${id}`,
          },
          {
            text: "🔗 Get Link",
            callback_data: `set_custom_url_link_${id}`,
          },
        ],
        [
          {
            text: "🖌 Set Logo",
            callback_data: `set_page_logo_${id}`,
          },
          {
            text: "🖼 Set Banner",
            callback_data: `set_page_banner_${id}`,
          },
        ],
        [
          {
            text: "Update Bio",
            callback_data: `set_page_bio_${id}`,
          },
        ],
        [
          {
            text: `Theme: ${theme === "dark" ? "🌑" : "☀"}`,
            callback_data: `set_page_theme_${id}`,
          },
          {
            text: `🌈 Set Color`,
            callback_data: `set_page_color_${id}`,
          },
        ],
        [
          {
            text: `🔗 Social Media Links`,
            callback_data: `set_page_social_media_${id}`,
          },
        ],
        [
          {
            text: `💎 Favorite Tokens`,
            callback_data: `set_page_favorite_tokens_${id}`,
          },
        ],
      ],
    };
  } else {
    return {
      inline_keyboard: [
        [
          {
            text: "👤 Update Name",
            callback_data: `set_display_name_${id}`,
          },
          {
            text: "🔗 Get Link",
            callback_data: `set_custom_url_link_${id}`,
          },
        ],
        [
          {
            text: "🖌 Set Logo",
            callback_data: `set_page_logo_${id}`,
          },
          {
            text: "🖼 Set Banner",
            callback_data: `set_page_banner_${id}`,
          },
        ],
        [
          {
            text: "Update Bio",
            callback_data: `set_page_bio_${id}`,
          },
        ],
        [
          {
            text: `Theme: ${theme === "dark" ? "🌑" : "☀"}`,
            callback_data: `set_page_theme_${id}`,
          },
          {
            text: `🌈 Set Color`,
            callback_data: `set_page_color_${id}`,
          },
        ],
        [
          {
            text: `🔗 Social Media Links`,
            callback_data: `set_page_social_media_${id}`,
          },
        ],
        [
          {
            text: `💎 Set Community Token`,
            callback_data: `set_page_token_${id}`,
          },
        ],
      ],
    };
  }
};

const page_update_edit_msg = (id, theme = "dark") => {
  return {
    inline_keyboard: [
      [
        {
          text: "👤 Update Name",
          url: `https://t.me/${botUsername}?start=configure_${id}_set_display_name`,
        },
        {
          text: "🔗 Get Link",
          url: `https://t.me/${botUsername}?start=configure_${id}_set_custom_url_link`,
        },
      ],
      [
        {
          text: "🖌 Set Logo",
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_logo`,
        },
        {
          text: "🖼 Set Banner",
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_banner`,
        },
      ],
      [
        {
          text: "Update Bio",
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_bio`,
        },
      ],
      [
        {
          text: `Theme: ${theme === "dark" ? "🌑" : "☀"}`,
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_theme`,
        },
        {
          text: `🌈 Set Color`,
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_color`,
        },
      ],
      [
        {
          text: `🔗 Social Media Links`,
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_social_media`,
        },
      ],
      [
        {
          text: `💎 Favorite Tokens`,
          url: `https://t.me/${botUsername}?start=configure_${id}_set_page_favorite_tokens`,
        },
      ],
    ],
  };
};

const socialOptions = (id) => {
  return {
    inline_keyboard: [
      [
        {
          text: "Socials",
          callback_data: `get_social_links_${id}`,
        },
      ],
      [
        {
          text: "Back",
          callback_data: `load_page_${id}`,
        },
      ],
    ],
  };
};

const socialMediaList = (socialMedia) => {
  return {
    inline_keyboard: [
      [
        {
          text: `Website: ${socialMedia.website ? "✅" : "☑️"}`,
          callback_data: `set_website_${socialMedia.id}`,
        },
        {
          text: `Twitter: ${socialMedia.twitter ? "✅" : "☑️"}`,
          callback_data: `set_twitter_${socialMedia.id}`,
        },
      ],
      [
        {
          text: `Telegram: ${socialMedia.telegram ? "✅" : "☑️"}`,
          callback_data: `set_telegram_${socialMedia.id}`,
        },
        {
          text: `Instagram: ${socialMedia.instagram ? "✅" : "☑️"}`,
          callback_data: `set_instagram_${socialMedia.id}`,
        },
      ],
      [
        {
          text: `TikTok: ${socialMedia.tiktok ? "✅" : "☑️"}`,
          callback_data: `set_tiktok_${socialMedia.id}`,
        },
        {
          text: `Discord: ${socialMedia.discord ? "✅" : "☑️"}`,
          callback_data: `set_discord_${socialMedia.id}`,
        },
      ],
      [
        {
          text: `Coinmarketcap: ${socialMedia.coinmarketcap ? "✅" : "☑️"}`,
          callback_data: `set_coinmarketcap_${socialMedia.id}`,
        },
        {
          text: `CoinGecko: ${socialMedia.coingecko ? "✅" : "☑️"}`,
          callback_data: `set_coingecko_${socialMedia.id}`,
        },
      ],
      [
        {
          text: `Youtube: ${socialMedia.youtube ? "✅" : "☑️"}`,
          callback_data: `set_youtube_${socialMedia.id}`,
        },
      ],
      [
        {
          text: "Back",
          callback_data: `load_page_${socialMedia.page}`,
        },
      ],
    ],
  };
};

const buyUI = (contractAddress, chain) => {
  return {
    inline_keyboard: [
      [
        {
          text: "Buy from Anubis Bot",
          url: `https://t.me/${tradingBotUsername}?start=${contractAddress}_${chain}`,
        },
      ],
      [
        {
          text: "Buy from Anubis Terminal",
          url: `${terminalUrl}/${chain}/${contractAddress}`,
        },
      ],
      [
        {
          text: "View On DexScreener",
          url: `https://dexscreener.com/${chain}/${contractAddress}`,
        },
      ],
    ],
  };
};

module.exports = {
  mainMenu,
  service_configuration_msg,
  page_update_msg,
  page_update_edit_msg,
  socialOptions,
  socialMediaList,
  buyUI,
};
