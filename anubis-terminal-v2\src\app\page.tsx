"use client";
import Navbar from "@/components/Landing/Navbar";
import Hero from "@/components/Landing/Hero";
import Features from "@/components/Landing/Features";
import BrowserExtension from "@/components/Landing/BrowserExtension";
import HowItWorks from "@/components/Landing/HowItWorks";
import CTA from "@/components/Landing/CTA";
import Footer from "@/components/Landing/Footer";
import Link from "next/link";
import { motion } from "framer-motion";
import { BiBot } from "react-icons/bi";

export default function Home() {
  return (
    <main className="bg-[#0a0a0a] min-h-screen">
      <Navbar />
      <Hero />
      <Features />
      {/* Minimal Anubis Teams Callout */}
      <section className="py-16 px-4 md:px-8 lg:px-12 flex justify-center items-center">
        <motion.div
          className="w-full max-w-3xl mx-auto bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-2xl p-8 text-center relative overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            className="inline-flex items-center justify-center mb-4"
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <BiBot className="text-3xl text-white drop-shadow-[0_0_12px_rgba(255,255,255,0.7)] animate-pulse" />
          </motion.div>
          <h2 className="text-2xl md:text-3xl font-orbitron font-bold mb-2 text-white drop-shadow-[0_0_8px_rgba(255,255,255,0.5)]">Anubis Teams</h2>
          <p className="text-gray-300 font-space-grotesk mb-6">Build and manage your trading community with our powerful Telegram bot. <span className="text-white font-semibold">Custom pages, token integration, automation, and more.</span></p>
          <Link href="/anubis-teams">
            <motion.button
              className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-lg text-base hover:shadow-lg hover:shadow-white/30 transition-all duration-300 drop-shadow-[0_0_12px_rgba(255,255,255,0.7)]"
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 20px #fff"
              }}
              whileTap={{
                scale: 0.97
              }}
            >
              Learn More
            </motion.button>
          </Link>
        </motion.div>
      </section>
      <BrowserExtension />
      <HowItWorks />
      <CTA />
      <Footer />
    </main>
  );
}
