"use client";
import { useState } from "react";
import { useWeb3 } from "@/contexts/Web3Context";
import { TimeFilterDropdown } from "@/components/Terminal/TimeFilter";
import StatsFilter from "@/components/Terminal/TimeFilter/StatsFilter";
import { useGetMarketData } from "@/hooks/useGetMarketData";
import { getTimeAgo, getVolumeByTimeOption, getPriceChangeByTimeOption } from "@/utils/data";
import { useRouter } from "next/navigation";

const timeOptions = ["1h", "4h", "12h", "24h"];
type TimeOption = string;

interface TrendingToken {
    id: string;
    details: {
        name: string;
        symbol: string;
        address: string;
        imageThumbUrl?: string;
    };
    activity: {
        createdAt: number;
        change1?: string;
        change4?: string;
        change12?: string;
        change24?: string;
        volume1?: string;
        volume4?: string;
        volume12?: string;
        volume24?: string;
        [key: string]: any;
    };
    marketData: {
        marketCap: number;
        [key: string]: any;
    };
}

export default function Trending() {
    const { selectedChain } = useWeb3();
    const { push } = useRouter();
    const [selectedTime, setSelectedTime] = useState<TimeOption>("1h");
    const { trendingTokens, loading, error, refetch } = useGetMarketData({ networks: selectedChain?.slug, time: selectedTime, pollingInterval: 10000 });

    const handleTimeSelect = (time: TimeOption) => setSelectedTime(time);

    return (
        <div className="w-full p-3 flex flex-col gap-2 overflow-hidden row-span-1 border-b border-white/20">
            <div className="flex items-center justify-start gap-x-2 mb-2">
                <h5 className="font-space-grotesk text-xs font-bold">Trending</h5>
                <TimeFilterDropdown timeOptions={timeOptions} selectedTime={selectedTime} handleTimeSelect={handleTimeSelect} />
                <StatsFilter />
            </div>
            <div className="overflow-x-scroll scrollbar-hidden">
                <table className="w-full min-w-[420px] text-left text-xs border-separate border-spacing-y-1">
                    <thead>
                        <tr className="text-white/60">
                            <th className="px-2 py-1 font-semibold">Asset</th>
                            <th className="px-2 py-1 font-semibold">Age</th>
                            <th className="px-2 py-1 font-semibold">{selectedTime.toUpperCase()}</th>
                            <th className="px-2 py-1 font-semibold">Volume</th>
                            <th className="px-2 py-1 font-semibold">MCap</th>
                        </tr>
                    </thead>
                    <tbody>
                        {loading && (
                            <tr><td colSpan={5} className="text-center py-4 text-white/50">Loading...</td></tr>
                        )}
                        {error && (
                            <tr><td colSpan={5} className="text-center py-4 text-red-400">Error loading data</td></tr>
                        )}
                        {trendingTokens && (trendingTokens as TrendingToken[]).slice(0, 8).map((token) => {
                            // Fallbacks for missing activity fields
                            const activity = {
                                change1: "0", change4: "0", change12: "0", change24: "0",
                                volume1: "0", volume4: "0", volume12: "0", volume24: "0",
                                ...token.activity
                            };
                            const priceChange = getPriceChangeByTimeOption(activity, selectedTime);
                            const volume = getVolumeByTimeOption(activity, selectedTime);
                            return (
                                <tr key={token.id} className="hover:bg-white/5 rounded transition cursor-pointer" onClick={() => selectedChain?.slug && push(`/terminal/trade/${selectedChain.slug}/${token.details.address}`)}>
                                    {/* Asset */}
                                    <td className="px-2 py-1">
                                        <div className="flex items-center gap-2">
                                            {token.details.imageThumbUrl ? (
                                                <div className="w-5 h-5 relative">
                                                    <img src={token.details.imageThumbUrl} alt={token.details.name} className="w-5 h-5 rounded-full" />
                                                    <img src={selectedChain?.logo} alt="" className="w-2.5 h-2.5 absolute -bottom-1 -right-1" />
                                                </div>
                                            ) : (
                                                <div className="w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center text-[10px] relative">
                                                    {token.details.name.charAt(0)}
                                                </div>
                                            )}
                                            <span className="font-medium text-white truncate max-w-[70px]">{token.details.name}</span>
                                            <span className="text-white/40 text-[10px]">{token.details.symbol}</span>
                                        </div>
                                    </td>
                                    {/* Age */}
                                    <td className="px-2 py-1 text-white/70">{getTimeAgo(token.activity.createdAt)}</td>
                                    {/* The Price Timing */}
                                    <td className="px-2 py-1">
                                        <span className={priceChange > 0 ? "text-green-500" : priceChange < 0 ? "text-red-500" : "text-white/70"}>
                                            {priceChange > 0 ? "+" : ""}
                                            {priceChange.toFixed(1)}%
                                        </span>
                                    </td>
                                    {/* Volume */}
                                    <td className="px-2 py-1 font-mono text-white/80">${volume.toLocaleString()}</td>
                                    {/* Market Cap */}
                                    <td className="px-2 py-1 text-white/70 font-mono">${Number(token.marketData.marketCap).toLocaleString()}</td>
                                </tr>
                            );
                        })}
                        {trendingTokens && (trendingTokens as TrendingToken[]).length === 0 && !loading && (
                            <tr><td colSpan={5} className="text-center py-4 text-white/50">No trending tokens</td></tr>
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
}