const axios = require("axios");

module.exports = {
  friendlyName: "Leave chat",

  description: "",

  inputs: {
    groupChatId: {
      type: "string",
      description: "Group Chat ID",
      required: true,
    },
  },

  exits: {
    success: {
      description: "All done.",
    },
  },

  fn: async function (inputs, exits) {
    // TODO
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;
    const TELEGRAM_API = `https://api.telegram.org/bot${TOKEN}`;

    try {
      const response = await axios.post(`${TELEGRAM_API}/leaveChat`, {
        chat_id: inputs.groupChatId,
      });
      sails.log.info("Left chat successfully:", response.data);
    } catch (error) {
      sails.log.error("Error leaving chat:", error.message);
      if (error.response) {
        sails.log.error("Error response data:", error.response.data);
      }
    }
  },
};
