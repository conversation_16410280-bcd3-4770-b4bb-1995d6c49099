"use client"
import { motion } from "framer-motion";

interface GlitchTextProps {
  text: string;
  className?: string;
}

export default function GlitchText({ text, className = "" }: GlitchTextProps) {
  return (
    <div className={`relative inline-block ${className}`}>
      <motion.span
        className="absolute top-0 left-0 text-white font-orbitron font-bold"
        animate={{
          x: [0, -2, 0, 2, 0],
          opacity: [1, 0.8, 1],
        }}
        transition={{
          duration: 0.5,
          repeat: Infinity,
          repeatType: "mirror",
          ease: "easeInOut",
        }}
      >
        {text}
      </motion.span>
      <motion.span
        className="absolute top-0 left-0 text-red-500 font-orbitron font-bold mix-blend-screen"
        animate={{
          x: [0, 2, 0, -2, 0],
          opacity: [1, 0.6, 0.8, 0.6, 1],
        }}
        transition={{
          duration: 0.5,
          repeat: Infinity,
          repeatType: "mirror",
          ease: "easeInOut",
          delay: 0.1,
        }}
      >
        {text}
      </motion.span>
      <motion.span
        className="relative text-white font-orbitron font-bold"
        animate={{
          x: [0, 1, 0, -1, 0],
        }}
        transition={{
          duration: 0.5,
          repeat: Infinity,
          repeatType: "mirror",
          ease: "easeInOut",
          delay: 0.2,
        }}
      >
        {text}
      </motion.span>
    </div>
  );
}
