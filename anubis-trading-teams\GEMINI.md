# Anubis Trading Teams Bot

This document provides an overview of the Anubis Trading Teams Bot, a Telegram bot designed to help communities create and manage their own customized web pages and integrated crypto-related features.

## Project Overview

The project is a **Sails.js** application that powers a **Telegram bot** built using the **Telegraf** library. The bot allows Telegram group administrators to create and manage a public-facing "LinkTree" style page for their community. This page can be customized with branding, social links, and specific crypto token information.

The primary entry point for the bot's logic is the webhook handler at `api/controllers/bot/process-webhook.js`, which receives updates from Telegram and routes them to the appropriate functions in `api/utils/bot-functions.js`.

## Core Features

### 1. User and Group Management

- **User Onboarding**: New users who interact with the bot via the `/start` command are registered in the database (`User` model).
- **Group Setup**: When the bot is added to a new Telegram group, an administrator can run the `/setup` command. This command registers the group in the database (`Group` model), linking it to the administrator who initiated the setup.
- **Ownership**: The system maintains a clear ownership link between users, groups, and the pages they create. Actions are permission-checked to ensure only authorized admins can make changes.

### 2. Community Page Creation & Customization

The central feature is the ability to create a customizable page for a community. This is referred to as a "Token Page" or "Community" page.

- **Creation**: Admins can create new pages through a guided menu system. Each page is linked to a specific group.
- **Customization**: Admins can personalize their page through a series of bot commands and conversations:
    - **Display Name & Bio**: Set a custom name and description for the page.
    - **Logo & Banner**: Upload custom images to be used as the page's logo and banner.
    - **Color Theme**: Define a specific accent color for the page using hexadecimal values.
    - **Social Media Links**: Add and manage links to various social media platforms.
    - **Custom URL**: Define a unique, human-readable URL slug for their page (e.g., `anubistrade.xyz/your-page`).

### 3. "Buy Token" Integration

- **Token Setup**: Admins can configure a specific cryptocurrency token for their group. The setup involves:
    1.  Specifying the blockchain (e.g., Ethereum, BSC).
    2.  Providing the token's contract address.
- **Data Fetching**: The bot integrates with the **DexScreener API** to pull real-time data for the configured token, including its name, symbol, price, liquidity, and market cap.
- **`/ca` Command**: Group members can use the `/ca` command (e.g., `/ca@anubis_teams_bot`) to instantly receive a formatted message containing the token's details, a chart image, and a direct link to purchase it.

### 4. Bot Interaction & Workflow

- **Command Handling**: The bot responds to several commands:
    - `/start`: Initiates interaction with the bot, handles user creation, and deep linking for configuration.
    - `/setup`: Registers a new group and links it to the admin.
    - `/app`: Allows for editing the group's associated page.
- **Inline Keyboards**: The bot heavily utilizes Telegram's inline keyboard buttons for a user-friendly, menu-driven experience, guiding admins through the setup and customization process.
- **Conversational UI**: For settings that require text or media input (like setting a bio, URL, or uploading a logo), the bot enters a "mode" and waits for the user's next message, creating a conversational flow.
- **Welcome Messages**: The bot now automatically welcomes new users who join the Telegram group, providing them with a link to the community page and relevant social media links if configured.

## Technical Architecture

- **Backend Framework**: **Sails.js**
- **Telegram Bot Library**: **Telegraf**
- **Database Models**: The application uses several models to structure its data, including:
    - `User`: Stores information about individual Telegram users.
    - `Group`: Represents a Telegram group managed by the bot.
    - `Page`: Contains all the data for a customizable community page. (The `links` attribute has been removed and is now handled by the `SocialMedia` model).
    - `SocialMedia`: Stores social media links associated with a `Page`.
- **Key Files**:
    - `api/controllers/bot/process-webhook.js`: The main webhook handler that listens for and routes all incoming Telegram updates.
    - `api/utils/bot-functions.js`: Contains the core business logic for all bot features, including handling commands, callback queries, text messages, and new group member welcome messages.
    - `api/utils/token/dexscreener.js`: Handles communication with the DexScreener API.
    - `api/utils/message-structure.js`: Defines the structure of the inline keyboard menus sent by the bot.
    - `api/helpers/get-photo.js`: A new helper to fetch photo URLs from Telegram's API.