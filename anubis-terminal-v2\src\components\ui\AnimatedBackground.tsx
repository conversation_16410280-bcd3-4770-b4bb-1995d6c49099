"use client"
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface AnimatedBackgroundProps {
  className?: string;
}

export default function AnimatedBackground({ className = "" }: AnimatedBackgroundProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== "undefined") {
      // Set initial value
      setIsMobile(window.innerWidth < 768);

      // Add event listener for window resize
      const handleResize = () => {
        setIsMobile(window.innerWidth < 768);
      };

      window.addEventListener("resize", handleResize);

      // Clean up
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  }, []);

  // Grid lines configuration
  const gridLines = {
    horizontal: Array.from({ length: isMobile ? 10 : 20 }),
    vertical: Array.from({ length: isMobile ? 10 : 20 }),
  };

  return (
    <div className={`absolute inset-0 overflow-hidden ${className}`}>
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#0a0a0a] to-[#1a1a1a] z-0"></div>

      {/* Grid lines */}
      <div className="absolute inset-0 z-10">
        {/* Horizontal lines */}
        {gridLines.horizontal.map((_, index) => (
          <motion.div
            key={`h-${index}`}
            className="absolute left-0 right-0 h-px bg-white/5"
            style={{ top: `${(100 / gridLines.horizontal.length) * index}%` }}
            initial={{ opacity: 0, scaleX: 0 }}
            animate={{ opacity: 1, scaleX: 1 }}
            transition={{ duration: 1.5, delay: 0.05 * index }}
          />
        ))}

        {/* Vertical lines */}
        {gridLines.vertical.map((_, index) => (
          <motion.div
            key={`v-${index}`}
            className="absolute top-0 bottom-0 w-px bg-white/5"
            style={{ left: `${(100 / gridLines.vertical.length) * index}%` }}
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            transition={{ duration: 1.5, delay: 0.05 * index }}
          />
        ))}
      </div>

      {/* Animated glow spots */}
      <motion.div
        className="absolute w-[500px] h-[500px] rounded-full bg-white/5 blur-[100px]"
        style={{ top: "-10%", right: "-10%" }}
        animate={{
          opacity: [0.1, 0.15, 0.1],
        }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
      />

      <motion.div
        className="absolute w-[300px] h-[300px] rounded-full bg-white/5 blur-[80px]"
        style={{ bottom: "-5%", left: "30%" }}
        animate={{
          opacity: [0.1, 0.2, 0.1],
        }}
        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1 }}
      />

      {/* Animated scan line */}
      <motion.div
        className="absolute left-0 right-0 h-[2px] bg-white/10 blur-[1px] z-20"
        animate={{
          top: ["0%", "100%"],
          opacity: [0, 0.5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "linear",
        }}
      />
    </div>
  );
}
