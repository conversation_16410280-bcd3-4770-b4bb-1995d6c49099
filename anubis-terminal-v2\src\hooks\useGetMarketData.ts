import { useState, useEffect, useCallback, useRef } from 'react';
import { getTrendingTokens, getNewTokenPairs, getGainerTokenPairs } from '@/services/api/flooz';

interface UseGetMarketDataOptions {
    networks: string | undefined;
    time: string;
    pollingInterval?: number; // in ms
}

interface UseGetMarketDataResult<TokensType = any, PairsType = any> {
    trendingTokens: TokensType | null;
    newPairs: PairsType | null;
    gainers: PairsType | null;
    loading: boolean;
    isRefetching: boolean;
    error: Error | null;
    refetch: () => void;
}

export function useGetMarketData({ networks, time, pollingInterval }: UseGetMarketDataOptions): UseGetMarketDataResult {
    const [trendingTokens, setTrendingTokens] = useState<any | null>(null);
    const [newPairs, setNewPairs] = useState<any | null>(null);
    const [gainers, setGainers] = useState<any | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [isRefetching, setIsRefetching] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);
    const pollingRef = useRef<NodeJS.Timeout | null>(null);

    const fetchData = useCallback(async (isPolling = false) => {
        if (!isPolling) {
            setLoading(true);
        } else {
            setIsRefetching(true);
        }
        setError(null);
        try {
            const [tokens, pairs, gainers] = await Promise.all([
                getTrendingTokens(networks as string, time),
                getNewTokenPairs(networks as string, time),
                getGainerTokenPairs(networks as string, time)
            ]);
            setTrendingTokens(tokens);
            setNewPairs(pairs);
            setGainers(gainers);
        } catch (err) {
            setError(err as Error);
        } finally {
            if (!isPolling) {
                setLoading(false);
            } else {
                setIsRefetching(false);
            }
        }
    }, [networks, time]);

    // Initial fetch and refetch on param change
    useEffect(() => {
        fetchData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fetchData]);

    // Polling logic
    useEffect(() => {
        if (pollingInterval && pollingInterval > 0) {
            pollingRef.current = setInterval(() => fetchData(true), pollingInterval);
            return () => {
                if (pollingRef.current) clearInterval(pollingRef.current);
            };
        }
        return undefined;
    }, [fetchData, pollingInterval]);

    const refetch = useCallback(() => {
        fetchData(true);
    }, [fetchData]);

    return {
        trendingTokens,
        newPairs,
        gainers,
        loading,
        isRefetching,
        error,
        refetch,
    };
} 