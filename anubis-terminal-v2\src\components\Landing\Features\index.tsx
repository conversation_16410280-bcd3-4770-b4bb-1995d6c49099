"use client"
import { motion } from "framer-motion";
import { <PERSON>aBolt, FaShieldAlt, FaChartLine, FaRobot, FaExchangeAlt, FaUserSecret } from "react-icons/fa";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
}

const FeatureCard = ({ icon, title, description, index }: FeatureCardProps) => {
  return (
    <motion.div
      className="bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a] p-6 rounded-lg border border-white/10 hover:border-white/40 transition-all duration-300 relative group shadow-none hover:shadow-[0_0_24px_white]"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-100px" }}
      whileHover={{ scale: 1.03 }}
    >
      <div className="flex flex-col h-full">
        <motion.div
          className="mb-4 text-3xl text-white drop-shadow-[0_0_16px_white]"
          initial={{ scale: 0 }}
          whileInView={{ scale: 1 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.1 + index * 0.1
          }}
          viewport={{ once: true, margin: "-100px" }}
          whileHover={{
            scale: 1.15,
            filter: "drop-shadow(0 0 32px white)",
            transition: { duration: 0.2 }
          }}
        >
          {icon}
        </motion.div>
        <motion.h3
          className="text-xl font-orbitron font-bold mb-3 text-white"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 + index * 0.1 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {title}
        </motion.h3>
        <motion.p
          className="text-gray-300 font-space-grotesk flex-grow"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {description}
        </motion.p>
      </div>
    </motion.div>
  );
};

export default function Features() {
  const features = [
    {
      icon: <FaBolt />,
      title: "Lightning-Fast Execution",
      description: "Execute trades with millisecond precision. Our advanced infrastructure ensures you never miss an opportunity."
    },
    {
      icon: <FaShieldAlt />,
      title: "MEV Protection",
      description: "Advanced protection against Miner Extractable Value. Trade with confidence knowing your transactions are secure."
    },
    {
      icon: <FaChartLine />,
      title: "Real-Time Analytics",
      description: "Access comprehensive market data and analytics to make informed trading decisions instantly."
    },
    {
      icon: <FaRobot />,
      title: "Automated Trading",
      description: "Set up custom trading strategies that execute automatically based on your predefined parameters."
    },
    {
      icon: <FaExchangeAlt />,
      title: "Cross-Chain Trading",
      description: "Seamlessly trade across multiple blockchains from a single interface with optimized gas fees."
    },
    {
      icon: <FaUserSecret />,
      title: "Privacy-Focused",
      description: "Trade with enhanced privacy features that protect your identity and transaction data."
    }
  ];

  return (
    <section className="py-20 px-4 md:px-8 lg:px-12" id="features">
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400 drop-shadow-[0_0_16px_white]">
            Advanced Trading Features
          </h2>
          <p className="text-gray-300 font-space-grotesk max-w-2xl mx-auto text-lg">
            Anubis Terminal provides professional traders with cutting-edge tools for on-chain trading.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
