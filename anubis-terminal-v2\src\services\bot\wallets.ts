import { api } from './index';

export interface Wallet {
  address: `0x${string}`;
  isPrimary?: boolean;
  // Add other wallet properties as needed from the backend
}

export interface CreateWalletResponse {
  address: `0x${string}`;
  privateKey: string;
  phrase?: string;
  message: string;
}

export interface ImportWalletParams {
  privateKey: string;
  name?: string;
}

/**
 * Get all wallets for the authenticated user
 */
export const getWallets = async (token: string): Promise<Wallet[]> => {
  try {
    const response = await api.get<{ wallets: Wallet[] }>('/bot/user/wallets', {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data.wallets || [];
  } catch (error) {
    console.error('Error fetching wallets:', error);
    throw new Error('Failed to fetch wallets');
  }
};

/**
 * Create a new wallet
 */
export const createWallet = async (token: string): Promise<CreateWalletResponse> => {
  try {
    const response = await api.post<CreateWalletResponse>(
      '/bot/user/wallet/create',
      {},
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error creating wallet:', error);
    throw new Error('Failed to create wallet');
  }
};

/**
 * Import a wallet by private key
 */
export const importWallet = async (
  token: string,
  params: ImportWalletParams
): Promise<Wallet> => {
  try {
    const response = await api.post<Wallet>(
      '/bot/user/wallets/import',
      { privateKey: params.privateKey },
      {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error importing wallet:', error);
    throw new Error('Failed to import wallet');
  }
};

/**
 * Set a wallet as primary
 */
export const setPrimaryWallet = async (
  token: string,
  walletAddress: `0x${string}`
): Promise<void> => {
  try {
    await api.post(
      '/bot/user/wallets/primary',
      { address: walletAddress },
      {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Error setting primary wallet:', error);
    throw new Error('Failed to set primary wallet');
  }
};

/**
 * Get wallet private key
 * Note: This should be used with caution and proper security measures
 */
export const getWalletPrivateKey = async (
  token: string,
  walletAddress: `0x${string}`
): Promise<{ privateKey: string }> => {
  try {
    const response = await api.get<{ privateKey: string }>(
      `/bot/user/wallets/private/${walletAddress}`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error getting wallet private key:', error);
    throw new Error('Failed to get wallet private key');
  }
};

/**
 * Get wallet balance for a specific chain
 */
export const getWalletBalance = async (
  token: string,
  walletAddress: `0x${string}`,
  chainId: number
): Promise<{
  chainId: number;
  chainName: string;
  nativeBalance: string;
  tokens: Array<{
    address: string;
    name: string | null;
    symbol: string | null;
    balance: string;
    priceUsd: number | null;
    priceNative: string | null;
    isNative: boolean;
  }>;
}> => {
  try {
    const response = await api.get(
      `/bot/user/wallets/balance?address=${walletAddress}&chainId=${chainId}`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    throw new Error('Failed to get wallet balance');
  }
};
