"use client"
import { motion } from "framer-motion";
import Link from "next/link";
import { useState, useEffect } from "react";
import Navbar from "@/components/Landing/Navbar";
import { IoMdArrowBack } from "react-icons/io";
import GlitchText from "@/components/ui/GlitchText";

// Animated particles component
function AnimatedParticles() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== "undefined") {
      // Set initial value
      setIsMobile(window.innerWidth < 768);

      // Add event listener for window resize
      const handleResize = () => {
        setIsMobile(window.innerWidth < 768);
      };

      window.addEventListener("resize", handleResize);

      // Clean up
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  }, []);

  // Create an array of particles with different types - fewer on mobile for better performance
  const particleCount = isMobile ? 15 : 30;

  const particles = Array.from({ length: particleCount }).map((_, i) => ({
    id: i,
    type: Math.random() > 0.7 ? "star" : "circle",
    opacity: Math.random() * 0.3 + 0.1, // Random opacity between 0.1 and 0.4
  }));

  return (
    <>
      {particles.map((particle) => {
        // Random position and size for each particle - smaller on mobile
        const sizeFactor = isMobile ? 0.7 : 1;
        const size = (Math.random() * 5 + (particle.type === "star" ? 4 : 2)) * sizeFactor;
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const duration = Math.random() * 15 + (isMobile ? 15 : 10); // Slower on mobile for better performance
        const delay = Math.random() * 5;

        // Set opacity based on particle property
        const colorClass = `bg-white/[${particle.opacity}]`;

        // Different shapes based on particle type
        const shapeClass = particle.type === "star" ? "clip-path-star" : "rounded-full";

        return (
          <motion.div
            key={particle.id}
            className={`absolute ${colorClass} ${shapeClass}`}
            style={{
              width: size,
              height: size,
              left: `${x}%`,
              top: `${y}%`,
              clipPath:
                particle.type === "star"
                  ? "polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)"
                  : "none",
            }}
            animate={{
              y: [0, -150],
              x: [0, Math.random() * 40 - 20], // Random horizontal movement
              opacity: [0, 0.9, 0],
              scale: [1, particle.type === "star" ? 1.2 : 0.5],
              rotate: particle.type === "star" ? [0, 360] : 0,
            }}
            transition={{
              duration,
              repeat: Infinity,
              delay,
              ease: "linear",
            }}
          />
        );
      })}
    </>
  );
}

export default function NotFound() {
  return (
    <>
      <Navbar />
      <div className="min-h-[calc(100vh-80px)] flex flex-col items-center justify-center relative overflow-hidden px-4">
        {/* Background elements */}
        <div className="absolute inset-0 bg-[#0a0a0a] z-0"></div>
        <AnimatedParticles />

        {/* Glitchy 404 container */}
        <motion.div
          className="relative z-10 flex flex-col items-center justify-center gap-8 max-w-4xl mx-auto text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* 404 Text */}
          <motion.div
            className="text-6xl sm:text-8xl md:text-9xl font-orbitron font-black text-white mb-4"
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <GlitchText text="404" />
          </motion.div>

          {/* Error message */}
          <motion.div
            className="space-y-4"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h2 className="text-xl sm:text-2xl md:text-3xl font-orbitron font-bold text-white">
              PAGE NOT FOUND
            </h2>
            <p className="text-gray-400 font-space-grotesk max-w-md mx-auto">
              The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
            </p>
          </motion.div>

          {/* Terminal-like error message */}
          <motion.div
            className="w-full max-w-md bg-black/50 border border-white/20 rounded-md p-4 font-mono text-sm text-left"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span className="ml-2 text-gray-400">Terminal</span>
            </div>
            <div className="font-geist-mono text-white">
              <span className="text-green-500">anubis@terminal:~$</span> find /{" "}
              <span className="text-red-500">-name</span> requested-page
              <br />
              <span className="text-red-500">Error:</span> No such file or directory
            </div>
          </motion.div>

          {/* Action buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 mt-4"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <Link href="/">
              <motion.button
                className="flex items-center gap-2 font-orbitron font-semibold bg-white text-black px-6 py-3 rounded-md hover:shadow-lg hover:shadow-white/20"
                whileHover={{
                  boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                }}
                whileTap={{
                  backgroundColor: "#f0f0f0",
                }}
              >
                <IoMdArrowBack />
                Back to Home
              </motion.button>
            </Link>
            <Link href="/terminal/auth" target="_blank">
              <motion.button
                className="font-orbitron font-semibold border border-white text-white px-6 py-3 rounded-md hover:bg-white/10"
                whileHover={{
                  boxShadow: "0 0 15px rgba(255, 255, 255, 0.3)",
                }}
                whileTap={{
                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                }}
              >
                Go to Terminal
              </motion.button>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}
