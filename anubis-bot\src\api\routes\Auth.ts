import { z } from "zod";
import { <PERSON>o } from "hono";
import { storageInstance } from "../../../storage";
import { zValidator } from '@hono/zod-validator';
import { sign } from 'hono/jwt';
import { sendMessage } from "../helper/bot/index";

const verifyOtpSchema = z.object({
    tgUserId: z.string().min(1),
    otp: z.string().min(6).max(6),
})

const auth = new Hono();

// Verify OTP and Generate JWT Token
auth.post("/verify-otp", zValidator('json', verifyOtpSchema), async (c) => {
    const { otp, tgUserId } = c.req.valid('json');
    const user = await storageInstance.read(tgUserId);

    if (!user) {
        return c.json({
            error: "User not found",
            message: "User not found. Please try again."
        }, 400)
    }

    // Check if OTP Exists and Validate OTP
    if (user.__d.otp !== otp || !user.__d.otp) {
        return c.json({
            error: "Invalid OTP",
            message: "There was an error verifying your OTP. Please try again."
        }, 400)
    }

    // Validate OTP TTL
    if (user.__d.otpTTL as number < Date.now()) {
        return c.json({
            error: "Invalid OTP",
            message: "Your OTP has expired. Please request a new one by using the /link command in the bot."
        }, 400)
    }

    // Delete OTP
    user.__d.otp = undefined;
    user.__d.otpTTL = undefined;
    await storageInstance.write(tgUserId, user);

    const payload = { id: tgUserId, iat: Math.floor(Date.now() / 1000), exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7 };

    const token = await sign(
        payload,
        process.env.JWT_SECRET ?? ""
    );

    // Send Notification through bot
    await sendMessage({
        chat_id: tgUserId,
        text: "🎉 Success! Your account has been securely verified.\n\n✨ Welcome to Anubis Bot! You now have full access to all trading features.\n\n🚀 Ready to start trading!",
        enableWebPreview: false
    });

    return c.json({
        message: "User's OTP verified successfully.",
        token: token
    }, 200)

});

export default auth;
