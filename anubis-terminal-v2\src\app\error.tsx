"use client"
import { motion } from "framer-motion";
import Link from "next/link";
import { useEffect } from "react";
import { IoMdArrowBack, IoMdRefresh } from "react-icons/io";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden px-4 bg-[#0a0a0a]">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#0a0a0a] to-[#1a1a1a] z-0"></div>
      
      {/* Error container */}
      <motion.div
        className="relative z-10 flex flex-col items-center justify-center gap-8 max-w-4xl mx-auto text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Error icon */}
        <motion.div
          className="w-24 h-24 rounded-full border-2 border-red-500 flex items-center justify-center mb-4"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 260, 
            damping: 20,
            delay: 0.2 
          }}
        >
          <span className="text-red-500 text-5xl font-bold">!</span>
        </motion.div>

        {/* Error message */}
        <motion.div
          className="space-y-4"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-xl sm:text-2xl md:text-3xl font-orbitron font-bold text-white">
            SOMETHING WENT WRONG
          </h2>
          <p className="text-gray-400 font-space-grotesk max-w-md mx-auto">
            We apologize for the inconvenience. An unexpected error has occurred.
          </p>
        </motion.div>

        {/* Error details */}
        <motion.div
          className="w-full max-w-md bg-black/50 border border-white/20 rounded-md p-4 font-mono text-sm text-left"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="flex items-center gap-2 mb-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="ml-2 text-gray-400">Terminal</span>
          </div>
          <div className="font-geist-mono text-white">
            <span className="text-green-500">anubis@terminal:~$</span> system.status
            <br />
            <span className="text-red-500">Error:</span> {error.message || "An unexpected error occurred"}
            {error.digest && (
              <>
                <br />
                <span className="text-yellow-500">Digest:</span> {error.digest}
              </>
            )}
          </div>
        </motion.div>

        {/* Action buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 mt-4"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <motion.button
            className="flex items-center gap-2 font-orbitron font-semibold bg-white text-black px-6 py-3 rounded-md hover:shadow-lg hover:shadow-white/20"
            onClick={() => reset()}
            whileHover={{
              boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
            }}
            whileTap={{
              backgroundColor: "#f0f0f0",
            }}
          >
            <IoMdRefresh />
            Try Again
          </motion.button>
          <Link href="/">
            <motion.button
              className="font-orbitron font-semibold border border-white text-white px-6 py-3 rounded-md hover:bg-white/10"
              whileHover={{
                boxShadow: "0 0 15px rgba(255, 255, 255, 0.3)",
              }}
              whileTap={{
                backgroundColor: "rgba(255, 255, 255, 0.05)",
              }}
            >
              <div className="flex items-center gap-2">
                <IoMdArrowBack />
                Back to Home
              </div>
            </motion.button>
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
}
