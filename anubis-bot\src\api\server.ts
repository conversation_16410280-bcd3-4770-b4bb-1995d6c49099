import { Hono } from "hono";
import { logger } from 'hono/logger'
import auth from "./routes/Auth";
import botOp from "./routes/BotOp";
import trade from "./routes/Trade";
import { cors } from 'hono/cors'

const app = new Hono();
app.use(logger())
app.use(cors({
    origin: (origin) => origin ?? '*', // Reflects the request's origin or * if undefined
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    exposeHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    maxAge: 86400,
}))

app.get("/", (c) => c.text("Anubis Terminal API is running!"));
app.route("/auth", auth)
app.route("/bot", botOp)
app.route("/trade", trade)

const now = new Date();
const formattedDate = now.toLocaleString('en-US', {
  year: 'numeric',
  month: 'short',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false,
  timeZoneName: 'short'
});
console.log(`
==========================================
🚀 Anubis Terminal API is running!
🕒 Deployed at: ${formattedDate}
==========================================
`);

export default {
    port: 1340,
    fetch: app.fetch,
    idleTimeout: 255
}
