# Portfolio Enhancement: Paginated Token Buttons

## Overview

The portfolio display has been enhanced with pagination for token buttons, making the interface cleaner and more manageable when users have many tokens in their portfolio.

## Changes Made

### 1. Implemented Pagination for Token Buttons

- Added pagination to limit the number of token buttons displayed at once
- Set a default of 3 tokens per page to keep the interface clean
- Added navigation controls (Previous/Next) for browsing through tokens
- Added a page indicator showing current page and total pages

### 2. Added Session Storage for Pagination State

- Added `portfolioPage` property to the SessionData interface
- Implemented state management to remember the current page between interactions
- Ensured proper page boundaries are enforced

### 3. Enhanced Callback Handling

- Added handlers for pagination navigation buttons
- Implemented page switching logic with proper error handling
- Added informative tooltips for pagination controls

## Implementation Details

### Paginated Button Display

```typescript
// Get current page from session or default to 0
const currentPage = ctx.session.portfolioPage || 0;
const tokensPerPage = 3; // Number of tokens to show per page
const totalTokens = ctx.session.data.tokens.length;
const totalPages = Math.ceil(totalTokens / tokensPerPage);

// Calculate start and end indices for current page
const startIndex = currentPage * tokensPerPage;
const endIndex = Math.min(startIndex + tokensPerPage, totalTokens);

// Add buttons for tokens on current page
for (let i = startIndex; i < endIndex; i++) {
  // Add token button logic here
}

// Add pagination controls if needed
if (totalPages > 1) {
  const paginationRow = [];
  
  // Previous page button
  if (currentPage > 0) {
    paginationRow.push({
      text: "◀️ Previous",
      callback_data: `portfolio_page::${currentPage - 1}`
    });
  }
  
  // Page indicator
  paginationRow.push({
    text: `Page ${currentPage + 1}/${totalPages}`,
    callback_data: "portfolio_page_info"
  });
  
  // Next page button
  if (currentPage < totalPages - 1) {
    paginationRow.push({
      text: "Next ▶️",
      callback_data: `portfolio_page::${currentPage + 1}`
    });
  }
  
  inlineKeyboard.push(paginationRow);
}
```

### Pagination Callback Handlers

```typescript
// Handle portfolio pagination
else if (callbackData.startsWith('portfolio_page::')) {
  // Extract page number from callback data
  const pageNumber = parseInt(callbackData.split('::')[1]);
  if (!isNaN(pageNumber) && pageNumber >= 0) {
    // Update the current page in session
    ctx.session.portfolioPage = pageNumber;
    
    // Answer the callback query
    await ctx.answerCallbackQuery();
    
    // Refresh the portfolio display with the new page
    await handlePortfolio(ctx);
  } else {
    await ctx.answerCallbackQuery('Invalid page number');
  }
}
// Handle page info button click
else if (callbackData === 'portfolio_page_info') {
  await ctx.answerCallbackQuery('Use the navigation buttons to browse through your tokens');
}
```

## Benefits

1. **Improved User Experience**:
   - Cleaner interface with fewer buttons displayed at once
   - Easier navigation through large token collections
   - Clear visual indication of pagination status

2. **Better Performance**:
   - Reduced message size with fewer buttons per message
   - Faster rendering of portfolio display
   - More efficient use of Telegram's UI capabilities

3. **Enhanced Usability**:
   - Intuitive navigation controls
   - Consistent page size for better readability
   - Informative page indicators
