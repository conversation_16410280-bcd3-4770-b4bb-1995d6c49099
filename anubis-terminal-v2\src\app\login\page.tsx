"use client"
import { motion } from "framer-motion";
import Link from "next/link";
import { useState, useEffect } from "react";
import Navbar from "@/components/Landing/Navbar";
import { FaTelegram } from "react-icons/fa";
import AnimatedBackground from "@/components/ui/AnimatedBackground";

const telegramBotUsername = process.env.NODE_ENV === "production" ? process.env.NEXT_PUBLIC_BOT_USERNAME_PROD : process.env.NEXT_PUBLIC_BOT_USERNAME_DEV;


// Animated particles component
function AnimatedParticles() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== "undefined") {
      // Set initial value
      setIsMobile(window.innerWidth < 768);

      // Add event listener for window resize
      const handleResize = () => {
        setIsMobile(window.innerWidth < 768);
      };

      window.addEventListener("resize", handleResize);

      // Clean up
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  }, []);

  // Create an array of particles with different types - fewer on mobile for better performance
  const particleCount = isMobile ? 15 : 30;

  const particles = Array.from({ length: particleCount }).map((_, i) => ({
    id: i,
    type: Math.random() > 0.7 ? "star" : "circle",
    opacity: Math.random() * 0.3 + 0.1, // Random opacity between 0.1 and 0.4
  }));

  return (
    <>
      {particles.map((particle) => {
        // Random position and size for each particle - smaller on mobile
        const sizeFactor = isMobile ? 0.7 : 1;
        const size = (Math.random() * 5 + (particle.type === "star" ? 4 : 2)) * sizeFactor;
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const duration = Math.random() * 15 + (isMobile ? 15 : 10); // Slower on mobile for better performance
        const delay = Math.random() * 5;

        // Set opacity based on particle property
        const colorClass = `bg-white/[${particle.opacity}]`;

        // Different shapes based on particle type
        const shapeClass = particle.type === "star" ? "clip-path-star" : "rounded-full";

        return (
          <motion.div
            key={particle.id}
            className={`absolute ${colorClass} ${shapeClass}`}
            style={{
              width: size,
              height: size,
              left: `${x}%`,
              top: `${y}%`,
              clipPath:
                particle.type === "star"
                  ? "polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)"
                  : "none",
            }}
            animate={{
              y: [0, -150],
              x: [0, Math.random() * 40 - 20], // Random horizontal movement
              opacity: [0, 0.9, 0],
              scale: [1, particle.type === "star" ? 1.2 : 0.5],
              rotate: particle.type === "star" ? [0, 360] : 0,
            }}
            transition={{
              duration,
              repeat: Infinity,
              delay,
              ease: "linear",
            }}
          />
        );
      })}
    </>
  );
}

// Terminal-like typing effect component
function TypingEffect({ text, delay = 0 }: { text: string; delay?: number }) {
  const [displayedText, setDisplayedText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayedText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, 50); // Typing speed

      return () => clearTimeout(timeout);
    } else {
      setIsComplete(true);
    }
  }, [currentIndex, text]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay }}
      className="inline-block"
    >
      {displayedText}
      {!isComplete && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 0.8, repeat: Infinity }}
          className="inline-block ml-1 w-2 h-4 bg-white"
        ></motion.span>
      )}
    </motion.div>
  );
}

export default function LoginPage() {
  return (
    <>
      {/* <Navbar /> */}
      <div className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden px-4">
        {/* Background elements */}
        <AnimatedBackground className="z-0" />
        <AnimatedParticles />

        {/* Login container */}
        <motion.div
          className="relative z-10 flex flex-col items-center justify-center gap-8 max-w-4xl mx-auto text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Logo */}
          <motion.div
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: 0.2
            }}
            className="mb-6 relative"
          >
            {/* Glowing circle behind logo */}
            <motion.div
              className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 rounded-full bg-white/5 blur-xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.5, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            {/* Logo with floating animation */}
            <motion.div
              animate={{
                y: [0, -10, 0],
              }}
              transition={{
                y: {
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                },
              }}
            >
              <motion.img
                src="/logo/icon_no_bg.png"
                alt="Anubis Logo"
                className="w-32 h-32 object-contain mx-auto relative z-10"
                animate={{
                  filter: [
                    "drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))",
                    "drop-shadow(0 0 20px rgba(255, 255, 255, 0.5))",
                    "drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))",
                  ],
                }}
                transition={{
                  filter: {
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  },
                }}
              />
            </motion.div>
          </motion.div>

          {/* Welcome text */}
          <motion.div
            className="space-y-4"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-orbitron font-bold text-white">
              Welcome to Anubis Terminal
            </h1>
            <p className="text-gray-400 font-space-grotesk max-w-md mx-auto text-lg">
              The most advanced on-chain trading terminal for professional traders.
            </p>
          </motion.div>

          {/* Terminal-like message */}
          <motion.div
            className="w-full max-w-md bg-black/50 border border-white/20 rounded-md p-4 font-mono text-sm text-left"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span className="ml-2 text-gray-400">Terminal</span>
            </div>
            <div className="font-geist-mono text-white">
              <span className="text-green-500">anubis@terminal:~$</span>{" "}
              <TypingEffect text="connect --wallet telegram" delay={0.8} />
            </div>
          </motion.div>

          {/* Features list */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-2xl w-full"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            {[
              "Lightning-fast trading execution",
              "Advanced MEV protection",
              "Real-time market data",
              "Secure wallet integration",
              "Customizable trading interface",
              "24/7 trading support",
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-2 text-left"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 1 + index * 0.1 }}
              >
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <p className="text-gray-300 font-space-grotesk">{feature}</p>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Button */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 1.8 }}
            className="mt-8 relative"
          >
            {/* Button glow effect */}
            <motion.div
              className="absolute -inset-2 bg-white/10 rounded-lg blur-lg"
              animate={{
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            <Link href={`https://t.me/${telegramBotUsername}?start=terminal`}>
              <motion.button
                className="relative flex items-center justify-center gap-3 font-orbitron font-semibold bg-white text-black px-10 py-4 rounded-md text-lg w-full sm:w-auto"
                whileHover={{
                  boxShadow: "0 0 20px rgba(255, 255, 255, 0.5)",
                }}
                whileTap={{
                  backgroundColor: "#f0f0f0",
                }}
              >
                {/* Telegram icon with subtle animation */}
                <motion.div
                  animate={{
                    rotate: [0, 5, 0, -5, 0],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="text-[#0088cc]"
                >
                  <FaTelegram className="text-2xl" />
                </motion.div>

                <span>Start Trading Now</span>
              </motion.button>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}
