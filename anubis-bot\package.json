{"name": "tg-sigma", "version": "0.1.0", "author": "", "main": "index.js", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@grammyjs/menu": "1.2.1", "@grammyjs/parse-mode": "1.10.0", "@grammyjs/storage-free": "2.4.2", "@hono/zod-validator": "^0.6.0", "@uniswap/sdk-core": "5.0.0", "@uniswap/smart-order-router": "3.32.0", "axios": "^1.9.0", "ethers": "5.7.2", "grammy": "1.25.1", "hono": "^4.7.10", "node-fetch-cache": "4.1.1", "viem": "2.13.3", "zod": "^3.25.34"}, "description": "", "keywords": [], "license": "ISC", "scripts": {"dev": "bun run index.ts", "start": "bun dev", "api": "bun run src/api/server.ts", "api:watch": "nodemon --exec bun run src/api/server.ts", "deploy:api": "bun run src/api/server.ts"}, "devDependencies": {"nodemon": "^3.1.10"}}