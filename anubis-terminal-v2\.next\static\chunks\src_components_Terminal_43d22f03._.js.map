{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Navbar/SettingsModal/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useWeb3 } from '@/contexts/Web3Context';\r\nimport { chains } from '@/utils/data';\r\nimport { X } from 'lucide-react';\r\nimport { IoMdSettings } from 'react-icons/io';\r\nimport { FaWallet, FaExchangeAlt, FaCog } from 'react-icons/fa';\r\nimport { TiTick } from 'react-icons/ti';\r\nimport { updateSettings, setPrimaryWallet } from '@/services/bot';\r\nimport { toast } from 'react-toastify';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useUserData } from '@/hooks/useUserData';\r\nimport { IoChevronDown } from 'react-icons/io5';\r\nimport { Loader2 } from 'lucide-react';\r\n\r\ntype SettingsTab = 'general' | 'wallet' | 'trading';\r\n\r\nexport default function SettingsModal({\r\n    openSettingsModal,\r\n    setOpenSettingsModal\r\n}: {\r\n    openSettingsModal: boolean,\r\n    setOpenSettingsModal: React.Dispatch<React.SetStateAction<boolean>>\r\n}) {\r\n    const { selectedChain, setSelectedChain } = useWeb3();\r\n    const { token } = useAuth();\r\n    const { data, refetch } = useUserData(token as string);\r\n    const [activeTab, setActiveTab] = useState<SettingsTab>('general');\r\n    const [slippage, setSlippage] = useState<\"0.1\" | \"0.5\" | \"1\" | string>('0.5');\r\n    const [gasPrice, setGasPrice] = useState<string>('10');\r\n    const [newPrimaryWallet, setNewPrimaryWallet] = useState<`0x${string}`>('0x');\r\n\r\n    const gasPriceOptions = [\r\n        { value: '10', label: 'Slow' },\r\n        { value: '20', label: 'Standard' },\r\n        { value: '30', label: 'Fast' }\r\n    ];\r\n    const [selectedWalletAddress, setSelectedWalletAddress] = useState<string>('');\r\n    const [showWallets, setShowWallets] = useState<boolean>(false);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n\r\n    if (!openSettingsModal) return null;\r\n\r\n    const tabIcons = {\r\n        general: <FaCog className=\"mr-2\" />,\r\n        wallet: <FaWallet className=\"mr-2\" />,\r\n        trading: <FaExchangeAlt className=\"mr-2\" />\r\n    };\r\n\r\n    const handleSlippageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const value = e.target.value;\r\n        if (value === '' || /^\\d*\\.?\\d*$/.test(value)) {\r\n            setSlippage(value);\r\n        }\r\n    };\r\n\r\n    const handleChainChange = (chainId: number) => {\r\n        const chain = chains.find(c => c.chainId === chainId);\r\n        if (chain) setSelectedChain(chain);\r\n    };\r\n\r\n    const handleSaveSettings = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await updateSettings(token as string, {\r\n                slippage: Number(slippage),\r\n                gasPrice: Number(gasPrice),\r\n                chainId: selectedChain?.chainId,\r\n            });\r\n            console.log(\"newPrimaryWallet\", newPrimaryWallet);\r\n            if(newPrimaryWallet !== \"0x\") {\r\n                await setPrimaryWallet(token as string, newPrimaryWallet);\r\n            }\r\n\r\n            console.log('Settings saved:', response);\r\n            refetch();\r\n            toast.success('Settings saved successfully');\r\n        } catch (error) {\r\n            console.error('Error saving settings:', error);\r\n            toast.error('Failed to save settings');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (data) {\r\n            setSlippage(data.settings.slippage.toString());\r\n            setGasPrice(data.settings.gasPrice.toString());\r\n        }\r\n    }, [data]);\r\n\r\n    return (\r\n        <AnimatePresence>\r\n            <motion.div\r\n                className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                exit={{ opacity: 0 }}\r\n            >\r\n                <motion.div\r\n                    className=\"fixed inset-0 bg-black/80 backdrop-blur-sm\"\r\n                    onClick={() => setOpenSettingsModal(false)}\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                />\r\n\r\n                <motion.div\r\n                    className=\"bg-black/95 backdrop-blur-sm w-full max-w-md h-fit md:h-auto overflow-hidden shadow-2xl relative z-10 border border-white/10\"\r\n                    initial={{ x: '100%', opacity: 0 }}\r\n                    animate={{ x: 0, opacity: 1 }}\r\n                    exit={{ x: '100%', opacity: 0 }}\r\n                    transition={{ duration: 0.3, ease: 'easeInOut' }}\r\n                    onClick={(e) => { e.stopPropagation() }}\r\n                >\r\n                    {/* Header */}\r\n                    <div className=\"flex items-center justify-between p-4 border-b border-white/10\">\r\n                        <div className=\"flex items-center\">\r\n                            <IoMdSettings className=\"text-blue-400 mr-2 text-xl\" />\r\n                            <h3 className=\"text-lg font-orbitron font-bold text-white\">Settings</h3>\r\n                        </div>\r\n                        <button\r\n                            onClick={() => setOpenSettingsModal(false)}\r\n                            className=\"p-2 hover:bg-white/10 rounded-md transition-colors\"\r\n                        >\r\n                            <X size={20} className=\"text-white\" />\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Tabs */}\r\n                    <div className=\"flex border-b border-white/10 bg-black/50\">\r\n                        {(Object.keys(tabIcons) as SettingsTab[]).map((tab) => (\r\n                            <button\r\n                                key={tab}\r\n                                onClick={() => setActiveTab(tab)}\r\n                                className={`flex-1 py-3 text-sm font-space-grotesk font-medium transition-colors ${activeTab === tab\r\n                                    ? 'text-blue-400 border-b-2 border-blue-400 bg-gradient-to-b from-blue-400/5 to-transparent'\r\n                                    : 'text-white/60 hover:text-white/90 hover:bg-white/5'\r\n                                    }`}\r\n                            >\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    {tabIcons[tab]}\r\n                                    {tab.charAt(0).toUpperCase() + tab.slice(1)}\r\n                                </div>\r\n                            </button>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Tab Content */}\r\n                    <div className=\"p-4 space-y-4 overflow-y-auto max-h-[60vh] md:max-h-[50vh]\">\r\n                        <AnimatePresence mode=\"wait\">\r\n                            <motion.div\r\n                                key={activeTab}\r\n                                initial={{ opacity: 0, y: 10 }}\r\n                                animate={{ opacity: 1, y: 0 }}\r\n                                exit={{ opacity: 0, y: -10 }}\r\n                                transition={{ duration: 0.2 }}\r\n                                className=\"space-y-4\"\r\n                            >\r\n                                {activeTab === 'general' && (\r\n                                    <div className=\"space-y-4\">\r\n                                        <div>\r\n                                            <label className=\"block text-sm font-space-grotesk font-medium text-white/80 mb-2\">\r\n                                                Network\r\n                                            </label>\r\n                                            <div className=\"grid grid-cols-2 gap-2\">\r\n                                                {chains.map((chain) => (\r\n                                                    <motion.button\r\n                                                        key={chain.chainId}\r\n                                                        onClick={() => handleChainChange(chain.chainId)}\r\n                                                        className={`flex items-center gap-2 p-3 rounded-md transition-colors ${selectedChain?.chainId === chain.chainId ? 'bg-white/10' : 'hover:bg-white/5'}`}\r\n                                                        whileTap={{ scale: 0.98 }}\r\n                                                    >\r\n                                                        <img src={chain.logo} alt={chain.chain} className=\"w-5 h-5 object-contain\" />\r\n                                                        <span className=\"font-space-grotesk text-sm\">{chain.chain}</span>\r\n                                                        {selectedChain?.chainId === chain.chainId && (\r\n                                                            <TiTick className=\"text-blue-400 ml-auto\" />\r\n                                                        )}\r\n                                                    </motion.button>\r\n                                                ))}\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                )}\r\n\r\n                                {activeTab === 'wallet' && (\r\n                                    <div className=\"space-y-4 h-fit\">\r\n                                        <div className=\"mb-2\">\r\n                                            <h3 className=\"text-sm font-space-grotesk font-medium text-white/90 mb-1\">\r\n                                                Primary Wallet\r\n                                            </h3>\r\n                                            <p className=\"text-xs text-white/60\">Your primary wallet for all transactions</p>\r\n                                        </div>\r\n\r\n                                        <div className=\"relative\">\r\n                                            <button\r\n                                                type=\"button\"\r\n                                                className=\"w-full flex items-center justify-between px-4 py-3 bg-white/5 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200\"\r\n                                                onClick={() => setShowWallets(!showWallets)}\r\n                                                aria-haspopup=\"listbox\"\r\n                                                aria-expanded={showWallets}\r\n                                            >\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                    <div className=\"w-2 h-2 rounded-full bg-green-400 animate-pulse\" />\r\n                                                    <span className=\"text-white font-mono text-sm\">\r\n                                                        {selectedWalletAddress\r\n                                                            ? `${selectedWalletAddress.substring(0, 6)}...${selectedWalletAddress.slice(-4)}`\r\n                                                            : 'Select a wallet'}\r\n                                                    </span>\r\n                                                </div>\r\n                                                <motion.div\r\n                                                    animate={{ rotate: showWallets ? 180 : 0 }}\r\n                                                    transition={{ duration: 0.2 }}\r\n                                                >\r\n                                                    <IoChevronDown className=\"text-white/60 text-lg\" />\r\n                                                </motion.div>\r\n                                            </button>\r\n\r\n                                            <AnimatePresence>\r\n                                                {showWallets && data?.wallets && data.wallets.length > 0 && (\r\n                                                    <motion.div\r\n                                                        initial={{ opacity: 0, y: -10 }}\r\n                                                        animate={{ opacity: 1, y: 0 }}\r\n                                                        exit={{ opacity: 0, y: -10 }}\r\n                                                        transition={{ duration: 0.15 }}\r\n                                                        className=\"fixed z-[100] mt-1 w-[calc(100%-3rem)] max-w-[28rem] bg-gray-900/95 backdrop-blur-sm rounded-lg border border-white/10 shadow-xl overflow-hidden h-fit\"\r\n                                                        onClick={(e) => e.stopPropagation()}\r\n                                                    >\r\n                                                        <div className=\"max-h-[50vh] overflow-y-auto custom-scrollbar\">\r\n                                                            {data.wallets.map((wallet: { address: string }, index: number) => (\r\n                                                                <button\r\n                                                                    key={index}\r\n                                                                    className={`w-full text-left px-4 py-3 text-sm font-mono transition-colors flex items-center ${selectedWalletAddress === wallet.address\r\n                                                                        ? 'bg-blue-500/20 text-blue-400'\r\n                                                                        : 'text-white/80 hover:bg-white/5'}`}\r\n                                                                    onClick={() => {\r\n                                                                        setSelectedWalletAddress(wallet.address);\r\n                                                                        setShowWallets(false);\r\n                                                                        setNewPrimaryWallet(wallet.address as `0x${string}`);\r\n                                                                    }}\r\n                                                                >\r\n                                                                    <span className=\"truncate\">\r\n                                                                        {wallet.address}\r\n                                                                    </span>\r\n                                                                    {index === data?.user?.walletId && (\r\n                                                                        <TiTick className=\"ml-auto text-blue-400\" size={18} />\r\n                                                                    )}\r\n                                                                </button>\r\n                                                            ))}\r\n                                                        </div>\r\n                                                    </motion.div>\r\n                                                )}\r\n                                            </AnimatePresence>\r\n                                        </div>\r\n\r\n                                        {showWallets && (\r\n                                            <div\r\n                                                className=\"fixed inset-0 z-10\"\r\n                                                onClick={() => setShowWallets(false)}\r\n                                            />\r\n                                        )}\r\n                                    </div>\r\n                                )}\r\n\r\n                                {activeTab === 'trading' && (\r\n                                    <div className=\"space-y-6\">\r\n                                        <div>\r\n                                            <label className=\"block text-sm font-space-grotesk font-medium text-white/80 mb-2\">\r\n                                                Slippage Tolerance\r\n                                            </label>\r\n                                            <div className=\"flex items-center space-x-3\">\r\n                                                <div className=\"relative flex-1\">\r\n                                                    <div className=\"relative\">\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            value={slippage}\r\n                                                            onChange={handleSlippageChange}\r\n                                                            className=\"w-full bg-white/5 text-white rounded-lg border border-white/10 px-4 py-3 text-sm focus:ring-2 focus:ring-blue-400/50 focus:border-transparent pr-10\"\r\n                                                            readOnly\r\n                                                        />\r\n                                                        <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50\">%</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex space-x-2 mt-3\">\r\n                                                {['0.1', '0.5', '1.0'].map((value) => (\r\n                                                    <motion.button\r\n                                                        key={value}\r\n                                                        type=\"button\"\r\n                                                        onClick={() => setSlippage(value)}\r\n                                                        whileHover={{ scale: 1.03 }}\r\n                                                        whileTap={{ scale: 0.98 }}\r\n                                                        className={`px-4 py-2 text-xs rounded-lg font-space-grotesk font-medium transition-colors ${slippage === value\r\n                                                            ? 'bg-blue-500 text-white'\r\n                                                            : 'bg-white/5 text-white/70 hover:bg-white/10'\r\n                                                            }`}\r\n                                                    >\r\n                                                        {value}%\r\n                                                    </motion.button>\r\n                                                ))}\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <div>\r\n                                            <label className=\"block text-sm font-space-grotesk font-medium text-white/80 mb-2\">\r\n                                                Gas Price\r\n                                            </label>\r\n                                            <div className=\"space-y-2\">\r\n                                                {gasPriceOptions.map((option) => (\r\n                                                    <motion.button\r\n                                                        key={option.value}\r\n                                                        onClick={() => setGasPrice(option.value)}\r\n                                                        className={`w-full flex items-center justify-between p-3 rounded-md transition-colors ${gasPrice === option.value ? 'bg-blue-500/10 border border-blue-500/30' : 'hover:bg-white/5 border border-white/5'}`}\r\n                                                        whileTap={{ scale: 0.98 }}\r\n                                                    >\r\n                                                        <span className=\"font-space-grotesk text-sm\">\r\n                                                            {option.label}\r\n                                                        </span>\r\n                                                        {gasPrice === option.value && (\r\n                                                            <TiTick className=\"text-blue-400\" />\r\n                                                        )}\r\n                                                    </motion.button>\r\n                                                ))}\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                )}\r\n                            </motion.div>\r\n                        </AnimatePresence>\r\n                    </div>\r\n\r\n                    {/* Footer */}\r\n                    <div className=\"p-4 border-t border-white/10 flex justify-end space-x-3 bg-black/30\">\r\n                        <motion.button\r\n                            onClick={() => setOpenSettingsModal(false)}\r\n                            whileTap={{ scale: 0.98 }}\r\n                            className=\"px-4 py-2 text-sm font-space-grotesk font-medium text-white/80 hover:text-white hover:bg-white/10 rounded-md transition-colors\"\r\n                        >\r\n                            Cancel\r\n                        </motion.button>\r\n                        <motion.button\r\n                            onClick={handleSaveSettings}\r\n                            whileHover={{ scale: 1.02 }}\r\n                            whileTap={{ scale: 0.98 }}\r\n                            className=\"px-4 py-2 text-sm font-orbitron font-medium text-white bg-blue-500 hover:bg-blue-500 rounded-md transition-colors \r\n                            flex gap-x-3 items-center\"\r\n                        >\r\n                            <span>Save Settings</span>\r\n                            {loading && <Loader2 className=\"w-4 h-4 animate-spin\" />}\r\n                        </motion.button>\r\n                    </div>\r\n                </motion.div>\r\n            </motion.div>\r\n        </AnimatePresence>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;;AAmBe,SAAS,cAAc,EAClC,iBAAiB,EACjB,oBAAoB,EAIvB;;IACG,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,kBAAkB;QACpB;YAAE,OAAO;YAAM,OAAO;QAAO;QAC7B;YAAE,OAAO;YAAM,OAAO;QAAW;QACjC;YAAE,OAAO;YAAM,OAAO;QAAO;KAChC;IACD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,IAAI,CAAC,mBAAmB,OAAO;IAE/B,MAAM,WAAW;QACb,uBAAS,6LAAC,iJAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC1B,sBAAQ,6LAAC,iJAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC5B,uBAAS,6LAAC,iJAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;IACtC;IAEA,MAAM,uBAAuB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,UAAU,MAAM,cAAc,IAAI,CAAC,QAAQ;YAC3C,YAAY;QAChB;IACJ;IAEA,MAAM,oBAAoB,CAAC;QACvB,MAAM,QAAQ,uHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QAC7C,IAAI,OAAO,iBAAiB;IAChC;IAEA,MAAM,qBAAqB;QACvB,IAAI;YACA,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD,EAAE,OAAiB;gBACnD,UAAU,OAAO;gBACjB,UAAU,OAAO;gBACjB,SAAS,eAAe;YAC5B;YACA,QAAQ,GAAG,CAAC,oBAAoB;YAChC,IAAG,qBAAqB,MAAM;gBAC1B,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,OAAiB;YAC5C;YAEA,QAAQ,GAAG,CAAC,mBAAmB;YAC/B;YACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QAChB,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,MAAM;gBACN,YAAY,KAAK,QAAQ,CAAC,QAAQ,CAAC,QAAQ;gBAC3C,YAAY,KAAK,QAAQ,CAAC,QAAQ,CAAC,QAAQ;YAC/C;QACJ;kCAAG;QAAC;KAAK;IAET,qBACI,6LAAC,4LAAA,CAAA,kBAAe;kBACZ,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACP,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;;8BAEnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACP,WAAU;oBACV,SAAS,IAAM,qBAAqB;oBACpC,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;;;;;;8BAGvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACP,WAAU;oBACV,SAAS;wBAAE,GAAG;wBAAQ,SAAS;oBAAE;oBACjC,SAAS;wBAAE,GAAG;wBAAG,SAAS;oBAAE;oBAC5B,MAAM;wBAAE,GAAG;wBAAQ,SAAS;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,SAAS,CAAC;wBAAQ,EAAE,eAAe;oBAAG;;sCAGtC,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,iJAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;;;;;;;8CAE/D,6LAAC;oCACG,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;sCACV,AAAC,OAAO,IAAI,CAAC,UAA4B,GAAG,CAAC,CAAC,oBAC3C,6LAAC;oCAEG,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,qEAAqE,EAAE,cAAc,MAC3F,6FACA,sDACA;8CAEN,cAAA,6LAAC;wCAAI,WAAU;;4CACV,QAAQ,CAAC,IAAI;4CACb,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;;;;;;;mCATxC;;;;;;;;;;sCAgBjB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CAClB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAEP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;wCAET,cAAc,2BACX,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;;kEACG,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDAAI,WAAU;kEACV,uHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,CAAC,sBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gEAEV,SAAS,IAAM,kBAAkB,MAAM,OAAO;gEAC9C,WAAW,CAAC,yDAAyD,EAAE,eAAe,YAAY,MAAM,OAAO,GAAG,gBAAgB,oBAAoB;gEACtJ,UAAU;oEAAE,OAAO;gEAAK;;kFAExB,6LAAC;wEAAI,KAAK,MAAM,IAAI;wEAAE,KAAK,MAAM,KAAK;wEAAE,WAAU;;;;;;kFAClD,6LAAC;wEAAK,WAAU;kFAA8B,MAAM,KAAK;;;;;;oEACxD,eAAe,YAAY,MAAM,OAAO,kBACrC,6LAAC,iJAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;+DARjB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;wCAiBzC,cAAc,0BACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAG1E,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAGzC,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DACG,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,eAAe,CAAC;4DAC/B,iBAAc;4DACd,iBAAe;;8EAEf,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFACX,wBACK,GAAG,sBAAsB,SAAS,CAAC,GAAG,GAAG,GAAG,EAAE,sBAAsB,KAAK,CAAC,CAAC,IAAI,GAC/E;;;;;;;;;;;;8EAGd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oEACP,SAAS;wEAAE,QAAQ,cAAc,MAAM;oEAAE;oEACzC,YAAY;wEAAE,UAAU;oEAAI;8EAE5B,cAAA,6LAAC,kJAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIjC,6LAAC,4LAAA,CAAA,kBAAe;sEACX,eAAe,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,mBACnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACP,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC3B,YAAY;oEAAE,UAAU;gEAAK;gEAC7B,WAAU;gEACV,SAAS,CAAC,IAAM,EAAE,eAAe;0EAEjC,cAAA,6LAAC;oEAAI,WAAU;8EACV,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAA6B,sBAC5C,6LAAC;4EAEG,WAAW,CAAC,iFAAiF,EAAE,0BAA0B,OAAO,OAAO,GACjI,iCACA,kCAAkC;4EACxC,SAAS;gFACL,yBAAyB,OAAO,OAAO;gFACvC,eAAe;gFACf,oBAAoB,OAAO,OAAO;4EACtC;;8FAEA,6LAAC;oFAAK,WAAU;8FACX,OAAO,OAAO;;;;;;gFAElB,UAAU,MAAM,MAAM,0BACnB,6LAAC,iJAAA,CAAA,SAAM;oFAAC,WAAU;oFAAwB,MAAM;;;;;;;2EAd/C;;;;;;;;;;;;;;;;;;;;;;;;;;gDAwBhC,6BACG,6LAAC;oDACG,WAAU;oDACV,SAAS,IAAM,eAAe;;;;;;;;;;;;wCAM7C,cAAc,2BACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAM,WAAU;sEAAkE;;;;;;sEAGnF,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAI,WAAU;;sFACX,6LAAC;4EACG,MAAK;4EACL,OAAO;4EACP,UAAU;4EACV,WAAU;4EACV,QAAQ;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;sFAAoE;;;;;;;;;;;;;;;;;;;;;;sEAIhG,6LAAC;4DAAI,WAAU;sEACV;gEAAC;gEAAO;gEAAO;6DAAM,CAAC,GAAG,CAAC,CAAC,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oEAEV,MAAK;oEACL,SAAS,IAAM,YAAY;oEAC3B,YAAY;wEAAE,OAAO;oEAAK;oEAC1B,UAAU;wEAAE,OAAO;oEAAK;oEACxB,WAAW,CAAC,8EAA8E,EAAE,aAAa,QACnG,2BACA,8CACA;;wEAEL;wEAAM;;mEAVF;;;;;;;;;;;;;;;;8DAgBrB,6LAAC;;sEACG,6LAAC;4DAAM,WAAU;sEAAkE;;;;;;sEAGnF,6LAAC;4DAAI,WAAU;sEACV,gBAAgB,GAAG,CAAC,CAAC,uBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oEAEV,SAAS,IAAM,YAAY,OAAO,KAAK;oEACvC,WAAW,CAAC,0EAA0E,EAAE,aAAa,OAAO,KAAK,GAAG,6CAA6C,0CAA0C;oEAC3M,UAAU;wEAAE,OAAO;oEAAK;;sFAExB,6LAAC;4EAAK,WAAU;sFACX,OAAO,KAAK;;;;;;wEAEhB,aAAa,OAAO,KAAK,kBACtB,6LAAC,iJAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;mEATjB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;mCA9JpC;;;;;;;;;;;;;;;sCAoLjB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACV,SAAS,IAAM,qBAAqB;oCACpC,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACb;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACV,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAGV,6LAAC;sDAAK;;;;;;wCACL,yBAAW,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D;GAnVwB;;QAOwB,kIAAA,CAAA,UAAO;QACjC,kIAAA,CAAA,UAAO;QACC,8HAAA,CAAA,cAAW;;;KATjB", "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Navbar/index.tsx"], "sourcesContent": ["\"use client\";\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { terminalNavLinks, GlobalNavLinks, chains } from \"@/utils/data\";\nimport { usePathname } from \"next/navigation\";\nimport { FaPaste, FaUserAlt, FaEye, FaCopy, FaEyeSlash, FaQrcode, FaCaretDown, FaGift, FaWallet, FaUsers, FaQuestionCircle } from \"react-icons/fa\";\nimport { FaMagnifyingGlass } from \"react-icons/fa6\";\nimport { GiHamburgerMenu } from \"react-icons/gi\";\nimport { IoMdCog, IoMdClose } from \"react-icons/io\";\nimport { FaLink } from \"react-icons/fa6\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TiTick } from \"react-icons/ti\";\nimport { useWeb3 } from \"@/contexts/Web3Context\";\nimport { IoMdNotifications, IoIosLogOut } from \"react-icons/io\";\nimport { IoDocumentTextOutline } from \"react-icons/io5\";\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { useUserData } from \"@/hooks/useUserData\";\nimport { toast } from 'react-toastify';\nimport { updateSettings } from \"@/services/bot\";\nimport { ImSpinner2 } from \"react-icons/im\"; // Add this import for the spinner\nimport SettingsModal from \"./SettingsModal\";\n\nexport type Chain = typeof chains[number];\n\nexport default function Navbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);\n    const [openSlider, setOpenSlider] = useState<boolean>(false);\n    const [chainSelector, setChainSelector] = useState<boolean>(false);\n    const [settingsModal, setSettingsModal] = useState<boolean>(false);\n    const { selectedChain, setSelectedChain, recentlyViewed } = useWeb3();\n    const { token } = useAuth();\n    const { data, error } = useUserData(token);\n    const [chainLoading, setChainLoading] = useState<string | null>(null); // Add loading state\n    const [tokenSearch, setTokenSearch] = useState<boolean>(false);\n\n    const pathname = usePathname();\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n        setOpenSlider(!openSlider);\n    };\n\n    useEffect(() => {\n        if (error) {\n            toast.error(\"Unable to load user data. Please refresh the page or try again later.\");\n        }\n    }, [error]);\n\n    const balance = data && data.balance ? Number(data?.balance) : 0;\n\n    const filteredRecentlyViewed = recentlyViewed.filter(token => token.chain === selectedChain?.slug);\n\n    // Write Async Call to Change Chain ID\n    async function handleSettingsUpdate(selectedChain: Chain) {\n        setChainLoading(selectedChain.chain); // Set loading for this chain\n        const chainId = selectedChain.chainId;\n        try {\n            await updateSettings(token as string, { chainId });\n            setSelectedChain(selectedChain);\n            setChainSelector(false);\n            toast.success(\"Network switched successfully\");\n        } catch (error: any) {\n            if (error && error.response) {\n                toast.error(error.response.data.message);\n            }\n        } finally {\n            setChainLoading(null); // Remove loading state\n        }\n    }\n\n    // Fallback UI if selectedChain is not loaded\n    if (!selectedChain) {\n        return (\n            <section className=\"flex justify-center items-center h-16 w-full\">\n                <ImSpinner2 className=\"animate-spin text-white text-2xl\" />\n            </section>\n        );\n    }\n\n    return (\n        <section className=\"flex justify-between items-center flex-col max-w-full\">\n            <div className=\"items-center justify-start p-1 border-b border-white/10 min-w-full gap-x-2 md:flex hidden\">\n                <span className=\"text-sm font-space-grotesk font-medium text-gray-400 px-2.5 border-r border-white/30\">Recent</span>\n                <div className=\"flex gap-4 items-center px-2.5 hover:cursor-pointer\">\n                    {filteredRecentlyViewed.map((token, index) => {\n                        const tokenData = token.data?.results?.[0];\n                        if (!tokenData) return null;\n                        const { details, marketData } = tokenData;\n                        return (\n                            <div key={index} className=\"flex items-center gap-2\">\n                                {details?.imageThumbUrl ? (\n                                    <img src={details.imageThumbUrl} className=\"w-3 h-3 object-contain rounded-full\" alt={details.symbol} />\n                                ) : (\n                                    <div className=\"w-3 h-3 bg-gray-700 rounded-full flex items-center justify-center text-[8px]\">{details?.symbol?.charAt(0) ?? '?'}</div>\n                                )}\n                                <span className=\"font-space-grotesk font-medium text-sm\">{details?.symbol ?? ''}</span>\n                                <span className=\"font-space-grotesk font-medium text-[10px] text-white/60\">{marketData?.priceUSD ? `$${parseFloat(marketData.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 6 })}` : '-'}</span>\n                            </div>\n                        );\n                    })}\n                </div>\n            </div>\n            <nav className=\"min-w-full border-b border-white/10 py-3 px-2 relative flex items-center justify-between\">\n                <div className=\"flex items-center justify-start gap-x-5\">\n                    <Link href=\"/terminal/trending\" className=\"flex items-center justify-start z-10 gap-2\">\n                        <img src=\"/logo/icon.png\" className=\"w-5 h-5 object-contain\" alt=\"Anubis Logo\" />\n                        <span className=\"font-orbitron font-bold text-sm\">Anubis</span>\n                    </Link>\n                    <div className=\"items-center gap-x-5 capitalize md:flex hidden\">\n                        {terminalNavLinks.map((link: GlobalNavLinks) => (\n                            <Link href={link.url} key={link.title} className={`font-space-grotesk font-medium hover:text-white text-sm ${link.url.includes(pathname) ? \"text-white\" : \"text-gray-400\"} `}>\n                                {link.title}\n                            </Link>\n                        ))}\n                    </div>\n                </div>\n                <div className=\"flex items-stretch md:gap-x-2 gap-x-1 justify-end\">\n                    <motion.button className=\"text-[10px] md:text-sm  font-space-grotesk font-medium text-gray-400 hover:text-white items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer md:hidden flex\"\n                        initial={{\n                            backgroundColor: \"rgba(255, 255, 255, 1)\",\n                        }}\n                        whileTap={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.05)\",\n                            scale: 1.03\n                        }}\n                    >\n                        <FaLink className=\"text-black\" />\n                        <span className=\"text-black font-bold\">Share</span>\n                    </motion.button>\n                    <motion.button className=\"text-[10px] md:text-sm  font-space-grotesk font-medium text-gray-400 hover:text-white flex items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer\"\n                        whileHover={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                        }}\n                        whileTap={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.05)\",\n                            scale: 1.03\n                        }}\n                    >\n                        <FaPaste className=\"text-white\" />\n                        <span className=\"text-sm\">Paste CA</span>\n                    </motion.button>\n                    <motion.button className=\"text-[10px] md:text-sm font-space-grotesk font-medium text-gray-400 hover:text-white flex items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer\"\n                        whileHover={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                        }}\n                        whileTap={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.05)\",\n                            scale: 1.03\n                        }}\n                        onClick={() => setTokenSearch(true)}\n                        >\n                        <FaMagnifyingGlass className=\"text-white\" />\n                        <span className=\"text-sm\">Search</span>\n                        <span className=\"text-[10px] md:text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden md:flex items-center gap-2 border border-white/30 px-2  rounded-sm hover:cursor-pointer\">\n                            /\n                        </span>\n                    </motion.button>\n                    <motion.button className=\"text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer md:flex\"\n                        whileHover={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                        }}\n                        whileTap={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.05)\",\n                            scale: 1.03\n                        }}\n                        onClick={() => setChainSelector(true)}\n                    >\n                        <FaWallet className=\"text-white\" />\n                        <span>{balance.toFixed(2)}</span>\n                        {selectedChain ? (\n                            <img src={selectedChain.logo} className=\"w-5 h-5 object-contain\" alt={selectedChain.chain} />\n                        ) : (\n                            <ImSpinner2 className=\"animate-spin text-white w-5 h-5\" />\n                        )}\n                    </motion.button>\n                    <motion.button className=\"text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer md:flex\"\n                        whileHover={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                        }}\n                        whileTap={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.05)\",\n                            scale: 1.03\n                        }}\n                        onClick={() => setSettingsModal(true)}>\n                        <IoMdCog className=\"text-white\" />\n                    </motion.button>\n                    <motion.button className=\"text-sm font-space-grotesk font-medium text-gray-400 hover:text-white hidden items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer min-h-full md:flex\"\n                        whileHover={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                        }}\n                        whileTap={{\n                            backgroundColor: \"rgba(255, 255, 255, 0.05)\",\n                            scale: 1.03\n                        }}\n                        onClick={() => setOpenSlider(!openSlider)}\n                    >\n                        <FaUserAlt className=\"text-white\" />\n                    </motion.button>\n\n                    {/* Mobile Menu Button */}\n                    <motion.button\n                        className=\"text-sm font-space-grotesk font-medium text-gray-400 hover:text-white items-center gap-2 border border-white/30 px-2.5 py-1 rounded-sm hover:cursor-pointer min-h-full flex md:hidden\"\n                        onClick={toggleMenu}\n                        aria-label=\"Toggle menu\"\n                        whileTap={{ backgroundColor: \"rgba(255, 255, 255, 0.1)\" }}\n                    >\n                        {isMenuOpen ? <IoMdClose className=\"text-[10px]\" /> : <GiHamburgerMenu className=\"text-[10px]\" />}\n                    </motion.button>\n                </div>\n            </nav>\n            <AnimatePresence mode=\"wait\">\n                {openSlider && <SliderMenu openSlider={openSlider} setOpenSlider={setOpenSlider} />}\n                {chainSelector && (\n                    <ChainSelectorModal\n                        chainSelector={chainSelector}\n                        setChainSelector={setChainSelector}\n                        handleSettingsUpdate={handleSettingsUpdate}\n                        chainLoading={chainLoading} // Pass loading state\n                    />\n                )}\n                {tokenSearch && (\n                    <TokenSearchModal\n                        tokenSearch={tokenSearch}\n                        setTokenSearch={setTokenSearch}\n                    />\n                )}\n                {settingsModal && (\n                    <SettingsModal\n                        openSettingsModal={settingsModal}\n                        setOpenSettingsModal={setSettingsModal}\n                    />\n                )}\n            </AnimatePresence>\n        </section>\n    )\n}\n\nfunction SliderMenu({ openSlider, setOpenSlider }: { openSlider: boolean, setOpenSlider: React.Dispatch<React.SetStateAction<boolean>> }) {\n    const [openProfileDropdown, setOpenProfileDropdown] = useState<boolean>(false);\n    const [profileDetailsBlurred, setProfileDetailsBlurred] = useState<boolean>(false);\n    const [openWalletDropdown, setOpenWalletDropdown] = useState<boolean>(false);\n    const [openRewardsDropdown, setOpenRewardsDropdown] = useState<boolean>(false);\n    const [openReferralsDropdown, setOpenReferralsDropdown] = useState<boolean>(false);\n    const { selectedChain, setSelectedChain, chainList } = useWeb3();\n    const { logout } = useAuth();\n    const { token } = useAuth();\n    const { data, loading, error } = useUserData(token);\r\n\r\n\r\n    return (\r\n        <motion.section className=\"min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n        >\r\n            <div className=\"absolute inset-0\" onClick={() => setOpenSlider(false)} />\r\n\r\n            <motion.nav className=\"inset-0 flex flex-col justify-start gap-y-3 transition-all duration-300 ease-in-out z-[60] md:min-w-[20.5%] md:max-w-[20.5%] w-[80%] border-white/10 border bg-black/80 p-3\"\r\n                initial={{ x: \"100%\" }}\r\n                animate={{ x: \"0%\" }}\r\n                exit={{ x: \"100%\" }}\r\n                transition={{ duration: 0.3 }}\r\n            >\r\n                <div className=\"\">\r\n                    <Link href=\"/\" className=\"flex items-center justify-start z-10 gap-2\">\r\n                        <img src=\"/logo/icon.png\" className=\"w-8 h-8 object-contain\" alt=\"Anubis Logo\" />\r\n                        <span className=\"font-orbitron font-bold text-xl\">Anubis</span>\r\n                    </Link>\r\n                </div>\r\n                {/* Profile Dropdown */}\r\n                <motion.div className=\"flex flex-col gap-x-2 justify-between bg-black border border-white/10 p-3 rounded-sm cursor-pointer\" onClick={() => setOpenProfileDropdown(!openProfileDropdown)}\r\n                >\r\n                    <div className=\"flex gap-x-2 justify-between w-full\">\r\n                        <div className=\"flex gap-x-2 items-center\">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <FaUserAlt className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className={`font-bold ${profileDetailsBlurred ? \"blur-sm\" : \"\"}`}>@{data?.user?.telegramUsername}</span>\r\n                                <span className={`flex items-center gap-x-1 ${profileDetailsBlurred ? \"blur-sm\" : \"\"}`}>ID: <span className=\"truncate w-10\">{data?.user?.tgUserId}</span><FaCopy className=\"cursor-pointer\" /></span>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-x-2\">\r\n                            {profileDetailsBlurred ? <FaEyeSlash onClick={(e) => {\r\n                                e.stopPropagation();\r\n                                setProfileDetailsBlurred(!profileDetailsBlurred)\r\n                            }} className=\"text-white text-md cursor-pointer\" /> : <FaEye onClick={(e) => {\r\n                                e.stopPropagation();\r\n                                setProfileDetailsBlurred(!profileDetailsBlurred)\r\n                            }} className=\"text-white text-sm cursor-pointer\" />}\r\n                            <motion.div\r\n                                animate={{ rotate: openProfileDropdown ? 180 : 0 }}\r\n                                transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\r\n                            >\r\n                                <FaCaretDown className=\"text-white text-sm\" />\r\n                            </motion.div>\r\n                        </div>\r\n                    </div>\r\n                    <AnimatePresence mode=\"wait\">\r\n                        {openProfileDropdown && (\r\n                            <motion.div className=\"flex flex-col gap-y-3 mt-3\"\r\n                                initial={{ opacity: 0, height: 0 }}\r\n                                animate={{ opacity: 1, height: \"auto\" }}\r\n                                exit={{ opacity: 0, height: 0 }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                <motion.button\r\n                                    className=\"font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-sm\"\r\n                                    whileHover={{\r\n                                        boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                                        scale: 1.05\r\n                                    }}\r\n                                    whileTap={{\r\n                                        backgroundColor: \"#f0f0f0\",\r\n                                        scale: 1.03\r\n                                    }}\r\n                                    onClick={(e) => {\r\n                                        e.stopPropagation()\r\n                                    }}\r\n                                >\r\n                                    Select Your Wallet\r\n                                </motion.button>\r\n                                <hr />\r\n                                <div className=\"flex justify-between text-sm\">\r\n                                    <span>Fee Discount</span> <span>0.00%</span>\r\n                                </div>\r\n                                <hr />\r\n                                <div className=\"flex justify-between text-sm\">\r\n                                    <span>Trading Fee</span> <span>1.00%</span>\r\n                                </div>\r\n                            </motion.div>\r\n                        )}</AnimatePresence>\r\n                </motion.div>\r\n                {/* Profile Dropdown */}\r\n\r\n                {/* Sign in Mobile */}\r\n                {/* <motion.div className=\"flex flex-col gap-x-2 justify-between bg-black border border-white/10 p-3 rounded-sm cursor-pointer\" onClick={() => setOpenWalletDropdown(!openWalletDropdown)}\r\n                >\r\n                    <div className=\"flex gap-x-2 justify-between w-full\">\r\n                        <div className=\"flex gap-x-2 items-center\">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <FaQrcode className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Sign In On Mobile</span>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-x-2\">\r\n                            <motion.div\r\n                                animate={{ rotate: openWalletDropdown ? 180 : 0 }}\r\n                                transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\r\n                            >\r\n                                <FaCaretDown className=\"text-white text-sm\" />\r\n                            </motion.div>\r\n                        </div>\r\n                    </div>\r\n                    <AnimatePresence mode=\"wait\">\r\n                        {openWalletDropdown && (\r\n                            <motion.div className=\"flex flex-col gap-y-3 mt-3\"\r\n                                initial={{ opacity: 0, height: 0 }}\r\n                                animate={{ opacity: 1, height: \"auto\" }}\r\n                                exit={{ opacity: 0, height: 0 }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                <motion.button\r\n                                    className=\"font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-sm\"\r\n                                    whileHover={{\r\n                                        boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                                        scale: 1.05\r\n                                    }}\r\n                                    whileTap={{\r\n                                        backgroundColor: \"#f0f0f0\",\r\n                                        scale: 1.03\r\n                                    }}\r\n                                >\r\n                                    Show QR Code\r\n                                </motion.button>\r\n                            </motion.div>\r\n                        )}\r\n                    </AnimatePresence>\r\n                </motion.div> */}\r\n                {/* Sign in Mobile */}\r\n\r\n                {/* Rewards */}\r\n                <motion.div className=\"flex flex-col gap-x-2 gap-y-4 justify-between bg-black border border-white/10 py-3 rounded-sm cursor-pointer\"\r\n                >\r\n                    <div className=\"flex gap-x-2 justify-between w-full px-3\" onClick={() => setOpenRewardsDropdown(!openRewardsDropdown)}>\r\n                        <div className=\"flex gap-x-2 items-center\">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <FaGift className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Rewards</span>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-x-2\">\r\n                            <motion.div\r\n                                animate={{ rotate: openRewardsDropdown ? 180 : 0 }}\r\n                                transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\r\n                            >\r\n                                <FaCaretDown className=\"text-white text-sm\" />\r\n                            </motion.div>\r\n                        </div>\r\n                    </div>\r\n                    <AnimatePresence mode=\"wait\">\r\n                        {openRewardsDropdown && (\r\n                            <motion.div className=\"flex flex-col gap-y-3 mt-3 w-full px-3\"\r\n                                initial={{ opacity: 0, height: 0 }}\r\n                                animate={{ opacity: 1, height: \"auto\" }}\r\n                                exit={{ opacity: 0, height: 0 }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                <div className=\"text-[10px] bg-white/10 p-2 rounded-sm\">\r\n                                    <span className=\"\">Enhance your trading journey and access premium perks. Your trading activity directly impacts the rewards you receive!</span>\r\n                                </div>\r\n                                <hr />\r\n                                <div className=\"flex justify-between items-center text-sm\">\r\n                                    <span>Your volume</span> <span>0.00%</span>\r\n                                </div>\r\n                                <hr />\r\n                                <div className=\"flex justify-between items-center text-sm\">\r\n                                    <span>Revenue</span>\r\n                                    <motion.button\r\n                                        className=\"font-orbitron font-semibold bg-white text-black px-2 py-1.5 rounded-md hover:shadow-lg hover:shadow-white/20 text-sm\"\r\n                                        whileHover={{\r\n                                            boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                                            scale: 1.05\r\n                                        }}\r\n                                        whileTap={{\r\n                                            backgroundColor: \"#f0f0f0\",\r\n                                            scale: 1.03\r\n                                        }}\r\n                                    >\r\n                                        Claim\r\n                                    </motion.button>\r\n                                </div>\r\n                            </motion.div>)}\r\n                    </AnimatePresence>\r\n                    <hr className=\"border-white/20\" />\r\n                    <div className=\"flex gap-x-2 justify-between w-full px-3\" onClick={() => setOpenReferralsDropdown(!openReferralsDropdown)}>\r\n                        <div className=\"flex gap-x-2 items-center \">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <FaUsers className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Referrals</span>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-x-2 \">\r\n                            <span className=\"w-5 h-5 text-white bg-white/30 flex items-center justify-center text-sm border border-white/20 rounded-sm\">\r\n                                {0}\r\n                            </span>\r\n                            <motion.div\r\n                                animate={{ rotate: openReferralsDropdown ? 180 : 0 }}\r\n                                transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\r\n                            >\r\n                                <FaCaretDown className=\"text-white text-sm\" />\r\n                            </motion.div>\r\n                        </div>\r\n                    </div>\r\n                    <AnimatePresence mode=\"wait\">\r\n                        {openReferralsDropdown && (\r\n                            <motion.div className=\"flex flex-col gap-y-3 mt-3 w-full px-3\"\r\n                                initial={{ opacity: 0, height: 0 }}\r\n                                animate={{ opacity: 1, height: \"auto\" }}\r\n                                exit={{ opacity: 0, height: 0 }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                <div className=\"text-[10px] bg-white/10 p-2 rounded-sm\">\r\n                                    <span>Please select a wallet address to receive your referral rewards</span>\r\n                                </div>\r\n                                <motion.button\r\n                                    className=\"font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full text-sm\"\r\n                                    whileHover={{\r\n                                        boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                                        scale: 1.05\r\n                                    }}\r\n                                    whileTap={{\r\n                                        backgroundColor: \"#f0f0f0\",\r\n                                        scale: 1.03\r\n                                    }}\r\n                                >\r\n                                    Select Wallet for Rewards\r\n                                </motion.button>\r\n                                <hr />\r\n                                <div className=\"flex justify-between items-center text-sm\">\r\n                                    <span>Your receive</span> <span>0.00%</span>\r\n                                </div>\r\n                                <hr />\r\n                                <div className=\"flex justify-between items-center text-sm\">\r\n                                    <span>Revenue</span>\r\n                                    <motion.button\r\n                                        className=\"font-orbitron font-semibold bg-white text-black px-2 py-1.5 rounded-md hover:shadow-lg hover:shadow-white/20 text-sm\"\r\n                                        whileHover={{\r\n                                            boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                                            scale: 1.05\r\n                                        }}\r\n                                        whileTap={{\r\n                                            backgroundColor: \"#f0f0f0\",\r\n                                            scale: 1.03\r\n                                        }}\r\n                                    >\r\n                                        Claim\r\n                                    </motion.button>\r\n                                </div>\r\n                            </motion.div>)}\r\n                    </AnimatePresence>\r\n                </motion.div>\r\n                {/* Rewards */}\r\n\r\n                {/* Notifications */}\r\n                <motion.div className=\"flex flex-col gap-x-2 justify-between bg-black border border-white/10 p-3 rounded-sm cursor-pointer\"\r\n                >\r\n                    <div className=\"flex gap-x-2 justify-between w-full\">\r\n                        <div className=\"flex gap-x-2 items-center\">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <IoMdNotifications className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Notifications</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </motion.div>\r\n                {/* Notifications */}\r\n\r\n                {/* Support Section */}\r\n                <motion.div className=\"flex flex-col gap-x-2 gap-y-4 justify-between bg-black border border-white/10 py-3 rounded-sm cursor-pointer\"\r\n                >\r\n                    <div className=\"flex gap-x-2 justify-between w-full px-3\">\r\n                        <div className=\"flex gap-x-2 items-center\">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <FaQuestionCircle className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Support</span>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-x-2\">\r\n                            <motion.div\r\n                                animate={{ rotate: openRewardsDropdown ? 180 : 0 }}\r\n                                transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\r\n                            >\r\n                                <FaCaretDown className=\"text-white text-sm\" />\r\n                            </motion.div>\r\n                        </div>\r\n                    </div>\r\n                    <AnimatePresence mode=\"wait\">\r\n                        {openRewardsDropdown && (\r\n                            <motion.div className=\"flex flex-col gap-y-3 mt-3 w-full px-3\"\r\n                                initial={{ opacity: 0, height: 0 }}\r\n                                animate={{ opacity: 1, height: \"auto\" }}\r\n                                exit={{ opacity: 0, height: 0 }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                <div className=\"text-[10px] bg-white/10 p-2 rounded-sm\">\r\n                                    <span className=\"\">Enhance your trading journey and access premium perks. Your trading activity directly impacts the rewards you receive!</span>\r\n                                </div>\r\n                                <hr />\r\n                                <div className=\"flex justify-between items-center text-sm\">\r\n                                    <span>Your volume</span> <span>0.00%</span>\r\n                                </div>\r\n                                <hr />\r\n                                <div className=\"flex justify-between items-center text-sm\">\r\n                                    <span>Revenue</span>\r\n                                    <motion.button\r\n                                        className=\"font-orbitron font-semibold bg-white text-black px-2 py-1.5 rounded-md hover:shadow-lg hover:shadow-white/20 text-sm\"\r\n                                        whileHover={{\r\n                                            boxShadow: \"0 0 15px rgba(255, 255, 255, 0.5)\",\r\n                                            scale: 1.05\r\n                                        }}\r\n                                        whileTap={{\r\n                                            backgroundColor: \"#f0f0f0\",\r\n                                            scale: 1.03\r\n                                        }}\r\n                                    >\r\n                                        Claim\r\n                                    </motion.button>\r\n                                </div>\r\n                            </motion.div>)}\r\n                    </AnimatePresence>\r\n                    <hr className=\"border-white/20\" />\r\n                    <div className=\"flex gap-x-2 justify-between w-full px-3\">\r\n                        <div className=\"flex gap-x-2 items-center \">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <IoDocumentTextOutline className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Privacy Policy</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <hr className=\"border-white/20\" />\r\n                    <div className=\"flex gap-x-2 justify-between w-full px-3\" onClick={logout}>\r\n                        <div className=\"flex gap-x-2 items-center \">\r\n                            <div className=\"w-8 h-8 flex items-center justify-center bg-white/10 rounded-full\">\r\n                                <IoIosLogOut className=\"text-white text-sm\" />\r\n                            </div>\r\n                            <div className=\"text-sm flex flex-col gap-y-1\">\r\n                                <span className=\"font-bold\">Logout</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </motion.div>\r\n                {/* Support Section */}\r\n            </motion.nav>\r\n        </motion.section>\r\n    )\r\n}\r\n\r\nfunction ChainSelectorModal({\r\n    chainSelector,\r\n    setChainSelector,\r\n    handleSettingsUpdate,\r\n    chainLoading // Receive loading state\r\n}: {\r\n    chainSelector: boolean,\r\n    setChainSelector: React.Dispatch<React.SetStateAction<boolean>>,\r\n    handleSettingsUpdate: (chain: Chain) => void,\r\n    chainLoading?: string | null\r\n}) {\r\n    const { chainList, selectedChain } = useWeb3();\r\n    return (\r\n        <motion.section className=\"min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n        >\r\n            <div className=\"absolute inset-0\" onClick={() => setChainSelector(false)} />\r\n\r\n            <motion.nav className=\"bg-black/95 backdrop-blur-sm w-full max-w-sm h-full p-4 relative z-10\"\r\n                initial={{ x: \"100%\" }}\r\n                animate={{ x: \"0%\" }}\r\n                exit={{ x: \"100%\" }}\r\n                transition={{ duration: 0.3 }}\r\n            >\r\n                <div className=\"flex items-center justify-between mb-6\">\r\n                    <h3 className=\"font-orbitron font-bold text-lg\">Select Chain</h3>\r\n                    <button\r\n                        onClick={() => setChainSelector(false)}\r\n                        className=\"p-2 hover:bg-white/10 rounded-md transition-colors\"\r\n                    >\r\n                        <IoMdClose className=\"text-white text-sm\" />\r\n                    </button>\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                    {chainList.map((chain) => (\r\n                        <motion.button\r\n                            key={chain.chain}\r\n                            onClick={() => handleSettingsUpdate(chain)}\r\n                            className={`flex items-center gap-3 w-full p-3 rounded-md hover:bg-white/10 transition-colors ${selectedChain?.chain === chain.chain ? \"bg-white/10\" : \"\"}`}\r\n                            whileTap={{ scale: 0.98 }}\r\n                            disabled={!!chainLoading} // Disable all buttons while loading\r\n                        >\r\n                            <img\r\n                                src={chain.logo}\r\n                                alt={chain.chain}\r\n                                className=\"w-5 h-5 object-contain\"\r\n                            />\r\n                            <span className=\"font-space-grotesk text-sm\">{chain.chain}</span>\r\n                            {/* Spinner for the chain being loaded */}\r\n                            {chainLoading === chain.chain ? (\r\n                                <ImSpinner2 className=\"animate-spin text-white ml-auto\" />\r\n                            ) : selectedChain?.chain === chain.chain ? (\r\n                                <TiTick className=\"text-green-500 ml-auto\" />\r\n                            ) : null}\r\n                        </motion.button>\r\n                    ))}\r\n                </div>\r\n            </motion.nav>\r\n        </motion.section>\r\n    )\r\n}\r\n\r\nfunction NotificationsModal({ }) {\r\n    return (\r\n        <motion.section className=\"min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n        >\r\n            <div className=\"absolute inset-0\" />\r\n        </motion.section>\r\n    )\r\n}\r\n\r\n// Implement Token Search Modal\r\nfunction TokenSearchModal({ tokenSearch, setTokenSearch }: { tokenSearch: boolean, setTokenSearch: React.Dispatch<React.SetStateAction<boolean>> }){\r\n    return (\r\n        <motion.section className=\"min-w-full bg-black/60 inset-0 absolute h-full z-50 flex justify-end\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n        >\r\n            <div className=\"absolute inset-0\" />\r\n            <motion.nav className=\"bg-black/95 backdrop-blur-sm w-full max-w-sm h-full p-4 relative z-10\"\r\n                initial={{ x: \"100%\" }}\r\n                animate={{ x: \"0%\" }}\r\n                exit={{ x: \"100%\" }}\r\n                transition={{ duration: 0.3 }}\r\n            >\r\n                <div className=\"flex items-center justify-between mb-6\">\r\n                    <h3 className=\"font-orbitron font-bold text-lg\">Search for a Token</h3>\r\n                    <button\r\n                        onClick={() => setTokenSearch(false)}\r\n                        className=\"p-2 hover:bg-white/10 rounded-md transition-colors\"\r\n                    >\r\n                        <IoMdClose className=\"text-white text-sm\" />\r\n                    </button>\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                    <input\r\n                        type=\"text\"\r\n                        placeholder=\"Enter Contract Address (0x...)\"\r\n                        className=\"w-full p-3 rounded-md bg-white/10 text-white border border-white/20 focus:outline-none focus:ring-2 focus:ring-white/50\"\r\n                    />\r\n                </div>\r\n            </motion.nav>\r\n\r\n\r\n        </motion.section>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA,8PAA6C,kCAAkC;AAC/E;;;AApBA;;;;;;;;;;;;;;;;;;;;;AAwBe,SAAS;;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5D,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE;IACpC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,oBAAoB;IAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACf,cAAc,CAAC;QACf,cAAc,CAAC;IACnB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,OAAO;gBACP,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAChB;QACJ;2BAAG;QAAC;KAAM;IAEV,MAAM,UAAU,QAAQ,KAAK,OAAO,GAAG,OAAO,MAAM,WAAW;IAE/D,MAAM,yBAAyB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,KAAK,eAAe;IAE7F,sCAAsC;IACtC,eAAe,qBAAqB,aAAoB;QACpD,gBAAgB,cAAc,KAAK,GAAG,6BAA6B;QACnE,MAAM,UAAU,cAAc,OAAO;QACrC,IAAI;YACA,MAAM,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD,EAAE,OAAiB;gBAAE;YAAQ;YAChD,iBAAiB;YACjB,iBAAiB;YACjB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAY;YACjB,IAAI,SAAS,MAAM,QAAQ,EAAE;gBACzB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC3C;QACJ,SAAU;YACN,gBAAgB,OAAO,uBAAuB;QAClD;IACJ;IAEA,6CAA6C;IAC7C,IAAI,CAAC,eAAe;QAChB,qBACI,6LAAC;YAAQ,WAAU;sBACf,cAAA,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;IAGlC;IAEA,qBACI,6LAAC;QAAQ,WAAU;;0BACf,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAK,WAAU;kCAAuF;;;;;;kCACvG,6LAAC;wBAAI,WAAU;kCACV,uBAAuB,GAAG,CAAC,CAAC,OAAO;4BAChC,MAAM,YAAY,MAAM,IAAI,EAAE,SAAS,CAAC,EAAE;4BAC1C,IAAI,CAAC,WAAW,OAAO;4BACvB,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG;4BAChC,qBACI,6LAAC;gCAAgB,WAAU;;oCACtB,SAAS,8BACN,6LAAC;wCAAI,KAAK,QAAQ,aAAa;wCAAE,WAAU;wCAAsC,KAAK,QAAQ,MAAM;;;;;6DAEpG,6LAAC;wCAAI,WAAU;kDAAgF,SAAS,QAAQ,OAAO,MAAM;;;;;;kDAEjI,6LAAC;wCAAK,WAAU;kDAA0C,SAAS,UAAU;;;;;;kDAC7E,6LAAC;wCAAK,WAAU;kDAA4D,YAAY,WAAW,CAAC,CAAC,EAAE,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,WAAW;4CAAE,uBAAuB;wCAAE,IAAI,GAAG;;;;;;;+BAP7L;;;;;wBAUlB;;;;;;;;;;;;0BAGR,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,WAAU;;kDACtC,6LAAC;wCAAI,KAAI;wCAAiB,WAAU;wCAAyB,KAAI;;;;;;kDACjE,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;0CACV,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,KAAK,GAAG;wCAAmB,WAAW,CAAC,wDAAwD,EAAE,KAAK,GAAG,CAAC,QAAQ,CAAC,YAAY,eAAe,gBAAgB,CAAC,CAAC;kDACvK,KAAK,KAAK;uCADY,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMjD,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAAC,WAAU;gCACrB,SAAS;oCACL,iBAAiB;gCACrB;gCACA,UAAU;oCACN,iBAAiB;oCACjB,OAAO;gCACX;;kDAEA,6LAAC,kJAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAuB;;;;;;;;;;;;0CAE3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAAC,WAAU;gCACrB,YAAY;oCACR,iBAAiB;gCACrB;gCACA,UAAU;oCACN,iBAAiB;oCACjB,OAAO;gCACX;;kDAEA,6LAAC,iJAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAE9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAAC,WAAU;gCACrB,YAAY;oCACR,iBAAiB;gCACrB;gCACA,UAAU;oCACN,iBAAiB;oCACjB,OAAO;gCACX;gCACA,SAAS,IAAM,eAAe;;kDAE9B,6LAAC,kJAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;kDAC7B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAsL;;;;;;;;;;;;0CAI1M,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAAC,WAAU;gCACrB,YAAY;oCACR,iBAAiB;gCACrB;gCACA,UAAU;oCACN,iBAAiB;oCACjB,OAAO;gCACX;gCACA,SAAS,IAAM,iBAAiB;;kDAEhC,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAM,QAAQ,OAAO,CAAC;;;;;;oCACtB,8BACG,6LAAC;wCAAI,KAAK,cAAc,IAAI;wCAAE,WAAU;wCAAyB,KAAK,cAAc,KAAK;;;;;6DAEzF,6LAAC,iJAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAG9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAAC,WAAU;gCACrB,YAAY;oCACR,iBAAiB;gCACrB;gCACA,UAAU;oCACN,iBAAiB;oCACjB,OAAO;gCACX;gCACA,SAAS,IAAM,iBAAiB;0CAChC,cAAA,6LAAC,iJAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAAC,WAAU;gCACrB,YAAY;oCACR,iBAAiB;gCACrB;gCACA,UAAU;oCACN,iBAAiB;oCACjB,OAAO;gCACX;gCACA,SAAS,IAAM,cAAc,CAAC;0CAE9B,cAAA,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAIzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACV,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,UAAU;oCAAE,iBAAiB;gCAA2B;0CAEvD,2BAAa,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAAmB,6LAAC,iJAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAI7F,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;;oBACjB,4BAAc,6LAAC;wBAAW,YAAY;wBAAY,eAAe;;;;;;oBACjE,+BACG,6LAAC;wBACG,eAAe;wBACf,kBAAkB;wBAClB,sBAAsB;wBACtB,cAAc;;;;;;oBAGrB,6BACG,6LAAC;wBACG,aAAa;wBACb,gBAAgB;;;;;;oBAGvB,+BACG,6LAAC,qKAAA,CAAA,UAAa;wBACV,mBAAmB;wBACnB,sBAAsB;;;;;;;;;;;;;;;;;;AAM9C;GAnNwB;;QAKwC,kIAAA,CAAA,UAAO;QACjD,kIAAA,CAAA,UAAO;QACD,8HAAA,CAAA,cAAW;QAIlB,qIAAA,CAAA,cAAW;;;KAXR;AAqNxB,SAAS,WAAW,EAAE,UAAU,EAAE,aAAa,EAAyF;;IACpI,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE;IAG7C,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QAAC,WAAU;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;gBAAmB,SAAS,IAAM,cAAc;;;;;;0BAE/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,WAAU;gBAClB,SAAS;oBAAE,GAAG;gBAAO;gBACrB,SAAS;oBAAE,GAAG;gBAAK;gBACnB,MAAM;oBAAE,GAAG;gBAAO;gBAClB,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACrB,6LAAC;oCAAI,KAAI;oCAAiB,WAAU;oCAAyB,KAAI;;;;;;8CACjE,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAI1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;wBAAsG,SAAS,IAAM,uBAAuB,CAAC;;0CAE/J,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC,iJAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAK,WAAW,CAAC,UAAU,EAAE,wBAAwB,YAAY,IAAI;;4DAAE;4DAAE,MAAM,MAAM;;;;;;;kEACtF,6LAAC;wDAAK,WAAW,CAAC,0BAA0B,EAAE,wBAAwB,YAAY,IAAI;;4DAAE;0EAAI,6LAAC;gEAAK,WAAU;0EAAiB,MAAM,MAAM;;;;;;0EAAgB,6LAAC,iJAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAGnL,6LAAC;wCAAI,WAAU;;4CACV,sCAAwB,6LAAC,iJAAA,CAAA,aAAU;gDAAC,SAAS,CAAC;oDAC3C,EAAE,eAAe;oDACjB,yBAAyB,CAAC;gDAC9B;gDAAG,WAAU;;;;;qEAAyC,6LAAC,iJAAA,CAAA,QAAK;gDAAC,SAAS,CAAC;oDACnE,EAAE,eAAe;oDACjB,yBAAyB,CAAC;gDAC9B;gDAAG,WAAU;;;;;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACP,SAAS;oDAAE,QAAQ,sBAAsB,MAAM;gDAAE;gDACjD,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;0DAE1D,cAAA,6LAAC,iJAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAInC,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACjB,qCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;oCAClB,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACV,WAAU;4CACV,YAAY;gDACR,WAAW;gDACX,OAAO;4CACX;4CACA,UAAU;gDACN,iBAAiB;gDACjB,OAAO;4CACX;4CACA,SAAS,CAAC;gDACN,EAAE,eAAe;4CACrB;sDACH;;;;;;sDAGD,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;gDAAmB;8DAAC,6LAAC;8DAAK;;;;;;;;;;;;sDAEpC,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;gDAAkB;8DAAC,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAwDnD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;;0CAElB,6LAAC;gCAAI,WAAU;gCAA2C,SAAS,IAAM,uBAAuB,CAAC;;kDAC7F,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC,iJAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACP,SAAS;gDAAE,QAAQ,sBAAsB,MAAM;4CAAE;4CACjD,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;sDAE1D,cAAA,6LAAC,iJAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAInC,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACjB,qCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;oCAClB,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAK,WAAU;0DAAG;;;;;;;;;;;sDAEvB,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;gDAAkB;8DAAC,6LAAC;8DAAK;;;;;;;;;;;;sDAEnC,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;8DACN,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACV,WAAU;oDACV,YAAY;wDACR,WAAW;wDACX,OAAO;oDACX;oDACA,UAAU;wDACN,iBAAiB;wDACjB,OAAO;oDACX;8DACH;;;;;;;;;;;;;;;;;;;;;;;0CAMjB,6LAAC;gCAAG,WAAU;;;;;;0CACd,6LAAC;gCAAI,WAAU;gCAA2C,SAAS,IAAM,yBAAyB,CAAC;;kDAC/F,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC,iJAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAK,WAAU;0DACX;;;;;;0DAEL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACP,SAAS;oDAAE,QAAQ,wBAAwB,MAAM;gDAAE;gDACnD,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;0DAE1D,cAAA,6LAAC,iJAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAInC,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACjB,uCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;oCAClB,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;0DAAK;;;;;;;;;;;sDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACV,WAAU;4CACV,YAAY;gDACR,WAAW;gDACX,OAAO;4CACX;4CACA,UAAU;gDACN,iBAAiB;gDACjB,OAAO;4CACX;sDACH;;;;;;sDAGD,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;gDAAmB;8DAAC,6LAAC;8DAAK;;;;;;;;;;;;sDAEpC,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;8DACN,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACV,WAAU;oDACV,YAAY;wDACR,WAAW;wDACX,OAAO;oDACX;oDACA,UAAU;wDACN,iBAAiB;wDACjB,OAAO;oDACX;8DACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;kCAElB,cAAA,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;;0CAElB,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACP,SAAS;gDAAE,QAAQ,sBAAsB,MAAM;4CAAE;4CACjD,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;sDAE1D,cAAA,6LAAC,iJAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAInC,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACjB,qCACG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;oCAClB,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAK,WAAU;0DAAG;;;;;;;;;;;sDAEvB,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;gDAAkB;8DAAC,6LAAC;8DAAK;;;;;;;;;;;;sDAEnC,6LAAC;;;;;sDACD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;8DAAK;;;;;;8DACN,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACV,WAAU;oDACV,YAAY;wDACR,WAAW;wDACX,OAAO;oDACX;oDACA,UAAU;wDACN,iBAAiB;wDACjB,OAAO;oDACX;8DACH;;;;;;;;;;;;;;;;;;;;;;;0CAMjB,6LAAC;gCAAG,WAAU;;;;;;0CACd,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,kJAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;;;;;;sDAErC,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;0CAIxC,6LAAC;gCAAG,WAAU;;;;;;0CACd,6LAAC;gCAAI,WAAU;gCAA2C,SAAS;0CAC/D,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5D;IApXS;;QAMkD,kIAAA,CAAA,UAAO;QAC3C,kIAAA,CAAA,UAAO;QACR,kIAAA,CAAA,UAAO;QACQ,8HAAA,CAAA,cAAW;;;MATvC;AAsXT,SAAS,mBAAmB,EACxB,aAAa,EACb,gBAAgB,EAChB,oBAAoB,EACpB,YAAY,AAAC,wBAAwB;EAMxC;;IACG,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QAAC,WAAU;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;gBAAmB,SAAS,IAAM,iBAAiB;;;;;;0BAElE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,WAAU;gBAClB,SAAS;oBAAE,GAAG;gBAAO;gBACrB,SAAS;oBAAE,GAAG;gBAAK;gBACnB,MAAM;oBAAE,GAAG;gBAAO;gBAClB,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,6LAAC;gCACG,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI7B,6LAAC;wBAAI,WAAU;kCACV,UAAU,GAAG,CAAC,CAAC,sBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEV,SAAS,IAAM,qBAAqB;gCACpC,WAAW,CAAC,kFAAkF,EAAE,eAAe,UAAU,MAAM,KAAK,GAAG,gBAAgB,IAAI;gCAC3J,UAAU;oCAAE,OAAO;gCAAK;gCACxB,UAAU,CAAC,CAAC;;kDAEZ,6LAAC;wCACG,KAAK,MAAM,IAAI;wCACf,KAAK,MAAM,KAAK;wCAChB,WAAU;;;;;;kDAEd,6LAAC;wCAAK,WAAU;kDAA8B,MAAM,KAAK;;;;;;oCAExD,iBAAiB,MAAM,KAAK,iBACzB,6LAAC,iJAAA,CAAA,aAAU;wCAAC,WAAU;;;;;+CACtB,eAAe,UAAU,MAAM,KAAK,iBACpC,6LAAC,iJAAA,CAAA,SAAM;wCAAC,WAAU;;;;;+CAClB;;+BAjBC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;AAwB5C;IAhES;;QAWgC,kIAAA,CAAA,UAAO;;;MAXvC;AAkET,SAAS,mBAAmB,EAAG;IAC3B,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QAAC,WAAU;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAG3B;MAXS;AAaT,+BAA+B;AAC/B,SAAS,iBAAiB,EAAE,WAAW,EAAE,cAAc,EAA2F;IAC9I,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QAAC,WAAU;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,WAAU;gBAClB,SAAS;oBAAE,GAAG;gBAAO;gBACrB,SAAS;oBAAE,GAAG;gBAAK;gBACnB,MAAM;oBAAE,GAAG;gBAAO;gBAClB,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,6LAAC;gCACG,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI7B,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BACG,MAAK;4BACL,aAAY;4BACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAQlC;MArCS", "debugId": null}}, {"offset": {"line": 2719, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/Footer/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { SiGitbook } from \"react-icons/si\";\r\nimport { FaXTwitter } from \"react-icons/fa6\";\r\nimport { FaD<PERSON>rd, FaTelegramPlane, FaWallet } from \"react-icons/fa\";\r\nimport { TbAntennaBars5, TbAntennaBarsOff } from \"react-icons/tb\";\r\nimport { FaFire } from \"react-icons/fa\";\r\nimport { HiSparkles } from \"react-icons/hi\";\r\nimport { FaBriefcase } from \"react-icons/fa\";\r\nimport { FaCrosshairs } from \"react-icons/fa\";\r\nimport Link from 'next/link';\r\nimport { usePathname } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nconst telegramBotUsername = process.env.NODE_ENV === \"production\" ? process.env.NEXT_PUBLIC_BOT_USERNAME_PROD : process.env.NEXT_PUBLIC_BOT_USERNAME_DEV;\r\n\r\n\r\nexport const terminalNavLinks = [\r\n    {\r\n        title: \"Trending\",\r\n        url: \"/terminal/trending\",\r\n        icon: <FaFire className=\"text-md\" />\r\n    },\r\n    {\r\n        title: \"New\",\r\n        url: \"/terminal/new\",\r\n        icon: <HiSparkles className=\"text-md\" />\r\n    },\r\n    {\r\n        title: \"portfolio\",\r\n        url: \"/terminal/portfolio\",\r\n        icon: <FaBriefcase className=\"text-md\" />\r\n    },\r\n    {\r\n        title: \"Wallet\",\r\n        url: \"/terminal/wallet\",\r\n        icon: <FaWallet className=\"text-md\" />\r\n    },\r\n    {\r\n        title: \"Sniping\",\r\n        url: \"/terminal/sniping\",\r\n        icon: <FaCrosshairs className=\"text-md\" />\r\n    }\r\n]\r\n\r\nconst socials = [\r\n    {\r\n        icon: <FaTelegramPlane className=\"text-white text-sm\" />,\r\n        href: `https://t.me/${telegramBotUsername}`,\r\n        label: \"Telegram\"\r\n    },\r\n    {\r\n        icon: <FaXTwitter className=\"text-white text-sm\" />,\r\n        href: \"https://twitter.com/AnubisTerminal\",\r\n        label: \"Twitter\"\r\n    },\r\n    {\r\n        icon: <FaDiscord className=\"text-white text-sm\" />,\r\n        href: \"https://discord.gg/anubisterminal\",\r\n        label: \"Discord\"\r\n    },\r\n    {\r\n        icon: <SiGitbook className=\"text-white text-sm\" />,\r\n        href: \"https://docs.anubis.finance\",\r\n        label: \"Documentation\"\r\n    }\r\n]\r\n\r\ninterface NetworkStatusType {\r\n    status: \"Stable\" | \"Offline\" | \"Irregular\";\r\n}\r\n\r\nfunction NetworkStatus({ status }: NetworkStatusType) {\r\n    const statusMap = {\r\n        Stable: <><TbAntennaBars5 className=\"text-green-500 text-xs\" /> <span className=\"text-green-500 text-xs\">Stable</span></>,\r\n        Offline: <><TbAntennaBarsOff className=\"text-red-500 text-xs\" /> <span className=\"text-red-500 text-xs\">Offline</span></>,\r\n        Irregular: <><TbAntennaBars5 className=\"text-yellow-500 text-xs\" /> <span className=\"text-yellow-500 text-xs\">Irregular</span></>\r\n    };\r\n\r\n    return statusMap[status];\r\n}\r\n\r\n\r\nexport default function Footer() {\r\n    const pathname = usePathname();\r\n\r\n    // Find the active tab index\r\n    const activeTabIndex = terminalNavLinks.findIndex(link => link.url.includes(pathname));\r\n\r\n    return (\r\n        <footer className=\"fixed bottom-0 left-0 w-full bg-black/50 md:bg-black pb-3 flex flex-col items-center justify-center\">\r\n            {/* Bottom Nav */}\r\n            <div className={`md:hidden grid grid-cols-5 w-full px-1 relative`}>\r\n                {/* Animated indicator for active tab */}\r\n                {activeTabIndex !== -1 && (\r\n                    <motion.div\r\n                        className=\"absolute top-0 h-[3px] bg-white\"\r\n                        initial={false}\r\n                        animate={{\r\n                            left: `${(activeTabIndex * 25)}%`,\r\n                            width: '25%'\r\n                        }}\r\n                        transition={{\r\n                            type: \"spring\",\r\n                            stiffness: 300,\r\n                            damping: 30\r\n                        }}\r\n                        layoutId=\"activeTabIndicator\"\r\n                    />\r\n                )}\r\n\r\n                {terminalNavLinks.map((link, index) => {\r\n                    const isActive = link.url.includes(pathname);\r\n                    return (\r\n                        <Link\r\n                            href={link.url}\r\n                            key={index}\r\n                            className={`flex flex-col pt-3 items-center justify-center gap-y-2`}\r\n                        >\r\n                            <motion.span\r\n                                className={`${isActive ? \"text-white\" : \"text-white/70\"}`}\r\n                                animate={{\r\n                                    color: isActive ? \"rgba(255, 255, 255, 1)\" : \"rgba(255, 255, 255, 0.7)\"\r\n                                }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                {link.icon}\r\n                            </motion.span>\r\n                            <motion.span\r\n                                className={`capitalize font-bold font-orbitron text-md`}\r\n                                animate={{\r\n                                    color: isActive ? \"rgba(255, 255, 255, 1)\" : \"rgba(255, 255, 255, 0.7)\"\r\n                                }}\r\n                                transition={{ duration: 0.3 }}\r\n                            >\r\n                                {link.title}\r\n                            </motion.span>\r\n                        </Link>\r\n                    )\r\n                })}\r\n            </div>\r\n            <div className=\"border-t border-white/10 hidden md:flex justify-between py-1 px-2 w-full\">\r\n                <div className=\"flex items-center gap-x-3\">\r\n                    <NetworkStatus status=\"Stable\" />\r\n                </div>\r\n                <div className=\"flex items-center gap-x-3\">\r\n                    {socials.map((social, index) => (\r\n                        <a key={index} href={social.href} target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center justify-center text-white hover:bg-white/10 transition-colors\">\r\n                            {social.icon}\r\n                        </a>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n        </footer >\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAc4B;;AAZ5B;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAcA,MAAM,sBAAsB;AAGrB,MAAM,mBAAmB;IAC5B;QACI,OAAO;QACP,KAAK;QACL,oBAAM,6LAAC,iJAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC5B;IACA;QACI,OAAO;QACP,KAAK;QACL,oBAAM,6LAAC,iJAAA,CAAA,aAAU;YAAC,WAAU;;;;;;IAChC;IACA;QACI,OAAO;QACP,KAAK;QACL,oBAAM,6LAAC,iJAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IACjC;IACA;QACI,OAAO;QACP,KAAK;QACL,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC9B;IACA;QACI,OAAO;QACP,KAAK;QACL,oBAAM,6LAAC,iJAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IAClC;CACH;AAED,MAAM,UAAU;IACZ;QACI,oBAAM,6LAAC,iJAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;QACjC,MAAM,CAAC,aAAa,EAAE,qBAAqB;QAC3C,OAAO;IACX;IACA;QACI,oBAAM,6LAAC,kJAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,MAAM;QACN,OAAO;IACX;IACA;QACI,oBAAM,6LAAC,iJAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,MAAM;QACN,OAAO;IACX;IACA;QACI,oBAAM,6LAAC,iJAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,MAAM;QACN,OAAO;IACX;CACH;AAMD,SAAS,cAAc,EAAE,MAAM,EAAqB;IAChD,MAAM,YAAY;QACd,sBAAQ;;8BAAE,6LAAC,iJAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;gBAA2B;8BAAC,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;;;QACzG,uBAAS;;8BAAE,6LAAC,iJAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAAyB;8BAAC,6LAAC;oBAAK,WAAU;8BAAuB;;;;;;;;QACxG,yBAAW;;8BAAE,6LAAC,iJAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;gBAA4B;8BAAC,6LAAC;oBAAK,WAAU;8BAA0B;;;;;;;;IAClH;IAEA,OAAO,SAAS,CAAC,OAAO;AAC5B;KARS;AAWM,SAAS;;IACpB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,4BAA4B;IAC5B,MAAM,iBAAiB,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC;IAE5E,qBACI,6LAAC;QAAO,WAAU;;0BAEd,6LAAC;gBAAI,WAAW,CAAC,+CAA+C,CAAC;;oBAE5D,mBAAmB,CAAC,mBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACP,WAAU;wBACV,SAAS;wBACT,SAAS;4BACL,MAAM,GAAI,iBAAiB,GAAI,CAAC,CAAC;4BACjC,OAAO;wBACX;wBACA,YAAY;4BACR,MAAM;4BACN,WAAW;4BACX,SAAS;wBACb;wBACA,UAAS;;;;;;oBAIhB,iBAAiB,GAAG,CAAC,CAAC,MAAM;wBACzB,MAAM,WAAW,KAAK,GAAG,CAAC,QAAQ,CAAC;wBACnC,qBACI,6LAAC,+JAAA,CAAA,UAAI;4BACD,MAAM,KAAK,GAAG;4BAEd,WAAW,CAAC,sDAAsD,CAAC;;8CAEnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACR,WAAW,GAAG,WAAW,eAAe,iBAAiB;oCACzD,SAAS;wCACL,OAAO,WAAW,2BAA2B;oCACjD;oCACA,YAAY;wCAAE,UAAU;oCAAI;8CAE3B,KAAK,IAAI;;;;;;8CAEd,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACR,WAAW,CAAC,0CAA0C,CAAC;oCACvD,SAAS;wCACL,OAAO,WAAW,2BAA2B;oCACjD;oCACA,YAAY;wCAAE,UAAU;oCAAI;8CAE3B,KAAK,KAAK;;;;;;;2BAnBV;;;;;oBAuBjB;;;;;;;0BAEJ,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAc,QAAO;;;;;;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;kCACV,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBAClB,6LAAC;gCAAc,MAAM,OAAO,IAAI;gCAAE,QAAO;gCAAS,KAAI;gCAAsB,WAAU;0CACjF,OAAO,IAAI;+BADR;;;;;;;;;;;;;;;;;;;;;;AAQhC;GAxEwB;;QACH,qIAAA,CAAA,cAAW;;;MADR", "debugId": null}}]}