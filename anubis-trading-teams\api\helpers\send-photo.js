const axios = require("axios");
const retry = require("async-retry");

module.exports = {
  friendlyName: "Send photo",

  description: "Send Photo via bot",

  inputs: {
    chatId: {
      type: "string",
      description: "User Chat ID",
      required: true,
    },
    caption: {
      type: "string",
      description: "Message to send to user",
      required: true,
    },
    photo: {
      type: "string",
      description: "Message to send to user",
      required: true,
    },
    reply_markup: {
      type: "json",
      description: "Inline Keyboard",
    },
  },

  exits: {
    success: {
      description: "All done.",
    },
  },

  fn: async function ({ chatId, caption, photo, reply_markup }) {
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;
    const TELEGRAM_API = `https://api.telegram.org/bot${TOKEN}`;

    try {
      const res = await retry(
        async () => {
          const response = await axios.post(`${TELEGRAM_API}/sendPhoto`, {
            chat_id: chatId,
            photo,
            caption,
            reply_markup,
            parse_mode: "HTML",
          });

          if (response.data.ok) {
            return response;
          } else {
            throw new Error(
              `Error sending message: ${response.data.description}`
            );
          }
        },
        {
          retries: 5,
          minTimeout: 500,
          maxTimeout: 2000,
        }
      );

      sails.log.info(res.data);

      return {
        chat_id: res.data.result.chat.id,
        message_id: res.data.result.message_id,
      };
    } catch (error) {
      sails.log.error("Error sending message:", error);
      throw error;
    }
  },
};
