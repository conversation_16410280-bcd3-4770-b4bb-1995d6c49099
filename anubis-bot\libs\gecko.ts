import { Contract } from "ethers";
import { Address, erc20Abi, parseUnits } from "viem";
import {
  ESupported<PERSON>hains,
  SupportedChainNames,
  SupportedDexes,
  getConfig,
  supportedChains,
} from "../config";
import { getProvider } from "./provider";
import NodeFetchCache from "node-fetch-cache";

// Create a cached fetch instance with TTL for successful responses
export const cachedFetch = NodeFetchCache.create({
  // Only cache responses with a 2xx status code
  shouldCacheResponse: (response) => response.ok,
  ttl: 5 * 60 * 1000, // 5 minutes cache
});

// Cache for token info to avoid repeated API calls
const tokenInfoCache = new Map<string, any>();
const TOKEN_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Cache for pair info
const pairInfoCache = new Map<string, any>();
const PAIR_CACHE_TTL = 2 * 60 * 1000; // 2 minutes

type TokenInfo = {
  id: string;
  type: string;
  attributes: {
    name: string;
    address: Address;
    symbol: string;
    decimals: number;
    total_supply: string;
    coingecko_coin_id: string;
    price_usd: string;
    fdv_usd: string;
    total_reserve_in_usd: string;
    volume_usd: Record<string, number>;
    market_cap_usd: string;
  };
  relationships: Record<string, unknown>;
};

type TokenInfoResponse = {
  data: TokenInfo[];
};

export async function getGeckoTokenInfo(
  addresses: Address[],
  chainId: ESupportedChains
): Promise<TokenInfoResponse> {
  // Create a cache key based on addresses and chain
  const cacheKey = `gecko_${chainId}_${addresses.join(",")}`;

  // Check if we have a valid cached response
  const cachedResponse = tokenInfoCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - TOKEN_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached token info for ${cacheKey}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching token info for ${cacheKey}`);
  const res = await cachedFetch(
    `https://api.geckoterminal.com/api/v2/networks/${getConfig(chainId).chain.name
    }/tokens/multi/${addresses.join(",")}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json;version=20230302",
      },
    }
  );
  const data = await res.json();

  // Cache the response
  tokenInfoCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });

  return data;
}

interface SwapRouterArgs {
  tokenIn: Address;
  amountIn: string;
  tokenOut: Address;
  amountOutMin: string;
  to: Address;
  routeCode: Address;
  txdata: Address;
  value: string;
}

interface SwapToken {
  address: Address;
  symbol: string;
  name: string;
  decimals: number;
}

interface SwapResponse {
  status: string;
  tokens: SwapToken[];
  tokenFrom: number;
  tokenTo: number;
  primaryPrice: number;
  swapPrice: number;
  priceImpact: number;
  amountIn: string;
  assumedAmountOut: string;
  gasSpent: number;
  route: {
    poolAddress: Address;
    poolType: "Concentrated" | "Bridge" | "Classic";
    poolName: string;
    poolFee: number;
    tokenFrom: number;
    tokenTo: number;
    share: number;
    assumedAmountIn: string;
    assumedAmountOut: string;
  }[];
  routeProcessorAddr?: Address;
  routeProcessorArgs?: SwapRouterArgs;
}

async function getTokenDecimals(
  token: Address,
  chainId: ESupportedChains
): Promise<number> {
  if (!!token.match(getConfig(chainId).nativeCurrencyAddress)) {
    return 18;
  }
  const contract = new Contract(token, erc20Abi, getProvider(chainId));
  const decimals = Number((await contract.decimals?.()) ?? 18);
  return decimals;
}

// Cache for swap quotes
const swapQuoteCache = new Map<string, any>();
const SWAP_QUOTE_CACHE_TTL = 30 * 1000; // 30 seconds - shorter TTL for price quotes

export async function getSushiSwapQuote(
  tokenIn: Address,
  tokenOut: Address,
  amount: number,
  maxPriceImpact: number,
  gasPrice: number,
  to: string,
  preferSushi: boolean = true,
  chainId: ESupportedChains
): Promise<SwapResponse> {
  const baseUrl = `https://api.sushi.com/swap/v4/${getConfig(chainId).chain.id
    }`;
  const decimals = await getTokenDecimals(tokenIn, chainId);
  const queryParams = new URLSearchParams({
    tokenIn,
    tokenOut,
    amount: parseUnits(amount.toString(), decimals).toString(),
    maxPriceImpact: maxPriceImpact.toString(),
    gasPrice: gasPrice.toString(),
    preferSushi: preferSushi.toString(),
    to,
  });

  const url = `${baseUrl}?${queryParams}`;

  // Create a cache key
  const cacheKey = url;

  // Check if we have a valid cached response
  const cachedResponse = swapQuoteCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - SWAP_QUOTE_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached swap quote for ${tokenIn} -> ${tokenOut}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching swap quote for ${tokenIn} -> ${tokenOut}`);
  try {
    const res = await cachedFetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    const data = (await res.json()) as SwapResponse;

    // Cache the response
    swapQuoteCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });

    return data;
  } catch (e) {
    console.log("getSwapQuoteFailed", url);
    throw new Error("this token is not supported on Sushiswap v4");
  }
}

interface SwapQuote {
  tokenFrom: SwapToken;
  tokenTo: SwapToken;
  route: {
    address: Address;
    args: SwapRouterArgs;
    gasSpent: number;
    poolType: SwapResponse["route"][0]["poolType"];
  };
}

export async function getSwapQuote(
  tokenIn: Address,
  tokenOut: Address,
  amount: number,
  to: string,
  chainId: ESupportedChains,
  gasPrice: number = 30_000_000_000,
  maxPriceImpact: number = 0.01
): Promise<SwapQuote> {
  const {
    route,
    tokens,
    routeProcessorAddr,
    routeProcessorArgs,
    gasSpent,
  } = await getSushiSwapQuote(
    tokenIn,
    tokenOut,
    amount,
    maxPriceImpact,
    gasPrice,
    to,
    true,
    chainId
  );

  const quote = route?.find(
    (r) => r.poolType === "Concentrated" || r.poolType === "Classic"
  );
  if (!quote) {
    throw new Error("No quote found");
  }
  const tokenFrom = tokens[quote.tokenFrom];
  const tokenTo = tokens[quote.tokenTo];

  if (!tokenFrom || !tokenTo) {
    throw new Error("Token not found");
  }

  if (!routeProcessorAddr || !routeProcessorArgs) {
    throw new Error("Route processor not found");
  }

  return {
    tokenFrom,
    tokenTo,
    route: {
      address: routeProcessorAddr,
      args: routeProcessorArgs,
      gasSpent,
      poolType: quote.poolType,
    },
  };
}

interface TokensResponse {
  schemaVersion: string;
  pairs: Pair[] | null;
}

export interface Pair {
  chainId: SupportedChainNames;
  dexId: SupportedDexes;
  url: string;
  pairAddress: Address;
  baseToken: {
    address: Address;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: Address;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd?: string;
  txns: {
    m5: {
      buys: number;
      sells: number;
    };
    h1: {
      buys: number;
      sells: number;
    };
    h6: {
      buys: number;
      sells: number;
    };
    h24: {
      buys: number;
      sells: number;
    };
  };
  volume: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  liquidity?: {
    usd?: number;
    base: number;
    quote: number;
  };
  fdv?: number;
  pairCreatedAt?: number;
}

// Cache for dexscreener token info
const dexScreenerCache = new Map<string, any>();
const DEX_SCREENER_CACHE_TTL = 2 * 60 * 1000; // 2 minutes

export async function getDexScreenerTokenInfo(
  address: string
): Promise<TokensResponse> {
  // Create a cache key
  const cacheKey = `dexscreener_${address}`;

  // Check if we have a valid cached response
  const cachedResponse = dexScreenerCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - DEX_SCREENER_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached dexscreener info for ${address}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching dexscreener info for ${address}`);
  const response = await cachedFetch(`https://api.dexscreener.io/latest/dex/tokens/${address}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  });

  const data = await response.json();

  // Cache the response
  dexScreenerCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });

  return data;
}

export async function getDexScreenerPairInfo(
  address: Address
): Promise<Pair[]> {
  const tokenInfo = await getDexScreenerTokenInfo(address);
  return (
    tokenInfo.pairs!?.filter((p) =>
      Object.values(supportedChains).includes(p.chainId)
    ) ?? []
  );
}

// Cache for token pair extraction
const tokenPairCache = new Map<string, any>();
const TOKEN_PAIR_CACHE_TTL = 2 * 60 * 1000; // 2 minutes

export async function extractTokenFromPair(
  chainId: ESupportedChains,
  tokenOut: Address,
  tokenIn = getConfig(chainId).nativeCurrencyAddress
): Promise<Pair> {
  // Create a cache key
  const cacheKey = `pair_${chainId}_${tokenOut}_${tokenIn}`;

  // Check if we have a valid cached response
  const cachedResponse = tokenPairCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - TOKEN_PAIR_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached token pair for ${tokenOut}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Extracting token pair for ${tokenOut}`);
  const dexScreenerInfo = await getDexScreenerPairInfo(tokenOut);
  const tokenPairs = dexScreenerInfo.filter(
    (p) => getConfig(chainId).chain.name === p.chainId
  );

  let result;
  if (tokenPairs.length === 0) {
    const geckoInfo = await getGeckoTokenInfo([tokenOut], chainId);
    result = convertGeckoTokenInfoToPair(geckoInfo, chainId)!;
  } else {
    const tokenPair = tokenPairs.find(
      (p) =>
        p.baseToken.address === tokenIn ||
        (p.quoteToken.address === tokenIn && p.baseToken.address === tokenOut) ||
        tokenIn === getConfig(chainId).nativeCurrencyAddress
    );
    if (!tokenPair) {
      return undefined!;
    }
    const { baseToken, quoteToken } = tokenPair;
    result = {
      ...tokenPair,
      quoteToken: baseToken.address === tokenOut ? baseToken : quoteToken,
    };
  }

  // Cache the result
  tokenPairCache.set(cacheKey, {
    data: result,
    timestamp: Date.now()
  });

  return result;
}

function convertGeckoTokenInfoToPair(
  geckoInfo: TokenInfoResponse,
  chainId: ESupportedChains
): Pair | undefined {
  const token = geckoInfo.data?.[0];
  if (!token) return undefined;
  const { address, symbol, name, coingecko_coin_id } = token.attributes;
  const priceNative = token.attributes.price_usd;
  return {
    chainId: getConfig(chainId).chain.name as never,
    dexId: "gecko",
    url: `https://www.coingecko.com/en/coins/${coingecko_coin_id}`,
    pairAddress: "0x",
    baseToken: {
      address,
      name,
      symbol,
    },
    quoteToken: {
      address,
      name,
      symbol,
    },
    priceNative,
    priceUsd: token.attributes.price_usd,
    txns: {
      m5: {
        buys: 0,
        sells: 0,
      },
      h1: {
        buys: 0,
        sells: 0,
      },
      h6: {
        buys: 0,
        sells: 0,
      },
      h24: {
        buys: 0,
        sells: 0,
      },
    },
    volume: {
      m5: 0,
      h1: 0,
      h6: 0,
      h24: 0,
    },
    priceChange: {
      m5: 0,
      h1: 0,
      h6: 0,
      h24: 0,
    },
  };
}
