import { z } from "zod";
import { <PERSON>o } from "hono";
import { storageInstance } from "../../../storage";
import { zValidator } from '@hono/zod-validator';
import isLoggedIn from "../middleware/is-logged-in";
import { ethers } from "ethers";
import { Address } from "viem";
import { getBalance } from "../../../libs/token";
import { getConfig } from "../../../config";
import { extractTokenFromPair } from "../../../libs/gecko";
import { sendMessage } from "../helper/bot";
import { ESupportedChains } from "../../../config";

// Helper function to get portfolio for a specific chain
async function getPortfolioForChain(walletAddress: string, chainId: ESupportedChains, tokens: string[]) {
  const config = getConfig(chainId);
  
  // Get native token balance
  const nativeBalance = await getBalance(
    walletAddress as `0x${string}`,
    config.nativeCurrencyAddress,
    chainId
  );

  // Get token balances and info
  const tokenBalances = await Promise.all(
    tokens.map(async (token) => {
      try {
        const tokenAddress = token as `0x${string}`;
        const pair = await extractTokenFromPair(chainId, tokenAddress);
        const balance = await getBalance(walletAddress as `0x${string}`, tokenAddress, chainId);
        
        return {
          address: tokenAddress,
          name: pair?.quoteToken?.name || null,
          symbol: pair?.quoteToken?.symbol || null,
          balance,
          priceUsd: pair?.priceUsd || null,
          priceNative: pair?.priceNative || null,
          chainId,
          isNative: false
        };
      } catch (error) {
        console.error(`Error fetching token ${token} on chain ${chainId}:`, error);
        return null;
      }
    })
  );

  // Add native token to the portfolio
  const portfolio = [
    {
      address: config.nativeCurrencyAddress,
      name: config.chain.nativeCurrency.name,
      symbol: config.chain.nativeCurrency.symbol,
      balance: nativeBalance,
      priceUsd: null, // Native token price would need external API
      priceNative: '1', // 1:1 with native token
      chainId,
      isNative: true
    },
    ...tokenBalances.filter(Boolean)
  ];

  return {
    chainId,
    chainName: config.chain.name,
    nativeCurrency: config.chain.nativeCurrency,
    tokens: portfolio.filter(Boolean),
    totalValue: '0' // This would need to be calculated with price data
  };
}

const botOp = new Hono();

// Get User Data
botOp.get(
  "/user",
  isLoggedIn,
  async (c: any) => {
    const tgUserId = c.get('user');
    const user = await storageInstance.read(tgUserId);
    if (!user) {
      return c.json({
        error: "User not found",
        message: "There was an error fetching the user's data. Please try again."
      }, 400)
    }

    // Fetch Telegram username from Telegram API
    let telegramUsername = null;
    try {
      const botToken = process.env.BOT_TOKEN;
      if (botToken) {
        const res = await fetch(`https://api.telegram.org/bot${botToken}/getChat?chat_id=${tgUserId}`);
        if (res.ok) {
          const data = await res.json();
          telegramUsername = data?.result?.username || null;
        }
      }
    } catch (e) {
      // Ignore error, username will be null
    }

    const sanitizedUser = {
      tgUserId,
      ...user.__d,
      telegramUsername, // Add username to response
      settings: {
        ...user.__d.settings,
        wallets: user.__d.settings.wallets.map((wallet: any) => {
          delete wallet.privateKey;
          delete wallet.phrase;
          return {
            address: wallet.address,
          }
        })
      }
    }

    return c.json({
      user: sanitizedUser
    }, 200)
  }
)

// Get User's Wallets
botOp.get("/user/wallets", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);
  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }
  const wallets = user.__d.settings.wallets;
  const sanitizedWallet = wallets.map((wallet: any) => {
    delete wallet.privateKey;
    delete wallet.phrase;
    return {
      address: wallet.address,
    }
  })

  return c.json({
    wallets: sanitizedWallet
  }, 200)
})

// Generate New Wallet
botOp.post("/user/wallet/create", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  const wallet = ethers.Wallet.createRandom();
  user.__d.settings.wallets.push({
    address: wallet.address as Address,
    phrase: wallet.mnemonic?.phrase,
    privateKey: wallet.privateKey as Address,
  });

  await storageInstance.write(tgUserId, user);

  return c.json({
    address: wallet.address,
    privateKey: wallet.privateKey,
    phrase: wallet.mnemonic?.phrase,
    message: "Wallet created successfully."
  }, 200)
})

// Get Wallet Private Key
botOp.get("/user/wallets/private/:address", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  if (!user.__d.settings.wallets.length) {
    return c.json({
      error: "No wallets found",
      message: "There was an error fetching the wallet's data. Please try again."
    }, 400)
  }
  const address = c.req.param('address');
  const wallet = user.__d.settings.wallets.find((wallet: any) => wallet.address === address);

  if (!wallet) {
    return c.json({
      error: "Wallet not found",
      message: "There was an error fetching the wallet's data. Please try again."
    }, 400)
  }

  return c.json({
    address: wallet.address,
    privateKey: wallet.privateKey,
    phrase: wallet.phrase,
  }, 200)
})

const walletAddressSchema = z.object({
  address: z.string().min(42).max(42).optional(),
})

// Set Primary Wallet
botOp.post("/user/wallets/primary", isLoggedIn, zValidator('json', walletAddressSchema), async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  if (!user.__d.settings.wallets.length) {
    return c.json({
      error: "No wallets found",
      message: "There was an error fetching the wallet's data. Please try again."
    }, 400)
  }
  const address = c.req.valid('json').address;
  const walletIndex = user.__d.settings.wallets.findIndex((wallet: any) => wallet.address === address);

  if (walletIndex === -1) {
    return c.json({
      error: "Wallet not found",
      message: "There was an error fetching the wallet's data. Please try again."
    }, 400)
  }

  // Set the walletId to the selected wallet's index
  user.__d.walletId = walletIndex;

  // Optionally, remove isActive from all wallets if not used elsewhere
  user.__d.settings.wallets = user.__d.settings.wallets.map((wallet: any) => {
    const { isActive, ...rest } = wallet;
    return rest;
  });

  await storageInstance.write(tgUserId, user);

  return c.json({
    message: "Wallet set as primary successfully."
  }, 200)
})

const privateKeySchema = z.object({
  privateKey: z.string().min(64).max(64),
})

// Import Private Key
botOp.post("/user/wallets/import", isLoggedIn, zValidator('json', privateKeySchema), async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  const { privateKey } = c.req.valid('json');

  try {
    const wallet = new ethers.Wallet(
      privateKey.startsWith("0x")
        ? privateKey
        : `0x${privateKey}`
    );

    const walletInfo = {
      address: wallet.address as Address,
      privateKey: wallet.privateKey as Address,
      phrase: wallet.mnemonic?.phrase ?? undefined,
    };

    const walletIndex = user.__d.settings.wallets.findIndex(
      (w: any) => w?.address === walletInfo.address
    );

    if (walletIndex !== -1) {
      user.__d.settings.wallets[walletIndex] = walletInfo;
    } else {
      user.__d.settings.wallets.push(walletInfo);
    }

    await storageInstance.write(tgUserId, user);

    // Send success message via Telegram
    await sendMessage({
      chat_id: tgUserId,
      text: `✅ Wallet Successfully Imported!\n\n` +
        `Address: \`${wallet.address}\`\n\n` +
        `Your wallet has been securely added to your account. You can now use it for trading and other operations.`,
      parse_mode: "Markdown"
    });

    return c.json({
      message: "✅ Wallet imported successfully! You can now use it for trading and other operations.",
      address: wallet.address
    }, 200)

  } catch (error) {
    return c.json({
      error: "Invalid private key",
      message: "❌ The private key format is invalid. Please check and try again."
    }, 400)
  }
})

// Token Search by Contract Address
const contractAddressSchema = z.object({
  address: z.string().min(42).max(42),
})

botOp.post("/token/search", isLoggedIn, zValidator('json', contractAddressSchema), async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  const { address } = c.req.valid('json');
  // Use user's selected chainId or fallback to default
  const chainId = user.__d.settings.chainId;
  if (!chainId) {
    return c.json({
      error: "Chain ID not set",
      message: "Please set a chain ID in your settings."
    }, 400)
  }

  // Use user's primary wallet
  let walletId = user.__d.walletId;
  const wallets = user.__d.settings.wallets;
  if (typeof walletId !== "number" || walletId < 0 || walletId >= wallets.length) {
    walletId = 0;
  }
  const wallet = wallets[walletId];
  if (!wallet) {
    return c.json({
      error: "Wallet not found",
      message: "No wallet available for token lookup."
    }, 400);
  }

  try {
    const pair = await extractTokenFromPair(chainId, address);
    const balance = await getBalance(wallet.address, address, chainId);
    return c.json({
      address,
      name: pair?.quoteToken?.name || null,
      symbol: pair?.quoteToken?.symbol || null,
      balance,
      priceUsd: pair?.priceUsd || null,
      priceNative: pair?.priceNative || null,
      fdv: pair?.fdv || null,
      liquidity: pair?.liquidity?.usd || null,
      volume24h: pair?.volume?.h24 || null,
      priceChange24h: pair?.priceChange?.h24 || null,
      url: pair?.url || null,
    }, 200);
  } catch (e) {
    return c.json({
      error: "Token not found or fetch failed",
      message: "Could not fetch token info. Please check the contract address and try again."
    }, 400);
  }
});

// Setting Schema
const settingSchema = z.object({
  chainId: z.number().optional(),
  slippage: z.number().optional(),
  gasPrice: z.number().optional(),
})

// Change Settings
botOp.post("/user/settings", isLoggedIn, zValidator('json', settingSchema), async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  const { chainId, slippage, gasPrice } = c.req.valid('json');

  user.__d.settings.chainId = chainId || user.__d.settings.chainId;
  user.__d.settings.slippage = slippage || user.__d.settings.slippage;
  user.__d.settings.gasPrice = gasPrice || user.__d.settings.gasPrice;

  await storageInstance.write(tgUserId, user);

  return c.json({
    message: "Settings updated successfully."
  }, 200)
})

// Get Settings
botOp.get("/user/settings", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  return c.json({
    settings: user.__d.settings
  }, 200)
})

// Get Primary Wallet's Native Balance
botOp.get("/user/wallets/balance", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  const wallets = user.__d.settings.wallets;
  if (!Array.isArray(wallets) || wallets.length === 0) {
    return c.json({
      error: "No wallets found",
      message: "There was an error fetching the wallet's data. Please try again."
    }, 400)
  }

  // Ensure walletId is a valid index
  let walletId = user.__d.walletId;
  if (typeof walletId !== "number" || walletId < 0 || walletId >= wallets.length) {
    walletId = 0; // fallback to first wallet
  }

  const wallet = wallets[walletId];

  if (!wallet) {
    return c.json({
      error: "Wallet not found",
      message: "There was an error fetching the wallet's data. Please try again."
    }, 400)
  }

  const chainId = user.__d.settings.chainId;
  if (!chainId) {
    return c.json({
      error: "Chain ID not set",
      message: "Please set a chain ID in your settings."
    }, 400)
  }

  const config = getConfig(chainId);
  const balance = await getBalance(
    wallet.address,
    config.nativeCurrencyAddress,
    chainId
  );

  return c.json({
    balance
  }, 200)
})

// Get User's Portfolio across all chains
botOp.get("/user/portfolio", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400);
  }

  const wallet = user.__d.settings.wallets[user.__d.walletId];
  if (!wallet) {
    return c.json({
      error: "No wallets found",
      message: "No wallet available for portfolio lookup."
    }, 400);
  }

  // Get all supported chains
  const supportedChains = Object.values(ESupportedChains)
    .filter((v): v is number => typeof v === 'number');

  // Get portfolio for each chain in parallel
  const portfolios = await Promise.all(
    supportedChains.map(chainId => 
      getPortfolioForChain(wallet.address, chainId, user.__d.data.tokens || [])
    )
  );

  return c.json({
    success: true,
    data: portfolios
  }, 200);
});

// Get User's Portfolio for a specific chain
botOp.get("/user/portfolio/:chainId", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const chainId = parseInt(c.req.param('chainId'));
  
  if (isNaN(chainId) || !Object.values(ESupportedChains).includes(chainId)) {
    return c.json({
      error: "Invalid chain ID",
      message: "The provided chain ID is not supported."
    }, 400);
  }

  const user = await storageInstance.read(tgUserId);
  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400);
  }

  const wallet = user.__d.settings.wallets[user.__d.walletId];
  if (!wallet) {
    return c.json({
      error: "No wallets found",
      message: "No wallet available for portfolio lookup."
    }, 400);
  }

  try {
    const portfolio = await getPortfolioForChain(
      wallet.address, 
      chainId, 
      user.__d.data.tokens || []
    );

    return c.json({
      success: true,
      data: portfolio
    }, 200);
  } catch (error) {
    console.error(`Error fetching portfolio for chain ${chainId}:`, error);
    return c.json({
      error: "Internal Server Error",
      message: "There was an error fetching the portfolio. Please try again later."
    }, 500);
  }
});

// Send 2FA Code
botOp.post("/user/2fa/send", isLoggedIn, async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  // Generate 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const ttl = Date.now() + 5 * 60 * 1000; // 5 minutes from now

  user.__d.otp = otp;
  user.__d.otpTTL = ttl;
  await storageInstance.write(tgUserId, user);

  // Send OTP via Telegram bot
  const message = `🔐 Your verification code is: *${otp}*\n\n` +
    `This code will expire in 5 minutes.\n` +
    `Do not share this code with anyone for security reasons.`;

  await sendMessage({ chat_id: tgUserId, text: message, parse_mode: "Markdown" });

  return c.json({
    message: "2FA code sent successfully to your Telegram."
  }, 200)
})

const verify2faSchema = z.object({
  otp: z.string().min(6).max(6),
})

// Verify 2FA Code
botOp.post("/user/2fa/verify", isLoggedIn, zValidator('json', verify2faSchema), async (c: any) => {
  const tgUserId = c.get('user');
  const user = await storageInstance.read(tgUserId);

  if (!user) {
    return c.json({
      error: "User not found",
      message: "There was an error fetching the user's data. Please try again."
    }, 400)
  }

  const { otp } = c.req.valid('json');

  // Check if OTP Exists and Validate OTP
  if (user.__d.otp !== otp || !user.__d.otp) {
    return c.json({
      error: "Invalid OTP",
      message: "There was an error verifying your OTP. Please try again."
    }, 400)
  }

  // Validate OTP TTL
  if (user.__d.otpTTL as number < Date.now()) {
    return c.json({
      error: "Invalid OTP",
      message: "Your OTP has expired. Please request a new one."
    }, 400)
  }

  // Delete OTP
  user.__d.otp = undefined;
  user.__d.otpTTL = undefined;
  await storageInstance.write(tgUserId, user);

  // Send success message via Telegram
  await sendMessage({
    chat_id: tgUserId,
    text: "✅ 2FA Verification Successful!\n\nYou can now proceed with defined action.",
    parse_mode: "Markdown"
  });

  return c.json({
    message: "2FA verification successful. You can now proceed with defined action."
  }, 200)
})

export default botOp;
