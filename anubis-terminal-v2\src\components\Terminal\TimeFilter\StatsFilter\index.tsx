"use client";
import { useState } from "react";
import { motion } from "framer-motion";
import { IoFilter } from "react-icons/io5";


export default function StatsFilter() {

    return (
        <motion.button className="flex items-center gap-x-2 border border-white/30 px-2.5 py-1 rounded-sm  text-xs"
            whileTap={{
                scale: 1.03
            }}
            whileHover={{
                cursor: "pointer"
            }}
        >
            <IoFilter className="text-white" /> <span>Filter</span>
        </motion.button>
    )
}

function FilterDropdown() {
    return (
        <>


        </>
    )
}