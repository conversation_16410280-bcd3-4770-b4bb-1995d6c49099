// config/bootstrap.js

const axios = require("axios");
const retry = require("async-retry");

module.exports.bootstrap = async function () {
  sails.log.info(
    `Running Telegram webhook bootstrap with public URL: ${sails.config.custom.appPublicUrl} and token: ${sails.config.custom.telegramBotToken}.`
  );

  const TELEGRAM_BOT_TOKEN = sails.config.custom.telegramBotToken;
  const YOUR_APP_PUBLIC_URL = sails.config.custom.appPublicUrl;
  const MAX_RETRIES = 3;

  // IMPORTANT: Only set the webhook if you have a public URL.
  // In local development with ngrok, you'll run ngrok first and then lift Sails.
  // If `YOUR_APP_PUBLIC_URL` is not set (e.g., ngrok isn't running),
  // you might want to skip setting the webhook or log a warning.
  if (
    !YOUR_APP_PUBLIC_URL ||
    YOUR_APP_PUBLIC_URL.includes("YOUR_NGROK_URL_HERE_IF_MANUAL")
  ) {
    sails.log.warn(
      "Skipping Telegram webhook setup: appPublicUrl is not set. Ensure ngrok is running for local development or APP_PUBLIC_URL_PRODUCTION is set for production."
    );
    sails.log.info("Exiting Telegram webhook bootstrap.");
    return; // Exit the bootstrap function if no valid URL
  }

  const webhookUrl = `${YOUR_APP_PUBLIC_URL}/api/v1/webhook/telegram`;

  const setupWebhook = async () => {
    try {
      await retry(
        async (bail) => {
          try {
            await axios.post(
              `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/deleteWebhook`
            );
            sails.log.info("Existing Telegram webhook deleted (if any).");

            const response = await axios.post(
              `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/setWebhook`,
              {
                url: webhookUrl,
              }
            );

            if (response.data.ok) {
              sails.log.info(
                `Successfully set Telegram webhook to: ${webhookUrl}.`
              );
              return;
            } else {
              sails.log.error(
                "Failed to set Telegram webhook:",
                response.data.description
              );
              throw new Error("Telegram webhook setup failed");
            }
          } catch (error) {
            sails.log.error(
              `Telegram webhook setup failed on attempt #${error.attempt}. Error: ${error.message}`
            );
            if (error.attempt < MAX_RETRIES) {
              throw error;
            } else {
              bail(error);
            }
          }
        },
        {
          retries: MAX_RETRIES,
          minTimeout: 1000,
          maxTimeout: 5000,
        }
      );
    } catch (error) {
      sails.log.error("Error setting Telegram webhook:", error.message);
    } finally {
      sails.log.info("Exiting Telegram webhook bootstrap.");
    }
  };

  await setupWebhook();
};
