"use client";
import { useState, useEffect } from "react";
import { motion } from 'framer-motion';
import { useTokenPairs } from '@/hooks/useTokenPairs';
import { FaStar } from "react-icons/fa";
import { useWeb3 } from "@/contexts/Web3Context";
import { useParams } from "next/navigation";
import { Skeleton } from '@/components/ui/skeleton';
import { chains, getTimeAgo, getShareLink } from '@/utils/data';
import { toast } from 'react-toastify';


export default function TokenPairData() {
    const { chain, address } = useParams();
    const { data, tokenData, loading, error, fetchTokenPairs } = useTokenPairs(address as `0x${string}`, chain as string);
    const [favorite, setFavorite] = useState<boolean>(false);
    const tokenChain = chains.find(c => c.slug === chain);

    // Set page title based on token data
    useEffect(() => {
        if (data?.pair && tokenData?.marketData) {
            const price = Number(data.pair.priceUsd || tokenData.marketData.priceUSD).toFixed(6);
            const priceChange = data.pair.priceChange?.h24 || 0;
            const symbol = data.pair.baseToken?.symbol || 'Token';
            document.title = `${symbol}/${data.pair.quoteToken?.symbol} | $${price} | ${priceChange > 0 ? '↑' : '↓'} ${Math.abs(priceChange).toFixed(1)}% On ${tokenChain?.slug ? tokenChain.slug.charAt(0).toUpperCase() + tokenChain.slug.slice(1) : ''} / ${data?.pair?.dexId ? data.pair.dexId.charAt(0).toUpperCase() + data.pair.dexId.slice(1) : ''} | Anubis Terminal`;
        }
    }, [data, tokenData]);

    if (loading) {
        return (
            <div className='border-b border-white/20 flex justify-between gap-x-3 py-5 px-3'>
                <Skeleton className="w-5 h-5 rounded-full" />
                <div className="flex gap-x-1 items-center">
                    <Skeleton className="w-5 h-5 rounded-full" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-10" />
                </div>
                <Skeleton className="w-3 h-3 rounded-full" />
            </div>
        );
    }

    if (error) {
        return (
            <div className='border-b border-white/20 flex justify-between gap-x-3 py-5 px-3 text-red-500'>
                Error loading token data
            </div>
        );
    }

    console.log("Data", data);
    console.log("Token Data", tokenData)
    console.log("Token Chain", tokenChain)

    function handleCopyShareLink() {
        navigator.clipboard.writeText(getShareLink(chain as string, address as string)).then(() => {
            toast.success("Link Copied");
        }).catch((err) => {
            toast.error("Failed to copy link");
            console.error("Failed to copy: ", err);
        });
    }

    return (
        <div className="flex flex-col h-full">
            {/* Token Info Header - matches image */}
            <div className="flex items-center justify-between px-4 py-3 bg-[#181818] border-b border-white/10">
                {/* Left: Token logo, name, pair, network, DEX, share */}
                <div className="flex items-center gap-3 min-w-0">
                    {/* Favorite Star */}
                    <motion.button
                        whileTap={{ scale: 0.88 }}
                        whileHover={{ cursor: "pointer", scale: 1.04 }}
                        onClick={() => setFavorite(!favorite)}
                        className="mr-1"
                    >
                        <FaStar className={`${favorite ? "text-yellow-400" : "text-white/30"} text-lg`} />
                    </motion.button>
                    {/* Token Logo */}
                    <img
                        src={data?.pair?.info?.imageUrl || tokenData?.details?.imageLargeUrl}
                        alt={data?.pair?.baseToken?.symbol}
                        className="w-10 h-10 rounded-full border border-white/10 bg-black object-cover"
                    />
                    {/* Name and Pair */}
                    <div className="flex flex-col min-w-0">
                        <span className="font-orbitron font-bold text-white truncate text-base leading-tight">
                            {data?.pair?.baseToken?.symbol}/{data?.pair?.quoteToken?.symbol}
                        </span>
                        <div className="flex items-center gap-2 text-xs text-white/60 mt-0.5">
                            {/* Age */}
                            <span>{getTimeAgo(tokenData?.activity?.createdAt || 0)}</span>
                            {/* Network */}
                            <span className="flex items-center gap-1">
                                <img src={tokenChain?.logo} alt="network" className="w-4 h-4 inline-block" />
                                {tokenChain?.chain || data?.pair?.chainId}
                            </span>
                            {/* DEX */}
                            <span className="flex items-center gap-1">
                                <img src="/icons/dex.svg" alt="dex" className="w-4 h-4 inline-block" />
                                {data?.pair?.dexId || 'DEX'}
                            </span>
                            {/* Share */}
                            <button
                                onClick={handleCopyShareLink}
                                className="flex items-center gap-1 text-orange-400 hover:underline ml-2 cursor-pointer"
                            >
                                <svg width="16" height="16" fill="none" viewBox="0 0 16 16"><path d="M12.667 10.667v1.333A1.333 1.333 0 0 1 11.333 13.333H4.667A1.333 1.333 0 0 1 3.333 12V5.333A1.333 1.333 0 0 1 4.667 4h1.333" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" /><path d="M10.667 2.667h2.666v2.666" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" /><path d="M7.333 8.667 13.333 2.667" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" /></svg>
                                <span className="font-medium">Share</span>
                            </button>
                        </div>
                    </div>
                </div>
                {/* Right: Market Cap, Price, Liquidity, Taxes */}
                <div className="flex items-center gap-8">
                    <div className="flex flex-col items-end">
                        <span className="text-xs text-white/50">Market Cap</span>
                        <span className="font-orbitron font-semibold text-lg text-white">${Number(data?.pair?.marketCap || tokenData?.marketData?.marketCap).toLocaleString(undefined, { maximumFractionDigits: 2 })}M</span>
                    </div>
                    <div className="flex flex-col items-end">
                        <span className="text-xs text-white/50">Price</span>
                        <span className="font-orbitron font-semibold text-lg text-white">${Number(data?.pair?.priceUsd || tokenData?.marketData?.priceUSD).toFixed(7)}</span>
                    </div>
                    <div className="flex flex-col items-end">
                        <span className="text-xs text-white/50">Liquidity</span>
                        <span className="font-orbitron font-semibold text-lg text-white">${Number(data?.pair?.liquidity?.usd || tokenData?.marketData?.liquidity).toLocaleString(undefined, { maximumFractionDigits: 2 })}</span>
                    </div>
                    <div className="flex flex-col items-end">
                        <span className="text-xs text-white/50">Taxes</span>
                        <span className="font-orbitron font-semibold text-lg text-white">0.0% <span className="text-white/40">|</span> 0.0%</span>
                    </div>
                </div>
            </div>

            {/* Chart Placeholder */}
            <div className="flex-1 flex flex-col bg-black/40 border-b border-white/10 justify-center items-center min-h-[260px]">
                <div className="w-full h-64 flex items-center justify-center">
                    <span className="text-white/40 text-lg">[Chart Placeholder]</span>
                </div>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-white/10 bg-black/30">
                {['Trades', 'Open Orders', 'Order History', 'Liquidity', 'Holders', 'Top Traders'].map(tab => (
                    <button
                        key={tab}
                        className={`px-4 py-2 text-sm font-medium text-white/80 hover:text-white focus:outline-none ${tab === 'Trades' ? 'border-b-2 border-green-500 text-white' : ''}`}
                    >
                        {tab}
                    </button>
                ))}
            </div>

            {/* Trades Table (Dummy Data) */}
            <div className="flex-1 overflow-y-auto bg-black/20">
                <table className="min-w-full text-xs text-left text-white/80">
                    <thead className="bg-black/40 border-b border-white/10">
                        <tr>
                            <th className="px-3 py-2 font-semibold">Age</th>
                            <th className="px-3 py-2 font-semibold">Side</th>
                            <th className="px-3 py-2 font-semibold">MCap</th>
                            <th className="px-3 py-2 font-semibold">Amount</th>
                            <th className="px-3 py-2 font-semibold">Total USD</th>
                            <th className="px-3 py-2 font-semibold">Total USDT</th>
                            <th className="px-3 py-2 font-semibold">Maker</th>
                        </tr>
                    </thead>
                    <tbody>
                        {[
                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '195.2', totalUsd: '399.21', totalUsdt: '399.1553', maker: '186Cf5a' },
                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '244.5', totalUsd: '500.07', totalUsdt: '500.000', maker: '7eeF25A9' },
                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '760.9', totalUsd: '1,556.51', totalUsdt: '1,556.2861', maker: 'A1058569' },
                            { age: '3s', side: 'Sell', mcap: '204.6M', amount: '37.8', totalUsd: '77.37', totalUsdt: '77.3585', maker: '67d1C5d8' },
                            { age: '3s', side: 'Sell', mcap: '204.6M', amount: '1.01K', totalUsd: '2,059.90', totalUsdt: '2,059.6103', maker: '770Fa894' },
                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '1.61K', totalUsd: '3,300.47', totalUsdt: '3,300.000', maker: '3243B0C4' },
                            { age: '3s', side: 'Buy', mcap: '204.6M', amount: '801.8', totalUsd: '1,640.23', totalUsdt: '1,640.000', maker: '7F7355BF' },
                        ].map((row, i) => (
                            <tr key={i} className="border-b border-white/5 last:border-0">
                                <td className="px-3 py-2 whitespace-nowrap">{row.age}</td>
                                <td className={`px-3 py-2 whitespace-nowrap font-bold ${row.side === 'Buy' ? 'text-green-400' : 'text-red-400'}`}>{row.side}</td>
                                <td className="px-3 py-2 whitespace-nowrap">{row.mcap}</td>
                                <td className="px-3 py-2 whitespace-nowrap">{row.amount}</td>
                                <td className="px-3 py-2 whitespace-nowrap">{row.totalUsd}</td>
                                <td className="px-3 py-2 whitespace-nowrap">{row.totalUsdt}</td>
                                <td className="px-3 py-2 whitespace-nowrap">{row.maker}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    )
}