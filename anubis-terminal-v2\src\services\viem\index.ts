// import { ethers } from "ethers";
import { avalancheClient, baseClient, unichainClient, berachainClient, sonicClient } from "@/utils/web3/config";

export const getBalanceForEachChain = async (address: `0x${string}`) => {
    const clients = [
        { name: "Avalanche", client: avalancheClient },
        { name: "Base", client: baseClient },
        { name: "Unichain", client: unichainClient },
        { name: "<PERSON><PERSON><PERSON><PERSON>", client: berachainClient },
        { name: "<PERSON>", client: sonicClient }
    ];

    const balances = clients.map(async ({ name, client }) => {
        try {
            const balance = await client.getBalance({ address });
            return { name, balance };
        } catch (error) {
            console.error(`Error fetching balance for ${name}:`, error);
            return { name, balance: BigInt(0) };
        }
    });

    return Promise.all(balances);
}