
import { Address } from "viem";
import { ESupportedChains } from "../config";
import Node<PERSON>etch<PERSON>ache from "node-fetch-cache";
import { cachedFetch } from "./gecko";

// Cache for Odos quotes
const odosQuoteCache = new Map<string, any>();
const ODOS_QUOTE_CACHE_TTL = 30 * 1000; // 30 seconds

// Cache for token prices
const tokenPriceCache = new Map<string, any>();
const TOKEN_PRICE_CACHE_TTL = 2 * 60 * 1000; // 2 minutes

// Function to quote a path for swapping between assets
export async function quotePath({
  chainId,
  amount,
  inputToken,
  outputToken,
  userAddr,
  slippageLimitPercent = 0.3,
}: {
  chainId: ESupportedChains;
  amount: string;
  inputToken: Address;
  outputToken: Address;
  userAddr: Address;
  slippageLimitPercent?: number;
}) {
  // Create a cache key
  const cacheKey = `odos_quote_${chainId}_${inputToken}_${outputToken}_${amount}_${slippageLimitPercent}`;

  // Check if we have a valid cached response
  const cachedResponse = odosQuoteCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - ODOS_QUOTE_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached Odos quote for ${inputToken} -> ${outputToken}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching Odos quote for ${inputToken} -> ${outputToken}`);
  try {
    const response = await cachedFetch("https://api.odos.xyz/sor/quote/v2", {
      headers: {
        accept: "application/json",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-type": "application/json",
        priority: "u=1, i",
        "sec-ch-ua": '"Not/A)Brand";v="8", "Chromium";v="126"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        Referer: "https://docs.odos.xyz/",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: JSON.stringify({
        chainId,
        compact: true,
        inputTokens: [
          {
            amount,
            tokenAddress: inputToken,
          },
        ],
        outputTokens: [
          {
            proportion: 1,
            tokenAddress: outputToken,
          },
        ],
        referralCode: 3491552714, // Sonic Chain Referral
        slippageLimitPercent,
        sourceBlacklist: [],
        sourceWhitelist: [],
        userAddr,
      }),
      method: "POST",
    });

    const data = (await response.json()) as {
      inTokens: [Address];
      outTokens: [Address];
      inAmounts: [number];
      outAmounts: [number];
      gasEstimate: number;
      dataGasEstimate: number;
      gweiPerGas: number;
      gasEstimateValue: number;
      inValues: [number];
      outValues: [number];
      netOutValue: number;
      priceImpact: number;
      percentDiff: number;
      partnerFeePercent: number;
      pathId: string;
      pathViz: null;
      blockNumber: number;
    };

    // Cache the response
    odosQuoteCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });

    return data;
  } catch (error) {
    console.error("Error quoting path:", error);
    return null;
  }
}

// Function to assemble an Odos quote into a transaction
export async function assembleQuote(
  userAddr: Address,
  pathId: string,
  simulate = true
) {
  try {
    const response = await fetch("https://api.odos.xyz/sor/assemble", {
      method: "POST",
      headers: {
        accept: "application/json",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-type": "application/json",
        priority: "u=1, i",
        "sec-ch-ua": '"Not/A)Brand";v="8", "Chromium";v="126"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        Referer: "https://docs.odos.xyz/",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: JSON.stringify({
        userAddr,
        pathId,
        simulate,
        receiver: userAddr,
      }),
    });

    const data = (await response.json()) as {
      deprecated: null;
      blockNumber: number;
      gasEstimate: number;
      gasEstimateValue: number;
      inputTokens: [
        {
          tokenAddress: Address;
          amount: string;
        }
      ];
      outputTokens: [
        {
          tokenAddress: Address;
          amount: string;
        }
      ];
      netOutValue: number;
      outValues: [string];
      transaction: {
        gas: number;
        gasPrice: number;
        value: string;
        to: Address;
        from: Address;
        data: Address;
        nonce: number;
        chainId: ESupportedChains;
      };
      simulation?: {
        isSuccess: boolean;
        amountsOut: [];
        gasEstimate: number;
        simulationError?: {
          type: "other";
          errorMessage: string
        };
      };
    };
    return data;
  } catch (error) {
    console.error("Error assembling quote:", error);
    throw error;
  }
}

// Function to get the price of a single token
export async function getTokenPrice(
  chainId: number,
  tokenAddr: string
): Promise<number> {
  // Create a cache key
  const cacheKey = `token_price_${chainId}_${tokenAddr}`;

  // Check if we have a valid cached response
  const cachedResponse = tokenPriceCache.get(cacheKey);
  if (cachedResponse && cachedResponse.timestamp > Date.now() - TOKEN_PRICE_CACHE_TTL) {
    console.log(`[CACHE HIT] Using cached token price for ${tokenAddr}`);
    return cachedResponse.data;
  }

  console.log(`[CACHE MISS] Fetching token price for ${tokenAddr}`);
  try {
    const response = await cachedFetch(
      `https://api.odos.xyz/pricing/token/${chainId}/${tokenAddr}`,
      {
        method: "GET",
      }
    );

    const data = (await response.json()) as {
      currencyId: "USD";
      price: number;
    };

    // Cache the response
    tokenPriceCache.set(cacheKey, {
      data: data.price,
      timestamp: Date.now()
    });

    return data.price;
  } catch (error) {
    console.error("Error getting token price:", error);
    return 0;
  }
}
