"use client"
import { motion } from "framer-motion";
import Link from "next/link";
import { FaTelegram } from "react-icons/fa";

export default function CTA() {
  return (
    <section className="py-20 px-4 md:px-8 lg:px-12 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[#0a0a0a] z-0"></div>
      
      {/* Animated particles */}
      <div className="absolute inset-0 z-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100],
              opacity: [0, 0.8, 0],
              scale: [1, 0.5],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <div className="max-w-5xl mx-auto relative z-10">
        <div className="bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a] p-8 md:p-12 lg:p-16 rounded-lg border border-white/10 relative overflow-hidden">
          {/* Animated gradient border */}
          <motion.div
            className="absolute inset-0 opacity-30"
            animate={{
              background: [
                "linear-gradient(0deg, transparent 0%, white 50%, transparent 100%)",
                "linear-gradient(90deg, transparent 0%, white 50%, transparent 100%)",
                "linear-gradient(180deg, transparent 0%, white 50%, transparent 100%)",
                "linear-gradient(270deg, transparent 0%, white 50%, transparent 100%)",
                "linear-gradient(0deg, transparent 0%, white 50%, transparent 100%)",
              ],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "linear",
            }}
          />
          
          <div className="relative z-10">
            <motion.div 
              className="text-center mb-8"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold mb-4 text-white">
                Ready to Elevate Your Trading?
              </h2>
              <p className="text-gray-400 font-space-grotesk max-w-2xl mx-auto text-lg">
                Join thousands of professional traders using Anubis Terminal for lightning-fast, secure on-chain trading.
              </p>
            </motion.div>

            <motion.div 
              className="flex flex-col sm:flex-row items-center justify-center gap-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              <Link href="/login">
                <motion.button
                  className="flex items-center gap-2 font-orbitron font-semibold bg-white text-black px-8 py-4 rounded-md hover:shadow-lg hover:shadow-white/20 w-full sm:w-auto"
                  whileHover={{
                    boxShadow: "0 0 20px rgba(255, 255, 255, 0.5)",
                  }}
                  whileTap={{
                    backgroundColor: "#f0f0f0",
                  }}
                >
                  <FaTelegram className="text-[#0088cc]" />
                  Start Trading Now
                </motion.button>
              </Link>
              
              <Link href="#features">
                <motion.button
                  className="font-orbitron font-semibold border border-white text-white px-8 py-4 rounded-md hover:bg-white/10 w-full sm:w-auto"
                  whileHover={{
                    boxShadow: "0 0 15px rgba(255, 255, 255, 0.3)",
                  }}
                  whileTap={{
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                  }}
                >
                  Learn More
                </motion.button>
              </Link>
            </motion.div>
            
            <motion.div 
              className="mt-8 text-center"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              <p className="text-gray-500 font-space-grotesk text-sm">
                No credit card required. Start trading in minutes.
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
