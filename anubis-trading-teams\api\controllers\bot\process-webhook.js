const {
  processInit,
  setupGroup,
  handleCallBackQuery,
  handleTextMessage,
  handleGroupAppEdit,
} = require('../../utils/bot-functions');
const { Telegraf } = require('telegraf');

module.exports = {
  friendlyName: 'Process webhook',

  description: 'Handle Telegram bot webhooks using Telegraf',

  inputs: {},

  exits: {},

  fn: async function () {
    const bot = new Telegraf(sails.config.telegram.botToken);

    // Set up your bot commands
    bot.command('start', async (ctx) => {
      try {
        await processInit(ctx);
      } catch (error) {
        sails.log.error(error);
      }
    });

    // Handle callback_data
    bot.on('callback_query', async (ctx) => {
      sails.log.debug(ctx.update.callback_query);
      try {
        await handleCallBackQuery(ctx);
      } catch (error) {
        sails.log.error(error);
      }
    });

    // Set Up
    bot.command('setup', async (ctx) => {
      try {
        await setupGroup(ctx);
      } catch (error) {
        sails.log.error(error);
      }
    });

    // App Command
    bot.command('app', async (ctx) => {
      try {
        await handleGroupAppEdit(ctx);
      } catch (error) {
        sails.log.error(error);
      }
    });

    // Handle other message types
    bot.on('message', async (ctx) => {
      sails.log.debug('New Message:', ctx.message);
      try {
        await handleTextMessage(ctx);
      } catch (error) {
        sails.log.error(error);
      }
    });

    bot.on('photo', async (ctx) => {
      try {
        sails.log.info(ctx);
      } catch (error) {
        sails.log.error(error);
      }
    });

    // Process the update
    try {
      await bot.handleUpdate(this.req.body);
    } catch (error) {
      sails.log.error('Error processing update:', error);
      return;
    }

    return;
  },
};

