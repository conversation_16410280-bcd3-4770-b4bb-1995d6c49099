import { Address } from "viem";
import { ESupportedChains } from "../config";
import { TypedDataField } from "@ethersproject/abstract-signer";
import NodeFetchCache from "node-fetch-cache";

// Create a cached fetch instance with TTL for successful responses
const cachedFetch = NodeFetchCache.create({
    // Only cache responses with a 2xx status code
    shouldCacheResponse: (response) => response.ok,
    ttl: 30 * 1000, // 30 seconds cache for price quotes
});

// Cache for 0x quotes
const zeroExQuoteCache = new Map<string, any>();
const ZEROX_QUOTE_CACHE_TTL = 30 * 1000; // 30 seconds




interface Permit2EIP712Types extends Record<string, TypedDataField[]> {
    EIP712Domain: TypedDataField[];
    PermitTransferFrom: TypedDataField[];
    TokenPermissions: TypedDataField[];
}

interface Permit2EIP712Domain {
    name: string;
    chainId: number;
    verifyingContract: string;
}

interface Permit2EIP712Message {
    permitted: {
        token: string;
        amount: string;
    };
    spender: string;
    nonce: string;
    deadline: string;
}

interface Permit2EIP712 {
    types: Permit2EIP712Types;
    domain: Permit2EIP712Domain;
    message: Permit2EIP712Message;
    primaryType: string;
}

interface Permit2Data {
    type: string;
    hash: string;
    eip712: Permit2EIP712;
}


interface ZeroExQuoteResponse {
    blockNumber: string;
    buyAmount: string;
    buyToken: string;
    fees: {
        integratorFee: {
            amount: string;
            token: string;
            type: string;
        } | null;
        zeroExFee: {
            amount: string;
            token: string;
            type: string;
        };
        gasFee: null;
    };
    issues: {
        allowance: null;
        balance: null;
        simulationIncomplete: boolean;
        invalidSourcesPassed: any[];
    };
    liquidityAvailable: boolean;
    minBuyAmount: string;
    permit2: Permit2Data;
    route: {
        fills: {
            from: string;
            to: string;
            source: string;
            proportionBps: string;
        }[];
        tokens: {
            address: string;
            symbol: string;
        }[];
    };
    sellAmount: string;
    sellToken: string;
    tokenMetadata: {
        buyToken: {
            buyTaxBps: string;
            sellTaxBps: string;
        };
        sellToken: {
            buyTaxBps: string;
            sellTaxBps: string;
        };
    };
    totalNetworkFee: string;
    transaction: {
        to: string;
        data: string;
        gas: string;
        gasPrice: string;
        value: string;
    };
    zid: string;
}

const xApiKey = "1ac1d5c1-88c4-455f-bc32-30762a544141";
const xApiUrl = "https://api.0x.org/swap/v1/quote?";

const feeRecipient = "******************************************";
const buyTokenPercentageFee = "100";

export async function get0xQuote({
    chainId,
    amount,
    inputToken,
    outputToken,
    slippageLimitPercent = 0.3,
    userAddr,
}: {
    chainId: any;
    amount: string;
    inputToken: Address;
    outputToken: Address;
    slippageLimitPercent?: number;
    userAddr: Address;
}) {
    const eth = "******************************************";
    const sellToken = inputToken.toLowerCase() === "******************************************"
        ? eth
        : inputToken;

    // Create a cache key
    const cacheKey = `0x_${chainId}_${sellToken}_${outputToken}_${amount}_${slippageLimitPercent}`;

    // Check if we have a valid cached response
    const cachedResponse = zeroExQuoteCache.get(cacheKey);
    if (cachedResponse && cachedResponse.timestamp > Date.now() - ZEROX_QUOTE_CACHE_TTL) {
        console.log(`[CACHE HIT] Using cached 0x quote for ${sellToken} -> ${outputToken}`);
        return cachedResponse.data;
    }

    console.log(`[CACHE MISS] Fetching 0x quote for ${sellToken} -> ${outputToken}`);
    try {
        const params = new URLSearchParams({
            sellToken: sellToken,
            buyToken: outputToken,
            sellAmount: amount,
            takerAddress: userAddr,
            slippagePercentage: slippageLimitPercent.toString(),
            chainId,
            swapFeeRecipient: feeRecipient,
            swapFeeBps: buyTokenPercentageFee,
            swapFeeToken: eth
        });

        const response = await cachedFetch(`${xApiUrl}?${params.toString()}`, {
            method: "GET",
            headers: {
                "0x-api-key": xApiKey,
                "0x-version": "v2",
                "Content-Type": "application/json",
            },
        });

        const data = await response.json() as ZeroExQuoteResponse;

        // Cache the response
        zeroExQuoteCache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });

        return data;
    } catch (error) {
        console.error("Error quoting path:", error);
        return null;
    }
}
