"use client";
import { useState } from "react";
import { motion } from "framer-motion";
import { FaCaretDown } from "react-icons/fa";

export default function TimeFilter({ timeOptions, selectedTime, handleTimeSelect }: {
    timeOptions: string[],
    selectedTime: string,
    handleTimeSelect: (time: string) => void
}) {


    return (
        <div className="rounded-sm border border-white/10 flex">
            {timeOptions.map((time, index) => (
                <button
                    key={index}
                    onClick={() => handleTimeSelect(time)}
                    className={`px-2.5 py-1 rounded-sm hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? "bg-white/10" : ""
                        }`}
                >
                    {time.toUpperCase()}
                </button>
            ))}
        </div>
    );
}

export function TimeFilterDropdown({ timeOptions, selectedTime, handleTimeSelect }: {
    timeOptions: string[],
    selectedTime: string,
    handleTimeSelect: (time: string) => void
}) {
    const [isOpen, setIsOpen] = useState(false);
    return (
        <div className="relative flex items-center gap-x-1">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="px-2.5 py-1 rounded-sm border border-white/10 text-xs hover:bg-white/10 transition-colors flex items-center gap-x-1"
            >
                {selectedTime.toUpperCase()}
                <FaCaretDown className="text-white" />
            </button>
            {isOpen && (
                <div className="absolute top-8 left-0 mt-1 w-20 rounded-sm border border-white/10 bg-black z-10 flex flex-col gap-y-1">
                    {timeOptions.map((time, index) => (
                        <motion.button
                            key={index}
                            onClick={() => {
                                handleTimeSelect(time);
                                setIsOpen(false);
                            }}
                            className={`px-2.5 py-1 hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? "bg-white/10" : ""}`}
                        >{time.toUpperCase()}
                        </motion.button>
                    ))}
                </div>
            )}
        </div>
    );
}
