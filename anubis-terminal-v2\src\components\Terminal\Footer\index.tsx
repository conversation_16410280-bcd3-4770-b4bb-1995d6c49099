"use client";

import { SiGitbook } from "react-icons/si";
import { FaXTwitter } from "react-icons/fa6";
import { FaD<PERSON>rd, FaTelegramPlane, FaWallet } from "react-icons/fa";
import { TbAntennaBars5, TbAntennaBarsOff } from "react-icons/tb";
import { FaFire } from "react-icons/fa";
import { HiSparkles } from "react-icons/hi";
import { FaBriefcase } from "react-icons/fa";
import { FaCrosshairs } from "react-icons/fa";
import Link from 'next/link';
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";

const telegramBotUsername = process.env.NODE_ENV === "production" ? process.env.NEXT_PUBLIC_BOT_USERNAME_PROD : process.env.NEXT_PUBLIC_BOT_USERNAME_DEV;


export const terminalNavLinks = [
    {
        title: "Trending",
        url: "/terminal/trending",
        icon: <FaFire className="text-md" />
    },
    {
        title: "New",
        url: "/terminal/new",
        icon: <HiSparkles className="text-md" />
    },
    {
        title: "portfolio",
        url: "/terminal/portfolio",
        icon: <FaBriefcase className="text-md" />
    },
    {
        title: "Wallet",
        url: "/terminal/wallet",
        icon: <FaWallet className="text-md" />
    },
    {
        title: "Sniping",
        url: "/terminal/sniping",
        icon: <FaCrosshairs className="text-md" />
    }
]

const socials = [
    {
        icon: <FaTelegramPlane className="text-white text-sm" />,
        href: `https://t.me/${telegramBotUsername}`,
        label: "Telegram"
    },
    {
        icon: <FaXTwitter className="text-white text-sm" />,
        href: "https://twitter.com/AnubisTerminal",
        label: "Twitter"
    },
    {
        icon: <FaDiscord className="text-white text-sm" />,
        href: "https://discord.gg/anubisterminal",
        label: "Discord"
    },
    {
        icon: <SiGitbook className="text-white text-sm" />,
        href: "https://docs.anubis.finance",
        label: "Documentation"
    }
]

interface NetworkStatusType {
    status: "Stable" | "Offline" | "Irregular";
}

function NetworkStatus({ status }: NetworkStatusType) {
    const statusMap = {
        Stable: <><TbAntennaBars5 className="text-green-500 text-xs" /> <span className="text-green-500 text-xs">Stable</span></>,
        Offline: <><TbAntennaBarsOff className="text-red-500 text-xs" /> <span className="text-red-500 text-xs">Offline</span></>,
        Irregular: <><TbAntennaBars5 className="text-yellow-500 text-xs" /> <span className="text-yellow-500 text-xs">Irregular</span></>
    };

    return statusMap[status];
}


export default function Footer() {
    const pathname = usePathname();

    // Find the active tab index
    const activeTabIndex = terminalNavLinks.findIndex(link => link.url.includes(pathname));

    return (
        <footer className="fixed bottom-0 left-0 w-full bg-black/50 md:bg-black pb-3 flex flex-col items-center justify-center">
            {/* Bottom Nav */}
            <div className={`md:hidden grid grid-cols-5 w-full px-1 relative`}>
                {/* Animated indicator for active tab */}
                {activeTabIndex !== -1 && (
                    <motion.div
                        className="absolute top-0 h-[3px] bg-white"
                        initial={false}
                        animate={{
                            left: `${(activeTabIndex * 25)}%`,
                            width: '25%'
                        }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30
                        }}
                        layoutId="activeTabIndicator"
                    />
                )}

                {terminalNavLinks.map((link, index) => {
                    const isActive = link.url.includes(pathname);
                    return (
                        <Link
                            href={link.url}
                            key={index}
                            className={`flex flex-col pt-3 items-center justify-center gap-y-2`}
                        >
                            <motion.span
                                className={`${isActive ? "text-white" : "text-white/70"}`}
                                animate={{
                                    color: isActive ? "rgba(255, 255, 255, 1)" : "rgba(255, 255, 255, 0.7)"
                                }}
                                transition={{ duration: 0.3 }}
                            >
                                {link.icon}
                            </motion.span>
                            <motion.span
                                className={`capitalize font-bold font-orbitron text-md`}
                                animate={{
                                    color: isActive ? "rgba(255, 255, 255, 1)" : "rgba(255, 255, 255, 0.7)"
                                }}
                                transition={{ duration: 0.3 }}
                            >
                                {link.title}
                            </motion.span>
                        </Link>
                    )
                })}
            </div>
            <div className="border-t border-white/10 hidden md:flex justify-between py-1 px-2 w-full">
                <div className="flex items-center gap-x-3">
                    <NetworkStatus status="Stable" />
                </div>
                <div className="flex items-center gap-x-3">
                    {socials.map((social, index) => (
                        <a key={index} href={social.href} target="_blank" rel="noopener noreferrer" className="flex items-center justify-center text-white hover:bg-white/10 transition-colors">
                            {social.icon}
                        </a>
                    ))}
                </div>
            </div>
        </footer >
    )
}