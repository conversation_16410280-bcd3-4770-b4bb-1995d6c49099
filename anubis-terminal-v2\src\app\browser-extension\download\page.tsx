"use client"
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import Link from "next/link";
import { FaArrowLeft, FaDownload, FaChrome, FaCheckCircle, FaExclamationTriangle, FaCopy, FaExternalLinkAlt, FaFirefox, FaEdge, FaSafari } from "react-icons/fa";

interface StepProps {
    number: number;
    title: string;
    description: string;
    children: React.ReactNode;
    isActive: boolean;
}

interface BrowserInfo {
    name: string;
    icon: React.ReactNode;
    extensionUrl: string;
    developerModeText: string;
    loadUnpackedText: string;
    supported: boolean;
}

const Step = ({ number, title, description, children, isActive }: StepProps) => {
    return (
        <motion.div
            className={`relative p-6 rounded-xl border transition-all duration-300 ${isActive
                ? 'bg-[#0f0f0f] border-white/30 shadow-lg shadow-white/5'
                : 'bg-[#0a0a0a] border-white/10'
                }`}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true, margin: "-100px" }}
        >
            {/* Step Number */}
            <div className="absolute -top-3 -left-3 w-8 h-8 bg-white text-black rounded-full flex items-center justify-center font-orbitron font-bold text-sm">
                {number}
            </div>

            {/* Content */}
            <div className="mt-4">
                <h3 className="font-orbitron font-bold text-xl text-white mb-2">{title}</h3>
                <p className="text-gray-400 font-space-grotesk mb-4">{description}</p>
                {children}
            </div>
        </motion.div>
    );
};

export default function DownloadPage() {
    const [copied, setCopied] = useState(false);
    const [activeStep, setActiveStep] = useState(1);
    const [detectedBrowser, setDetectedBrowser] = useState<BrowserInfo | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    // Browser detection function
    const detectBrowser = (): BrowserInfo => {
        const userAgent = navigator.userAgent;

        if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
            return {
                name: 'Chrome',
                icon: <FaChrome className="text-black text-sm" />,
                extensionUrl: 'chrome://extensions/',
                developerModeText: 'Developer mode',
                loadUnpackedText: 'Load unpacked',
                supported: true
            };
        } else if (userAgent.includes('Firefox')) {
            return {
                name: 'Firefox',
                icon: <FaFirefox className="text-black text-sm" />,
                extensionUrl: 'about:debugging#/runtime/this-firefox',
                developerModeText: 'Enable add-on debugging',
                loadUnpackedText: 'Load Temporary Add-on',
                supported: true
            };
        } else if (userAgent.includes('Edg')) {
            return {
                name: 'Edge',
                icon: <FaEdge className="text-black text-sm" />,
                extensionUrl: 'edge://extensions/',
                developerModeText: 'Developer mode',
                loadUnpackedText: 'Load unpacked',
                supported: true
            };
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            return {
                name: 'Safari',
                icon: <FaSafari className="text-black text-sm" />,
                extensionUrl: 'Safari → Preferences → Advanced → Show Develop menu',
                developerModeText: 'Develop → Show Extension Builder',
                loadUnpackedText: 'Add Extension',
                supported: false
            };
        } else {
            return {
                name: 'Unknown',
                icon: <FaChrome className="text-black text-sm" />,
                extensionUrl: 'chrome://extensions/',
                developerModeText: 'Developer mode',
                loadUnpackedText: 'Load unpacked',
                supported: true
            };
        }
    };

    useEffect(() => {
        const browser = detectBrowser();
        setDetectedBrowser(browser);
        setIsLoading(false);
    }, []);

    const steps = [
        {
            number: 1,
            title: "Download the Extension",
            description: "Get the latest version of Anubis Browser Extension"
        },
        {
            number: 2,
            title: "Extract the Files",
            description: "Unzip the downloaded file to a permanent location"
        },
        {
            number: 3,
            title: `Open ${detectedBrowser?.name} Extensions`,
            description: `Navigate to ${detectedBrowser?.name}'s extension management page`
        },
        {
            number: 4,
            title: detectedBrowser?.developerModeText || "Enable Developer Mode",
            description: "Turn on developer mode to install unpacked extensions"
        },
        {
            number: 5,
            title: detectedBrowser?.loadUnpackedText || "Load the Extension",
            description: "Select the extracted folder to install Anubis"
        },
        {
            number: 6,
            title: "Verify Installation",
            description: "Confirm the extension is working correctly"
        }
    ];

    if (isLoading) {
        return (
            <div className="min-h-screen bg-[#0a0a0a] flex items-center justify-center">
                <div className="text-center">
                    <div className="w-16 h-16 border-4 border-white/20 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-400 font-space-grotesk">Detecting your browser...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-[#0a0a0a]">
            {/* Header */}
            <div className="bg-[#0f0f0f] border-b border-white/10">
                <div className="max-w-7xl mx-auto px-4 md:px-8 lg:px-12 py-6">
                    <div className="flex items-center justify-between">
                        <Link href="/">
                            <motion.button
                                className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-200 group"
                                whileHover={{ x: -5 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <FaArrowLeft className="group-hover:scale-110 transition-transform duration-200" />
                                <span className="font-orbitron">Back to Home</span>
                            </motion.button>
                        </Link>

                        <div className="text-center">
                            <h1 className="font-orbitron font-bold text-2xl md:text-3xl text-white">
                                Download Anubis Extension
                            </h1>
                            <p className="text-gray-400 font-space-grotesk text-sm mt-1">
                                Install the browser extension for seamless trading
                            </p>
                        </div>

                        <div className="w-20"></div> {/* Spacer for centering */}
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-4xl mx-auto px-4 md:px-8 lg:px-12 py-12">
                {/* Browser Detection Banner */}
                <motion.div
                    className="bg-[#0f0f0f] p-6 rounded-2xl border border-white/20 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                >
                    <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            {detectedBrowser?.icon}
                        </div>
                        <div className="flex-1">
                            <h3 className="font-orbitron font-bold text-lg text-white">
                                Detected Browser: {detectedBrowser?.name}
                            </h3>
                            <p className="text-gray-400 font-space-grotesk text-sm">
                                {detectedBrowser?.supported
                                    ? "Great! Your browser is supported for installing the Anubis extension."
                                    : "Your browser may have limited support for unpacked extensions. We recommend using Chrome, Firefox, or Edge."
                                }
                            </p>
                        </div>
                    </div>
                </motion.div>

                {/* Download Section */}
                <motion.div
                    className="bg-[#0f0f0f] p-8 rounded-2xl border border-white/20 mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                >
                    <div className="text-center mb-8">
                        <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                            <FaDownload className="text-3xl" />
                        </div>
                        <h2 className="font-orbitron font-bold text-3xl text-white mb-4">
                            Ready to Download?
                        </h2>
                        <p className="text-gray-300 font-space-grotesk max-w-2xl mx-auto">
                            The Anubis Browser Extension is currently in beta. Download the latest version and follow the installation guide below.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Download Button */}
                        <a
                            href="/extension/anubis-extension.zip"
                            download
                            className="bg-white text-black p-6 rounded-xl font-orbitron font-semibold hover:bg-gray-100 transition-all duration-300 group inline-block"
                        >
                            <div className="flex items-center justify-center space-x-3">
                                <FaDownload className="text-2xl group-hover:scale-110 transition-transform duration-200" />
                                <div className="text-left">
                                    <div className="text-lg">Download Extension</div>
                                    <div className="text-sm opacity-80">Version 1.0.0 Beta</div>
                                </div>
                            </div>
                        </a>

                        {/* System Requirements */}
                        <div className="bg-[#0a0a0a] p-6 rounded-xl border border-white/10">
                            <h3 className="font-orbitron font-semibold text-white mb-3">System Requirements</h3>
                            <ul className="space-y-2 text-gray-300 font-space-grotesk text-sm">
                                <li className="flex items-center space-x-2">
                                    <FaCheckCircle className="text-white" />
                                    <span>{detectedBrowser?.name} {detectedBrowser?.name === 'Chrome' ? '90+' : 'latest version'}</span>
                                </li>
                                <li className="flex items-center space-x-2">
                                    <FaCheckCircle className="text-white" />
                                    <span>Windows 10/11, macOS, Linux</span>
                                </li>
                                <li className="flex items-center space-x-2">
                                    <FaCheckCircle className="text-white" />
                                    <span>MetaMask or similar wallet</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    {/* Warning */}
                    <div className="mt-6 bg-white/5 border border-white/20 rounded-xl p-4">
                        <div className="flex items-start space-x-3">
                            <FaExclamationTriangle className="text-white mt-1 flex-shrink-0" />
                            <div>
                                <h4 className="font-orbitron font-semibold text-white mb-1">Important Notice</h4>
                                <p className="text-gray-300 font-space-grotesk text-sm">
                                    This extension is not yet available on the {detectedBrowser?.name} Web Store. You'll need to install it as an unpacked extension using developer mode.
                                </p>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Installation Steps */}
                <div className="space-y-6">
                    <motion.div
                        className="text-center mb-8"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                        viewport={{ once: true, margin: "-100px" }}
                    >
                        <h2 className="font-orbitron font-bold text-3xl text-white mb-4">
                            Installation Guide for {detectedBrowser?.name}
                        </h2>
                        <p className="text-gray-400 font-space-grotesk">
                            Follow these steps to install the Anubis Browser Extension
                        </p>
                    </motion.div>

                    {/* Step 1: Download */}
                    <Step
                        number={1}
                        title="Download the Extension"
                        description="Get the latest version of Anubis Browser Extension"
                        isActive={activeStep === 1}
                    >
                        <div className="space-y-4">
                            <div className="bg-[#0a0a0a] p-4 rounded-lg border border-white/10">
                                <p className="text-gray-300 font-space-grotesk text-sm mb-3">
                                    Click the download button above to get the extension files. The download will be a ZIP file containing the extension.
                                </p>
                                <div className="flex items-center space-x-2 text-white font-space-grotesk text-sm">
                                    <FaDownload />
                                    <span>anubis-extension-v1.0.0.zip</span>
                                </div>
                            </div>
                        </div>
                    </Step>

                    {/* Step 2: Extract */}
                    <Step
                        number={2}
                        title="Extract the Files"
                        description="Unzip the downloaded file to a permanent location"
                        isActive={activeStep === 2}
                    >
                        <div className="space-y-4">
                            <div className="bg-[#0a0a0a] p-4 rounded-lg border border-white/10">
                                <p className="text-gray-300 font-space-grotesk text-sm mb-3">
                                    Extract the ZIP file to a permanent location on your computer. The folder should contain files like manifest.json, background.js, etc.
                                </p>
                                <div className="bg-[#1a1a1a] p-3 rounded border border-white/5 font-mono text-sm text-gray-300">
                                    📁 anubis-extension/
                                    <br />├── manifest.json
                                    <br />├── background.js
                                    <br />├── content.js
                                    <br />└── icons/
                                </div>
                            </div>
                        </div>
                    </Step>

                    {/* Step 3: Open Extensions */}
                    <Step
                        number={3}
                        title={`Open ${detectedBrowser?.name} Extensions`}
                        description={`Navigate to ${detectedBrowser?.name}'s extension management page`}
                        isActive={activeStep === 3}
                    >
                        <div className="space-y-4">
                            <div className="bg-[#0a0a0a] p-4 rounded-lg border border-white/10">
                                <p className="text-gray-300 font-space-grotesk text-sm mb-3">
                                    Open {detectedBrowser?.name} and navigate to the extensions page using one of these methods:
                                </p>
                                <div className="space-y-2">
                                    <div className="flex items-center space-x-2 text-gray-300 font-space-grotesk text-sm">
                                        <span className="w-2 h-2 bg-white rounded-full"></span>
                                        <span>Type <code className="bg-[#1a1a1a] px-2 py-1 rounded">{detectedBrowser?.extensionUrl}</code> in the address bar</span>
                                    </div>
                                    {detectedBrowser?.name === 'Chrome' && (
                                        <div className="flex items-center space-x-2 text-gray-300 font-space-grotesk text-sm">
                                            <span className="w-2 h-2 bg-white rounded-full"></span>
                                            <span>Or go to Menu → More Tools → Extensions</span>
                                        </div>
                                    )}
                                    {detectedBrowser?.name === 'Firefox' && (
                                        <div className="flex items-center space-x-2 text-gray-300 font-space-grotesk text-sm">
                                            <span className="w-2 h-2 bg-white rounded-full"></span>
                                            <span>Or go to Menu → Add-ons and themes → Extensions</span>
                                        </div>
                                    )}
                                    {detectedBrowser?.name === 'Edge' && (
                                        <div className="flex items-center space-x-2 text-gray-300 font-space-grotesk text-sm">
                                            <span className="w-2 h-2 bg-white rounded-full"></span>
                                            <span>Or go to Menu → Extensions → Manage extensions</span>
                                        </div>
                                    )}
                                </div>
                                <motion.button
                                    className="mt-3 flex items-center space-x-2 text-white hover:text-gray-300 transition-colors duration-200 group"
                                    whileHover={{ x: 5 }}
                                    onClick={() => copyToClipboard(detectedBrowser?.extensionUrl || '')}
                                >
                                    <FaCopy className="group-hover:scale-110 transition-transform duration-200" />
                                    <span className="font-space-grotesk text-sm">Copy URL</span>
                                </motion.button>
                            </div>
                        </div>
                    </Step>

                    {/* Step 4: Developer Mode */}
                    <Step
                        number={4}
                        title={detectedBrowser?.developerModeText || "Enable Developer Mode"}
                        description="Turn on developer mode to install unpacked extensions"
                        isActive={activeStep === 4}
                    >
                        <div className="space-y-4">
                            <div className="bg-[#0a0a0a] p-4 rounded-lg border border-white/10">
                                <p className="text-gray-300 font-space-grotesk text-sm mb-3">
                                    {detectedBrowser?.name === 'Firefox'
                                        ? "In the debugging page, click 'Load Temporary Add-on' and select the manifest.json file from your extracted folder."
                                        : `In the extensions page, toggle the "${detectedBrowser?.developerModeText}" switch in the top-right corner.`
                                    }
                                </p>
                                {detectedBrowser?.name !== 'Firefox' && (
                                    <div className="bg-[#1a1a1a] p-4 rounded border border-white/5">
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-300 font-space-grotesk text-sm">{detectedBrowser?.developerModeText}</span>
                                            <div className="w-12 h-6 bg-white rounded-full relative">
                                                <div className="w-5 h-5 bg-black rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </Step>

                    {/* Step 5: Load Extension */}
                    <Step
                        number={5}
                        title={detectedBrowser?.loadUnpackedText || "Load the Extension"}
                        description="Select the extracted folder to install Anubis"
                        isActive={activeStep === 5}
                    >
                        <div className="space-y-4">
                            <div className="bg-[#0a0a0a] p-4 rounded-lg border border-white/10">
                                <p className="text-gray-300 font-space-grotesk text-sm mb-3">
                                    {detectedBrowser?.name === 'Firefox'
                                        ? "Click 'Load Temporary Add-on' and select the manifest.json file from your extracted folder."
                                        : `Click the "${detectedBrowser?.loadUnpackedText}" button and select the folder where you extracted the extension files.`
                                    }
                                </p>
                                <div className="bg-[#1a1a1a] p-4 rounded border border-white/5">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-8 h-8 bg-white rounded flex items-center justify-center">
                                            {detectedBrowser?.icon}
                                        </div>
                                        <div>
                                            <div className="text-white font-space-grotesk text-sm">{detectedBrowser?.loadUnpackedText}</div>
                                            <div className="text-gray-400 font-space-grotesk text-xs">
                                                {detectedBrowser?.name === 'Firefox' ? 'Select manifest.json file' : 'Select extension directory'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Step>

                    {/* Step 6: Verify */}
                    <Step
                        number={6}
                        title="Verify Installation"
                        description="Confirm the extension is working correctly"
                        isActive={activeStep === 6}
                    >
                        <div className="space-y-4">
                            <div className="bg-[#0a0a0a] p-4 rounded-lg border border-white/10">
                                <p className="text-gray-300 font-space-grotesk text-sm mb-3">
                                    After installation, you should see the Anubis extension in your extensions list. Visit Dexscreener to start using it.
                                </p>
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-3 text-white">
                                        <FaCheckCircle />
                                        <span className="font-space-grotesk text-sm">Extension appears in {detectedBrowser?.name} toolbar</span>
                                    </div>
                                    <div className="flex items-center space-x-3 text-white">
                                        <FaCheckCircle />
                                        <span className="font-space-grotesk text-sm">No error messages in extensions page</span>
                                    </div>
                                    <div className="flex items-center space-x-3 text-white">
                                        <FaCheckCircle />
                                        <span className="font-space-grotesk text-sm">Extension icon is visible in browser</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Step>
                </div>

                {/* Next Steps */}
                <motion.div
                    className="mt-12 bg-[#0f0f0f] p-8 rounded-2xl border border-white/20"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <div className="text-center">
                        <h3 className="font-orbitron font-bold text-2xl text-white mb-4">
                            🎉 Installation Complete!
                        </h3>
                        <p className="text-gray-300 font-space-grotesk mb-6 max-w-2xl mx-auto">
                            Congratulations! You've successfully installed the Anubis Browser Extension on {detectedBrowser?.name}.
                            Now you can start trading directly from Dexscreener with unprecedented speed and efficiency.
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link href="https://dexscreener.com" target="_blank">
                                <motion.button
                                    className="bg-white text-black px-8 py-3 rounded-lg font-orbitron font-semibold hover:bg-gray-100 transition-all duration-300 flex items-center space-x-2 group"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <FaExternalLinkAlt className="group-hover:scale-110 transition-transform duration-200" />
                                    <span>Visit Dexscreener</span>
                                </motion.button>
                            </Link>

                            <Link href="/terminal">
                                <motion.button
                                    className="bg-[#0a0a0a] text-white border border-white/20 px-8 py-3 rounded-lg font-orbitron font-semibold hover:bg-[#0f0f0f] transition-all duration-300 group"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <span className="group-hover:translate-x-1 transition-transform duration-200">Open Terminal</span>
                                </motion.button>
                            </Link>
                        </div>
                    </div>
                </motion.div>

                {/* Troubleshooting */}
                <motion.div
                    className="mt-8 bg-[#0f0f0f] p-6 rounded-xl border border-white/10"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <h3 className="font-orbitron font-bold text-xl text-white mb-4">Need Help?</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-300 font-space-grotesk text-sm">
                        <div>
                            <h4 className="font-semibold text-white mb-2">Common Issues:</h4>
                            <ul className="space-y-1">
                                <li>• Extension not loading after installation</li>
                                <li>• Developer mode not working</li>
                                <li>• Extension disappears after restart</li>
                                {detectedBrowser?.name === 'Firefox' && (
                                    <li>• Temporary add-on expires after browser restart</li>
                                )}
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-semibold text-white mb-2">Support:</h4>
                            <ul className="space-y-1">
                                <li>• Check our documentation</li>
                                <li>• Join our Discord community</li>
                                <li>• Contact support team</li>
                            </ul>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
} 