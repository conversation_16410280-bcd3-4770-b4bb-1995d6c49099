import { MiddlewareHandler } from "hono";
import { createMiddleware } from "hono/factory";
import { verify } from "hono/jwt";
import { storageInstance } from "../../../storage";

const isLoggedIn = createMiddleware(async (c, next) => {
    const authHeader = c.req.header("Authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return c.json({
            error: "Unauthorized",
            message: "No token provided"
        }, 401);
    }

    try {
        const token = authHeader.split(" ")[1];
        const payload = await verify(token as string, process.env.JWT_SECRET ?? "") as { id: string };

        // Check if user exists in storage
        const user = await storageInstance.read(payload.id as string);
        if (!user) {
            return c.json({
                error: "Unauthorized",
                message: "User not found"
            }, 401);
        }

        console.log("User logged in:", user ? "User Account Present" : "Unknown");

        // Add user id
        c.set('user', payload.id);
        await next();
        return;
    } catch (error) {
        return c.json({
            error: "Unauthorized",
            message: "Token provided is invalid"
        }, 401);
    }
})

// const isLoggedIn: MiddlewareHandler = async (c, next) => {
//     const authHeader = c.req.header("Authorization");

//     if (!authHeader || !authHeader.startsWith("Bearer ")) {
//         return c.json({
//             error: "Unauthorized",
//             message: "No token provided"
//         }, 401);
//     }

//     try {
//         const token = authHeader.split(" ")[1];
//         const payload = await verify(token as string, process.env.JWT_SECRET ?? "") as { id: string };

//         // Check if user exists in storage
//         const user = await storageInstance.read(payload.id as string);
//         if (!user) {
//             return c.json({
//                 error: "Unauthorized",
//                 message: "User not found"
//             }, 401);
//         }

//         // Add user data to request context
//         c.set('user', user);
//         await next();
//         return;
//     } catch (error) {
//         return c.json({
//             error: "Unauthorized",
//             message: "Token provided is invalid"
//         }, 401);
//     }
// };

export default isLoggedIn;
