"use client"

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function Sniping() {
    const { token } = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (!token) {
            router.replace('/terminal/error');
        }
    }, [token, router]);

    if (!token) return null;    

    return (
        <div className="min-h-[calc(100vh-80px)] p-3 md:p-10">
            {/* Sniping content will go here */}
        </div>
    )
}