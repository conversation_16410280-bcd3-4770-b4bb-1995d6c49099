import {
  Bot,
  Context,
  SessionFlavor,
  session,
  enhanceStorage,
  NextFunction,
  Enhance,
} from "grammy";
import { Message } from "grammy/types";
import { Menu } from "@grammyjs/menu";
import { hydrateReply, parseMode } from "@grammyjs/parse-mode";
import type { ParseModeFlavor } from "@grammyjs/parse-mode";
import { ethers } from "ethers";
import {
  createWalletClient,
  getContract,
  http,
  parseAbi,
  parseUnits,
  Address,
  formatUnits,
  createPublicClient,
  Chain,
} from "viem";
import { privateKeyToAccount } from "viem/accounts";
import {
  approveTokenAllowance,
  getBalance,
  getTokenContractDecimal,
  getTokenContractInfo,
  transferNativeToken,
} from "./libs/token";
import { Pair, extractTokenFromPair, getSwapQuote } from "./libs/gecko";
import { ESupportedChains, getConfig, odosSupportedChains, supportedChains } from "./config";
import {
  convertToMaxDecimals,
  convertToUSD,
  escapeTelegramMarkdown,
} from "./libs/common";
import { assembleQuote, quotePath } from "./libs/odods";
import { get0xQuote } from "./libs/0x";
import { storageInstance } from "./storage";

// Helper function to show the main menu
async function showMainMenu(ctx: BotContext) {
  const messageLines = [
    `${ctx.me.first_name}!`,
    "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
    "",
    "*🚀 Main Menu*",
    "Select an option to get started or paste a token address.",
  ];

  // Delete the current message
  const [chatId, messageId] = ctx.match?.split("::") ?? [];
  if (chatId && messageId) {
    await ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => { });
  }

  // Send the main menu
  return send(ctx, messageLines, {
    reply_markup: mainMenu,
    parse_mode: "MarkdownV2",
  });
}

type ArgsType<T> = T extends (...args: infer Args) => any ? Args : never;

interface ReferralOptions {
  enabled: boolean;
  onStart: (ctx: BotContext) => Promise<void>;
  onReferral: (ctx: BotContext, id: number) => Promise<void>;
  userExists: (ctx: BotContext, id: number) => Promise<boolean>;
}

interface SessionWallet {
  address: Address;
  phrase?: string;
  privateKey: Address;
}

interface ReferralData {
  id: number;
  isValid: boolean;
  timestamp: number;
}

interface SnipeData {
  chainId: ESupportedChains;
  token: Address;
  amount: number;
  timestamp: number;
}

export interface SessionData {
  walletId: number;
  referredBy?: string;
  msgId?: number;
  chatId?: number;
  username?: string;
  token?: string;
  otp?: string;
  otpTTL?: number;
  data: {
    tokens: Address[];
    tradeValueInETH: Record<ESupportedChains, number>;
    referrals: ReferralData[];
    referralRewardInETH: Record<ESupportedChains, number>;
    snipeValueInETH: Record<ESupportedChains, number>;
    snipes: SnipeData[];
  };
  settings: {
    chainId: ESupportedChains;
    slippage: number;
    gasPrice: number;
    wallets: SessionWallet[];
    viewedOnboarding?: boolean;
  };
  adminOnly?: {
    users: number[];
    admins: number[];
  };
  withdrawalAmount?: number | string;
  withdrawalAddress?: string | `0x${string}`;
}

interface MemTexts {
  text: string;
  menu: Menu;
}

interface BotContext
  extends ParseModeFlavor<Context>,
  SessionFlavor<SessionData> {
  match: string;
}

// Create a bot object
const bot = new Bot<BotContext>(process.env.BOT_TOKEN ?? "");



const memTextStorage = new Map<string, MemTexts[]>();

const defaultNumByChain: Record<ESupportedChains, number> = {
  [ESupportedChains.Core]: 0,
  [ESupportedChains.Base]: 0,
  [ESupportedChains.ZkSync]: 0,
  [ESupportedChains.Blast]: 0,
  [ESupportedChains.Sonic]: 0,
  [ESupportedChains.Avax]: 0,
  [ESupportedChains.Bera]: 0,
  [ESupportedChains.Unichain]: 0,
};

const storage = enhanceStorage<SessionData>({
  storage: storageInstance,
  migrations: {
    2: (d: SessionData): SessionData => ({
      ...d,
      settings: {
        ...d.settings,
        gasPrice: 30 * 1e9,
      },
    }),
    3: (d: SessionData): SessionData => ({
      ...d,
      data: {
        ...d.data,
        // @ts-expect-error previous types
        tradeValueInETH: 0,
        referrals: [],
      },
    }),
    4: (d: SessionData): SessionData => ({
      ...d,
      data: {
        ...d.data,
        // @ts-expect-error previous types
        referralRewardInETH: 0,
      },
    }),
    5: (d: SessionData): SessionData => ({
      ...d,
      data: {
        ...d.data,
        // @ts-expect-error previous types
        snipeValueInETH: 0,
        snipes: [],
      },
    }),
    6: (d: SessionData): SessionData => {
      // @ts-expect-error deleted field
      d.data.tokenData = undefined;
      return {
        ...d,
        data: {
          ...d.data,
          tradeValueInETH: {
            ...defaultNumByChain,
            [d.settings.chainId]: d.data.tradeValueInETH,
          },
          referralRewardInETH: {
            ...defaultNumByChain,
            [d.settings.chainId]: d.data.referralRewardInETH,
          },
          snipeValueInETH: {
            ...defaultNumByChain,
            [d.settings.chainId]: d.data.snipeValueInETH,
          },
        },
      };
    },
  },
});

function getWallet(ctx: BotContext): SessionWallet {
  const wallet = ctx.session.settings.wallets[ctx.session.walletId];
  if (!wallet) {
    ctx.session.walletId = 0;
    return ctx.session.settings.wallets[ctx.session.walletId]!;
  }
  return wallet;
}

// Stores data per user.
function getSessionKey(ctx: Context): string | undefined {
  // Give every user their personal session storage
  // (will be shared across groups and in their private chat)
  return ctx.from?.id.toString();
}

function referrals(options: ReferralOptions) {
  return async (ctx: BotContext, next: NextFunction) => {
    if (!options.enabled) return next();
    if (!ctx.hasChatType("private")) return next();
    if (!ctx.hasCommand("start")) return next();

    await options.onStart(ctx);
    const isUserRegistered = await options.userExists(ctx, ctx.from?.id ?? 0);
    if (isUserRegistered) {
      const referrerId = parseInt(ctx.msg?.text?.split(" ")[1] || "");
      if (!isNaN(referrerId)) {
        const isReferrerExists = await options.userExists(ctx, referrerId);
        if (isReferrerExists) {
          await options.onReferral(ctx, referrerId);
        }
      }
      return next();
    }
  };
}

const ROUTE_ABI = parseAbi([
  "function processRoute(address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address to, bytes routeCode) payable returns (uint256 amountOut)",
  "function transferValueAndprocessRoute(address transferValueTo, uint256 value, address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address to, bytes routeCode) payable returns (uint256 amountOut)",
]);

async function routeReferralReward(
  ctx: BotContext,
  tax: bigint
): Promise<
  [SessionWallet | undefined, bigint, bigint, Enhance<SessionData> | undefined]
> {
  const referrerId = ctx.session.referredBy;

  if (referrerId) {
    const referrerUser = await storageInstance.read(referrerId);
    const [mainWallet] = referrerUser.__d.settings.wallets;
    const referTime = referrerUser.__d.data.referrals.find(
      (v) => v.id === ctx.from?.id
    )?.timestamp;

    const now = Date.now();
    const firstMonth = 30 * 24 * 60 * 60 * 1000;
    const secondMonth = 2 * firstMonth;
    const rewardRatio =
      now - referTime! < firstMonth
        ? 0.3
        : now - referTime! < secondMonth
          ? 0.2
          : 0.1;

    const reward = tax / parseUnits(rewardRatio.toString(), 18);
    return [mainWallet!, tax - reward, reward, referrerUser];
  }
  return [undefined, tax, parseUnits("0", 18), undefined];
}

async function payReferralReward(
  ctx: BotContext,
  reward: bigint,
  referrerUser: Enhance<SessionData>
) {
  const referrerId = ctx.session.referredBy;
  if (!referrerId) return;
  const rewardIneth = Number(formatUnits(reward, 18));
  referrerUser.__d.data.referralRewardInETH = {
    ...referrerUser.__d.data.referralRewardInETH,
    [ctx.session.settings.chainId]:
      referrerUser.__d.data.referralRewardInETH[ctx.session.settings.chainId] +
      rewardIneth,
  };
  await storageInstance.write(referrerId, referrerUser);
}

async function routeTokenAction(ctx: BotContext) {
  if (!ctx.message?.text) return;
  if (!ctx.session.data.tokens.includes(ctx.message.text as Address)) {
    ctx.session.data.tokens.push(ctx.message.text as Address);
    ctx.session.token = ctx.session.data.tokens.length.toString();
  } else {
    ctx.session.token = ctx.session.data.tokens
      .findIndex((v) => !!v.match(ctx.message?.text ?? ""))
      .toString();
  }

  const msg = await send(ctx, ["🔍 Searching for Token"]);
  if (msg !== true) {
    sendTokenInfo(
      ctx,
      {
        chat: { id: msg?.chat?.id },
        message_id: msg?.message_id ?? 0,
      } as Message,
      ctx.message?.text as Address
    );
  }
}

async function routeSwitchWallet(ctx: BotContext) {
  const walletAddress = ctx.message?.text ?? "";

  ctx.session.walletId = ctx.session.settings.wallets.findIndex(
    (w) => w?.address === walletAddress
  );
  await send(ctx, ["🔑 Switched Wallet to " + walletAddress], {
    reply_markup: walletsMenu,
  });
}

async function routeImportWallet(ctx: BotContext) {
  const messagePrivateKey = ctx.message?.text;
  if (!messagePrivateKey) {
    await send(ctx, ["Please enter a valid wallet privateKey"]);
    return;
  }
  try {
    const { address, mnemonic, privateKey } = new ethers.Wallet(
      messagePrivateKey.startsWith("0x")
        ? messagePrivateKey
        : `0x${messagePrivateKey}`
    );

    const walletInfo = {
      address: address as Address,
      privateKey: privateKey as Address,
      phrase: mnemonic?.phrase ?? undefined,
    };

    const walletIndex = ctx.session.settings.wallets.findIndex(
      (w) => w?.address === walletInfo.address
    );

    if (walletIndex !== -1) {
      ctx.session.settings.wallets[walletIndex] = walletInfo;
    } else {
      ctx.session.settings.wallets.push(walletInfo);
    }

    await send(ctx, ["🔑 Wallet Imported"]);
  } catch (error) {
    console.error(error);
    await send(ctx, ["Please enter a valid wallet privateKey"]);
  }
}

async function routeSnipeToken(ctx: BotContext) {
  const amount = Number(ctx.message?.text);
  if (isNaN(amount) || amount <= 0) {
    await send(ctx, ["The amount to snipe must be a number greater than 0"]);
    return;
  }
  const token = ctx.session.data.tokens[Number(ctx.session.token)];
  if (token) {
    ctx.session.data.snipeValueInETH = {
      ...ctx.session.data.snipeValueInETH,
      [ctx.session.settings.chainId]:
        ctx.session.data.snipeValueInETH[ctx.session.settings.chainId] + amount,
    };
    ctx.session.data.snipes.push({
      chainId: ctx.session.settings.chainId,
      token,
      amount,
      timestamp: Date.now(),
    });
    await send(ctx, ["🎯 Snipe Scheduled"]);
  }
}

async function routeWithdrawToken(ctx: BotContext) {
  const amount = Number(ctx.message?.text);
  if (isNaN(amount) || amount <= 0) {
    await send(ctx, ["The amount to withdraw must be a number greater than 0"]);
    return;
  }
  const tokenId = getTokenIdPayload(ctx);
  const token = ctx.session.data.tokens[Number(tokenId)];
  if (token) {
    const wallet = getWallet(ctx);
  }
}

const SNIPE_HANDLERS = new Map<string, () => void>();

async function dispatchSnipes(ctx: BotContext) {
  const snipeData = ctx.session.data.snipes;
  const taxAmount = getConfig(ctx.session.settings.chainId).tax.snipe;

  for (const snipe of snipeData) {
    let snipeInterval;
    let snipeKey = `${ctx.from?.id}::${snipe.token}::${snipe.timestamp}`;
    let snipeHandler = SNIPE_HANDLERS.get(snipeKey);

    if (!snipeHandler) {
      SNIPE_HANDLERS.set(snipeKey, async () => {
        const pairData = await extractTokenFromPair(
          ctx.session.settings.chainId,
          snipe.token
        );
        if (pairData) {
          await swapTokenFor(
            ctx,
            getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
            snipe.token,
            pairData,
            snipe.amount,
            taxAmount
          );
          SNIPE_HANDLERS.delete(snipe.token);
          clearInterval(snipeInterval!);
        }
      });
      snipeHandler = SNIPE_HANDLERS.get(snipeKey);
    }
    snipeInterval = setInterval(snipeHandler!, 1000);
  }
}

async function swapTokenFor(
  ctx: BotContext,
  tokenIn: Address,
  tokenOut: Address,
  pairData: Pair,
  amount: number,
  taxAmount = getConfig(ctx.session.settings.chainId).tax.default
) {
  const userConfig = getConfig(ctx.session.settings.chainId);
  const isBeraOrUnichain =
    (ctx.session.settings.chainId === 80094 || ctx.session.settings.chainId === 130) &&
    (userConfig.chain.name.toLowerCase() === 'bera' || userConfig.chain.name.toLowerCase() === 'unichain');

  const tax = amount * taxAmount;

  const { privateKey, address } = getWallet(ctx);
  const isNativeToken = !tokenOut.match(userConfig.nativeCurrencyAddress);
  if (tokenIn === tokenOut) {
    throw new Error("attempt to trade the same token");
  }

  if (!pairData) {
    throw new Error("could not get quote");
  }

  const account = privateKeyToAccount(privateKey);
  const wallet = createWalletClient({
    account,
    chain: userConfig.chain,
    transport: http(),
  });
  const client = createPublicClient({
    chain: userConfig.chain,
    transport: http(),
  });

  let amountOut: bigint | undefined;
  let txHash: Address | undefined = undefined;

  const tokenDecimals = await getTokenContractDecimal(
    tokenIn,
    ctx.session.settings.chainId
  );
  let taxValue = parseUnits(tax.toString(), tokenDecimals);
  let [
    referredWallet,
    taxRem,
    referredReward,
    referredUser,
  ] = await routeReferralReward(ctx, taxValue);
  const bigInputAmount = parseUnits(
    (isNativeToken ? amount - tax : amount).toString(),
    tokenDecimals
  );
  const inputAmount = formatUnits(bigInputAmount, 0);

  if (isBeraOrUnichain) {
    const orderQuote: any = await get0xQuote({
      chainId: ctx.session.settings.chainId,
      inputToken: tokenIn,
      amount: inputAmount,
      outputToken: tokenOut,
      userAddr: address
    })

    if ((orderQuote as any).message) {
      throw new Error((orderQuote as any).message)
    }

    txHash = await wallet.sendTransaction({
      to: orderQuote.transaction.to,
      value: parseUnits(orderQuote.transaction.value, 0),
      data: orderQuote.transaction.data,
      gas: parseUnits(orderQuote.transaction.gas.toString(), 0),
      gasPrice: parseUnits(orderQuote.transaction.gasPrice.toString(), 0),
      chainId: ctx.session.settings.chainId,
    });


  }

  if (odosSupportedChains.includes(userConfig.chain.id) && !isBeraOrUnichain) {
    const quote = await quotePath({
      chainId: userConfig.chain.id,
      inputToken: tokenIn,
      amount: inputAmount,
      outputToken: tokenOut,
      userAddr: address,
      slippageLimitPercent: ctx.session.settings.slippage * 10,
    });
    console.log("Retrieved Quote", quote);
    if (!quote?.pathId) {
      throw new Error("trade not possible");
    }
    const { transaction, outputTokens, simulation } = await assembleQuote(
      address,
      quote.pathId,
      isNativeToken
    );
    console.log("Transaction Simulation", simulation)
    if (simulation) {
      if (simulation.gasEstimate === -1) {
        console.error(simulation.simulationError?.errorMessage);
        const sanitizedErrorMessage = simulation.simulationError?.errorMessage?.replace(/'/g, '"') || '{}';
        const errorMessage = JSON.parse(sanitizedErrorMessage);
        throw new Error(errorMessage.message);
      }
      if (simulation.isSuccess === false) {
        const sanitizedErrorMessage = simulation.simulationError?.errorMessage?.replace(/'/g, '"') || '{}';
        const errorMessage = JSON.parse(sanitizedErrorMessage);
        throw new Error(errorMessage.message);
      }
    }
    if (!isNativeToken) {
      const { amount } = outputTokens[0];
      amountOut = parseUnits(amount.toString(), 18);
      const taxAmountOut = Number(amount) * taxAmount;

      taxValue = amountOut / parseUnits(taxAmountOut.toString(), 18);
      [
        referredWallet,
        taxRem,
        referredReward,
        referredUser,
      ] = await routeReferralReward(ctx, taxValue);

      await approveTokenAllowance(
        tokenIn,
        transaction.to,
        bigInputAmount,
        wallet,
        ctx.session.settings.chainId
      );
    }

    txHash = await wallet.sendTransaction({
      to: transaction.to,
      value: parseUnits(transaction.value, 0),
      data: transaction.data,
      gas: parseUnits(transaction.gas.toString(), 0),
      gasPrice: parseUnits(transaction.gasPrice.toString(), 0),
      nonce: transaction.nonce + 1,
      chainId: transaction.chainId,
    });
  } else if (pairData.dexId === "sushiswap" || isBeraOrUnichain) {
    const { route, tokenFrom } = await getSwapQuote(
      tokenIn,
      tokenOut,
      isNativeToken ? amount - tax : amount,
      address,
      ctx.session.settings.chainId,
      ctx.session.settings.gasPrice / 1e9,
      ctx.session.settings.slippage * 10
    );

    const contract = getContract({
      address: route.address,
      abi: ROUTE_ABI,
      client: { wallet },
    });

    const routeArgs: [Address, bigint, Address, bigint, Address, Address] = [
      route.args.tokenIn,
      parseUnits(route.args.amountIn, 0),
      route.args.tokenOut,
      parseUnits(route.args.amountOutMin, 0),
      route.args.to,
      route.args.routeCode,
    ];

    if (isNativeToken) {
      // Process the route and transfer the tax to the tax address
      try {
        // if (route.poolType === "Classic") {
        //   await approveTokenAllowance(
        //     tokenFrom.address,
        //     route.address,
        //     parseUnits(amount.toString(), tokenFrom.decimals),
        //     wallet,
        //     ctx.session.settings.chainId
        //   );
        // }
        txHash = await contract.write.transferValueAndprocessRoute(
          [userConfig.tax.address, taxRem, ...routeArgs],
          {
            value: route.args.value
              ? parseUnits(route.args.value, 0)
              : undefined,
            gas: parseUnits((route.gasSpent * 2).toString(), 0),
          }
        );
      } catch (error) {
        throw new Error(
          // @ts-ignore - conflicting types
          error?.details ?? error?.message ?? "swap failed"
        );
      }
    } else {
      await approveTokenAllowance(
        tokenFrom.address,
        route.address,
        parseUnits(route.args.amountIn, 0),
        wallet,
        ctx.session.settings.chainId
      );
      amountOut = parseUnits(route.args.amountOutMin, 0);
      taxValue = amountOut / parseUnits(taxAmount.toString(), 18);
      [
        referredWallet,
        taxRem,
        referredReward,
        referredUser,
      ] = await routeReferralReward(ctx, taxValue);

      // Process the route
      txHash = await contract.write.processRoute(routeArgs);
    }
  }
  // else if (pairData.dexId === "uniswap") {
  //   const quote = await getUniswapQuote(
  //     tokenIn === userConfig.nativeCurrencyAddress
  //       ? ("ETH" as Address)
  //       : tokenIn,
  //     tokenOut,
  //     formatUnits(bigInputAmount, 0),
  //     address,
  //     ctx.session.settings.chainId
  //   );

  //   if (!quote) {
  //     throw new Error("Route not found");
  //   }

  //   const { spender } = quote?.permitData?.values ?? {};

  //   if (spender !== quote.methodParameters.to) {
  //     throw new Error("Invalid spender");
  //   }

  //   txHash = await wallet.sendTransaction({
  //     to: quote.methodParameters.to,
  //     data: quote.methodParameters.calldata,
  //     value: fromHex(quote.methodParameters.value, "bigint"),
  //     gas: parseUnits(quote.gasUseEstimate, 0),
  //     gasPrice: parseUnits(quote.gasPriceWei, 0),
  //   });
  // }

  if (!txHash) throw new Error("trade transaction failed");
  // const receipt = await client.waitForTransactionReceipt({
  //   hash: txHash,
  //   timeout: 120_000, // (in milliseconds) 2 minutes
  // });
  // if (receipt.status === "reverted") {
  //   console.log(receipt);
  //   throw new Error(
  //     `swap reverted by the contract. View on [Scan](${userConfig.chain.blockExplorers?.default.url}tx/${txHash})`
  //   );
  // } else {
  // send tax
  wallet.sendTransaction({
    to: userConfig.tax.address,
    value: taxValue,
  });

  // send referral reward
  if (referredReward && referredWallet && referredUser) {
    await payReferralReward(ctx, referredReward, referredUser);
    wallet.sendTransaction({
      to: referredWallet.address,
      value: referredReward,
    });
  }
  let tradeValue = amount;
  if (amountOut) {
    tradeValue = Number(formatUnits(amountOut, 18));
  }
  ctx.session.data.tradeValueInETH = {
    ...ctx.session.data.tradeValueInETH,
    [ctx.session.settings.chainId]:
      ctx.session.data.tradeValueInETH[ctx.session.settings.chainId] +
      tradeValue,
  };
  // }

  return pairData;
}

function getChatIdPayload(ctx: BotContext) {
  const messageId =
    ctx.message?.reply_to_message?.message_id ?? ctx.message?.message_id;
  return `${ctx.chat?.id}::${(messageId ?? 0) + 1}`;
}

function getClosePayload(ctx: BotContext) {
  return `${ctx.chat?.id}::${ctx.message?.message_id}`;
}

function getTokenIdPayload(ctx: BotContext) {
  const token = ctx.message?.reply_to_message?.text ?? ctx.message?.text;
  const tokenId = ctx.session.data.tokens.findIndex((v) => v === token);
  if (tokenId === -1) {
    return `${ctx.session.token}`;
  }
  return `${tokenId}`;
}

function getTokenContextPayload(ctx: BotContext) {
  const chatIdPayload = getChatIdPayload(ctx);
  const tokenIdPayload = getTokenIdPayload(ctx);
  return `${chatIdPayload}::${tokenIdPayload}`;
}

function deleteMessage(ctx: BotContext) {
  const [chatId, messageId] = ctx.match?.split("::") ?? [];
  if (chatId && messageId) {
    ctx.api.deleteMessage(chatId, Number(messageId)).catch(console.error);
  }
  return ctx.deleteMessage();
}

async function send(
  ctx: BotContext,
  messageLines: string[],
  options?: ArgsType<BotContext["reply"]>[1],
  match?: string
) {
  const text = escapeTelegramMarkdown(
    messageLines.length > 1 ? messageLines.join("\n") : messageLines[0]!
  );
  if (ctx.match || match) {
    const [chatId, messageId] = (match ?? ctx.match).split("::");
    await ctx.api
      .deleteMessage(Number(chatId), Number(messageId))
      .catch(() => { });
    return ctx.replyWithMarkdownV2(text, options).catch(async (err) => {
      if (!err.description?.includes("message is not modified")) {
        return ctx.replyWithMarkdownV2(text, options);
      }
      return true;
    });
  } else {
    return ctx.replyWithMarkdownV2(text, options);
  }
}

async function sendTokenInfo(
  ctx: BotContext,
  msg: Message,
  tokenAddress: Address
) {
  const userConfig = getConfig(ctx.session.settings.chainId);
  const tokenPair = await extractTokenFromPair(
    ctx.session.settings.chainId,
    tokenAddress
    // userConfig.nativeCurrencyAddress
  );
  const nativeBalance = await getBalance(
    getWallet(ctx).address,
    userConfig.nativeCurrencyAddress,
    ctx.session.settings.chainId
  );
  if (!tokenPair) {
    const tokenContractInfo = await getTokenContractInfo(
      tokenAddress,
      ctx.session.settings.chainId
    );
    if (!tokenContractInfo) {
      return send(
        ctx,
        [
          `😓 Please paste a valid token contract address on ${userConfig.chain.name}`,
          "You can also change to one of our supported networks by clicking /settings",
        ],
        {},
        `${msg.chat.id}::${msg.message_id}`
      );
    }
    const balance = await getBalance(
      getWallet(ctx).address,
      tokenAddress,
      ctx.session.settings.chainId
    );
    return send(
      ctx,
      [
        `📊 *${tokenContractInfo.name} (${tokenContractInfo.symbol})*`,
        `\`${tokenAddress}\` `,
        "",
        `💼 Balance: ${balance} ${tokenContractInfo.symbol}`,
        `💼 Wallet: ${nativeBalance} ${userConfig.chain.nativeCurrency.symbol}`,
        "",
        "N/B: This token is not yet listed on our supported Dexes. You can snipe it automatically once it's listed at 20% fees 🚀",
        "",
        `🔗 View on [Scan](${userConfig.chain.blockExplorers?.default.url}/address/${tokenAddress})`,
      ],
      {
        reply_markup: contractTokenMenu,
        parse_mode: "MarkdownV2",
      },
      `${msg.chat.id}::${msg.message_id}`
    );
  }
  const balance = await getBalance(
    getWallet(ctx).address,
    tokenPair.quoteToken.address,
    ctx.session.settings.chainId
  );
  const messageLines = [
    `📊 *${tokenPair.quoteToken.name} (${tokenPair.quoteToken.symbol})* on ${userConfig.chain.name}`,
    `\`${tokenAddress}\` `,
    "",
    `💼 Balance: ${balance} ${tokenPair.quoteToken.symbol}`,
    `💼 Wallet: ${nativeBalance} ${userConfig.chain.nativeCurrency.symbol}`,
    Number(balance)
      ? [
        "",
        `Profit/Loss: 5m: ${tokenPair.priceChange.m5}% | 1h: ${tokenPair.priceChange.h1}% | 6h: ${tokenPair.priceChange.h6}% | 24h: ${tokenPair.priceChange.h24}%`,
        "",
      ]
      : [""],
    // Handle case when priceUsd is undefined but priceNative is available
    `💲 Price: \`${tokenPair.priceUsd !== undefined ? convertToUSD(tokenPair.priceUsd) :
      (tokenPair.priceNative !== undefined ? `$${parseFloat(tokenPair.priceNative).toFixed(6)}` : "Price unavailable")}\``,
    `💰 FDV: \`${tokenPair.fdv !== undefined ? convertToUSD(tokenPair.fdv) : "Not available"}\``,
    `💧 Liquidity: ${tokenPair.liquidity?.usd !== undefined ? convertToUSD(tokenPair.liquidity?.usd) : "Not available"}`,
    `📈 24H Volume: ${tokenPair.volume?.h24 !== undefined ? convertToUSD(tokenPair.volume.h24) : "Not available"}`,
    "",
    `🔗 View on [Scan](${userConfig.chain.blockExplorers?.default.url}/address/${tokenPair.quoteToken.address}) | [DexScreener](${tokenPair.url})`,
  ].flat();
  return send(
    ctx,
    messageLines,
    {
      reply_markup: tokenMenu,
      parse_mode: "MarkdownV2",
    },
    `${msg.chat.id}::${msg.message_id}`
  );
}

async function handleBuyToken(ctx: BotContext, amount: number, match: string) {
  const [_, _m, tokenId] = match.split("::");
  const tokenAddress = ctx.session.data.tokens[Number(tokenId)];
  if (!tokenAddress) {
    ctx.reply("Try again");
    return;
  }

  const pairData = await extractTokenFromPair(
    ctx.session.settings.chainId,
    tokenAddress
  );

  const nativeSymbol = getConfig(ctx.session.settings.chainId).chain
    .nativeCurrency.symbol;

  await send(
    ctx,
    [`🔄 Buying ${amount} ${nativeSymbol} of ${pairData.baseToken.name}`],
    {
      reply_markup: undefined,
    }
  );

  try {
    await swapTokenFor(
      ctx,
      getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
      tokenAddress,
      pairData,
      amount
    );
    const messageLines = [
      `💰 Bought ${amount} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
      }`,
      "",
      `🔗 View on [Scan](${getConfig(ctx.session.settings.chainId).chain.blockExplorers?.default
        .url
      }/address/${tokenAddress})`,
    ];
    await send(ctx, messageLines, {
      reply_markup: dismissMenu,
      parse_mode: "MarkdownV2",
    });
  } catch (error) {
    console.log(error);
    await send(
      ctx,
      [
        `🔄 Could not buy ${amount} ${nativeSymbol} of tokens`,
        `The trade failed due to ${
        // @ts-ignore - conflicting types
        error?.details ?? error?.message ?? "unknown error"
        }`,
      ],
      {
        reply_markup: dismissMenu,
        parse_mode: "MarkdownV2",
      }
    );
  }
}

async function handleSellToken(
  ctx: BotContext,
  percent: number,
  match: string
) {
  const [_, _m, tokenId] = match.split("::");
  const tokenAddress = ctx.session.data.tokens[Number(tokenId)];
  if (!tokenAddress) {
    ctx.reply("Try again");
    return;
  }
  await send(ctx, [`🔄 Selling ${percent}% of token`], {
    reply_markup: undefined,
  });
  const tokenBalance = await getBalance(
    getWallet(ctx).address,
    tokenAddress,
    ctx.session.settings.chainId
  );
  const amount = (Number(tokenBalance) * percent) / 100;

  const pairData = await extractTokenFromPair(
    ctx.session.settings.chainId,
    tokenAddress
  );
  await swapTokenFor(
    ctx,
    tokenAddress,
    getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
    pairData,
    amount
  )
    .then((poolInfo) => {
      const messageLines = [
        `💰 Sold ${amount} ${poolInfo.quoteToken.symbol}`,
        "",
        `🔗 View on [Scan](${getConfig(ctx.session.settings.chainId).chain.blockExplorers?.default
          .url
        }/address/${tokenAddress})`,
      ];
      send(
        ctx,
        messageLines,
        {
          reply_markup: dismissMenu,
          parse_mode: "MarkdownV2",
        },
        match
      );
    })
    .catch((error) => {
      console.log(error);
      send(
        ctx,
        [
          `🔄 Could not sell ${amount} tokens`,
          `The trade failed due to ${
          // @ts-ignore - conflicting types
          error?.details ?? error?.message ?? "unknown error"
          }`,
        ],
        {
          reply_markup: dismissMenu,
          parse_mode: "MarkdownV2",
        }
      );
    });
}

async function handleManualBuy(ctx: BotContext) {
  const messageLines = [
    "🎯 Manual Buy/AutoSnipe",
    "",
    "Paste/Enter a token address to get started.",
    `e.g \`${getConfig(ctx.session.settings.chainId).nativeCurrencyAddress}\``,
  ];
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
  });
}

async function handleSettings(ctx: BotContext) {
  const messageLines = ["🔧 *Anubis Trading Bot Settings*"];
  await send(ctx, messageLines, {
    reply_markup: settingsMenu,
    parse_mode: "MarkdownV2",
  });
}

async function handleWallets(ctx: BotContext) {
  // Get wallet balances
  const walletInfos = await Promise.all(
    ctx.session.settings.wallets.map(async (wallet, index) => {
      if (!wallet) return null;
      const balance = await getBalance(
        wallet.address,
        getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
        ctx.session.settings.chainId
      );
      return {
        index,
        address: wallet.address,
        balance,
        isActive: ctx.session.walletId === index
      };
    })
  );

  // Filter out null values
  const validWallets = walletInfos.filter(w => w !== null) as {
    index: number;
    address: Address;
    balance: string;
    isActive: boolean;
  }[];

  // Create message lines
  const messageLines = [
    "🔒 *Wallet Management*",
    "",
    "Select a wallet to manage or create a new one:",
    ""
  ];

  // Create inline keyboard with wallet buttons
  const inlineKeyboard: Array<Array<{ text: string; callback_data: string }>> = [];

  // Add wallet buttons
  validWallets.forEach(wallet => {
    const symbol = getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol;
    const shortAddress = `${wallet.address.substring(0, 6)}...${wallet.address.substring(wallet.address.length - 4)}`;

    // Add wallet info to message
    messageLines.push(`${wallet.isActive ? "🟢" : "🔴"} Wallet ${wallet.index + 1}: ${shortAddress} - ${wallet.balance} ${symbol}`);

    // Add button for this wallet
    inlineKeyboard.push([{
      text: `${wallet.isActive ? "✅" : ""} Manage Wallet ${wallet.index + 1}`,
      callback_data: `wallet_action:manage:${wallet.index}`
    }]);
  });

  // Add a button to generate a new wallet
  inlineKeyboard.push([{
    text: "🔑 Generate New Wallet",
    callback_data: "wallet_action:generate_new"
  }]);

  // Add import wallet button
  inlineKeyboard.push([{
    text: "📥 Import Wallet",
    callback_data: "main_menu_action:import"
  }]);

  // Add main menu button
  inlineKeyboard.push([{
    text: "🏠 Main Menu",
    callback_data: "main_menu_action:main_menu"
  }]);

  // Send the message with inline keyboard
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: inlineKeyboard
    }
  });
}

async function handleImportWallet(ctx: BotContext) {
  const messageLines = [
    "🔒 Import Wallet",
    "",
    "Enter your wallet private key:",
  ];
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

async function handleTokenTransfer(ctx: BotContext) {
  const messageLines = [
    "🔁 Token Transfer",
    "",
    "Enter the recipient's address:",
  ];
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

async function handleReferrals(ctx: BotContext) {
  const reward = ctx.session.data.referralRewardInETH[ctx.session.settings.chainId] ?? 0;
  const messageLines = [
    "Referrals",
    "",
    `Your Reflink: \`https://t.me/${ctx.me.username}?start=${ctx.from?.id}\``,
    "",
    `Referrals: ${ctx.session.data.referrals.length}`,
    `Total Rewards: ${reward} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol}`,
    "",
    `Rewards are updated at least every 24 hours and rewards are automatically deposited to your ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol} balance`,
    "Refer your friends and earn 30% of their fees in the first month, 20% in the second and 10% forever!",
  ];
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
  });
}

async function handleCustomSell(ctx: BotContext) {
  const messageLines = [
    "💲 Custom Sell",
    "",
    "Enter the amount (in percentage, e.g 75) you want to sell:",
  ];
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

async function handleCustomBuy(ctx: BotContext) {
  const messageLines = [
    "💲 Custom Buy",
    "",
    `Enter the amount (in ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
    }) you want to buy:`,
  ];
  ctx.session.token = getTokenIdPayload(ctx);
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

async function handlePortfolio(ctx: BotContext) {
  console.log("Loading Up Portfolio");

  // Get all token balances and info
  const tokenInfos = await Promise.all(
    ctx.session.data.tokens.map(async (token) => {
      try {
        const tokenMeta = await extractTokenFromPair(
          ctx.session.settings.chainId,
          token
        );

        console.log(tokenMeta);

        if (!tokenMeta) {
          const tokenContractInfo = await getTokenContractInfo(
            token,
            ctx.session.settings.chainId
          );

          if (!tokenContractInfo) {
            return "";
          }

          const balance = await getBalance(
            getWallet(ctx).address,
            token,
            ctx.session.settings.chainId
          );

          // Store token index in session data if not already there
          let tokenIndex = ctx.session.data.tokens.findIndex(t => t === token);
          if (tokenIndex === -1) {
            ctx.session.data.tokens.push(token);
            tokenIndex = ctx.session.data.tokens.length - 1;
          }

          // Create a clickable link for the token name
          const botUsername = ctx.me.username;
          const tokenAddressWithout0x = token.replace('0x', '');
          const deepLink = `https://t.me/${botUsername}?start=token_${tokenAddressWithout0x}`;

          // Return the token info with a clickable name
          return `🔹 [${tokenContractInfo.name}](${deepLink}): ${balance} ${tokenContractInfo.symbol}`;
        }

        const balance = await getBalance(
          getWallet(ctx).address,
          tokenMeta.quoteToken.address,
          ctx.session.settings.chainId
        );

        // Handle case when priceUsd is undefined, null, or "null" but priceNative is available
        let priceDisplay;
        if (tokenMeta.priceUsd !== undefined && tokenMeta.priceUsd !== null && tokenMeta.priceUsd !== "null") {
          priceDisplay = `$${tokenMeta.priceUsd}`;
        } else if (tokenMeta.priceNative !== undefined && tokenMeta.priceNative !== null && tokenMeta.priceNative !== "null") {
          // Use priceNative and format it properly
          const priceNative = parseFloat(tokenMeta.priceNative);
          if (!isNaN(priceNative)) {
            priceDisplay = `$${priceNative.toFixed(6)}`;
          } else {
            priceDisplay = "Price unavailable";
          }
        } else {
          priceDisplay = "Price unavailable";
        }

        // Store token index in session data if not already there
        let tokenIndex = ctx.session.data.tokens.findIndex(t => t === token);
        if (tokenIndex === -1) {
          ctx.session.data.tokens.push(token);
          tokenIndex = ctx.session.data.tokens.length - 1;
        }

        // Create a clickable link for the token name
        const botUsername = ctx.me.username;
        const tokenAddressWithout0x = token.replace('0x', '');
        const deepLink = `https://t.me/${botUsername}?start=token_${tokenAddressWithout0x}`;

        // Return the token info with a clickable name
        return `🔹 [${tokenMeta.quoteToken.name}](${deepLink}): ${priceDisplay} (${tokenMeta.priceChange.h24}% in 24hr) - ${balance} ${tokenMeta.quoteToken.symbol}`;
      } catch (error) {
        console.error(`Error fetching token info for ${token}:`, error);
        return "";
      }
    })
  );

  // Filter out empty results
  const tokensWithInfo = tokenInfos.filter((v) => v.length > 0);

  // Get native balance
  const nativeBalance = await getBalance(
    getWallet(ctx).address,
    getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
    ctx.session.settings.chainId
  );

  // Create message lines for portfolio display
  const messageLines = [
    "📊 Portfolio",
    "",
    `💼 Wallet Balance: ${nativeBalance} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol}`,
    "",
    "Your tokens:"
  ];

  // Add tokens to the message
  if (tokensWithInfo.length > 0) {
    tokensWithInfo.forEach((tokenInfo) => {
      messageLines.push(tokenInfo);
    });
    // Add a note explaining that token names are clickable
    messageLines.push("");
    messageLines.push("Click on any token name to view details and trade options.");
  } else {
    messageLines.push("No tokens found");
  }

  // Add a note about adding new tokens
  messageLines.push("");
  messageLines.push("To add a new token, paste its contract address.");

  // Send the message without buttons or pagination
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2"
  });
}

async function handleAutoSell(ctx: BotContext) {
  const messageLines = [
    "📉 Auto Sell",
    "",
    "Enter the amount you want to auto sell:",
  ];
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

async function handleAutoBuy(ctx: BotContext) {
  const messageLines = [
    "🤖 Auto Buy",
    "",
    "Enter the amount you want to auto buy:",
  ];
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

async function handleSnipe(ctx: BotContext) {
  const messageLines = [
    "🎯 Snipe",
    "",
    `Enter the amount you want to snipe with $${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
    }:`,
  ];
  await send(ctx, messageLines, {
    reply_markup: { force_reply: true },
    parse_mode: "MarkdownV2",
  });
}

function mapStatsData(
  fn: (chainName: string, chainInfo: Chain) => string,
  ignore?: ESupportedChains[]
): string[] {
  return Object.entries(ESupportedChains).reduce((acc, [chain, cn]) => {
    const chainId = Number(chain) as ESupportedChains;
    const chainName = cn as string;
    if (ignore?.includes(chainId) || isNaN(chainId)) return acc;
    return [...acc, fn(chainName, getConfig(chainId).chain)];
  }, [] as string[]);
}

async function handleTransfer(ctx: BotContext) {
  console.log("Handle Transfer Triggered");
  console.log("Withdrawal Address:", ctx.session.withdrawalAddress);
  console.log("Withdrawal Amount:", ctx.session.withdrawalAmount);

  if (!ctx.session.withdrawalAddress || !ctx.session.withdrawalAmount) {
    await send(ctx, ["❌ Withdrawal cancelled"], {
      parse_mode: "MarkdownV2",
    });
    return;
  }

  const userConfig = getConfig(ctx.session.settings.chainId);
  const wallet = getWallet(ctx);
  const walletClient = createWalletClient({
    account: privateKeyToAccount(wallet.privateKey),
    chain: userConfig.chain,
    transport: http(),
  });

  try {
    // Get current balance
    const nativeBalance = await getBalance(
      wallet.address,
      userConfig.nativeCurrencyAddress,
      ctx.session.settings.chainId
    );

    // Determine amount to transfer
    const amount = ctx.session.withdrawalAmount === "withdraw-all"
      ? "withdraw-all" // Pass the special string to the transfer function
      : ctx.session.withdrawalAmount;

    // Show processing message
    const displayAmount = amount === "withdraw-all"
      ? `maximum available (leaving gas fee)`
      : `${amount} ${userConfig.chain.nativeCurrency.symbol}`;

    await send(ctx, [
      `🔄 Processing withdrawal of ${displayAmount}...`
    ]);

    // Execute transfer
    const txHash = await transferNativeToken(
      amount,
      ctx.session.withdrawalAddress as `0x${string}`,
      walletClient,
      ctx.session.settings.chainId
    );

    // Clear withdrawal data
    ctx.session.withdrawalAddress = undefined;
    ctx.session.withdrawalAmount = undefined;

    // Show success message
    await send(ctx, [
      `✅ Transfer successful!`,
      `🔗 View on [Scan](${userConfig.chain.blockExplorers?.default.url}/tx/${txHash})`
    ], {
      parse_mode: "MarkdownV2"
    });

  } catch (error) {
    console.error("Transfer error:", error);
    // Cancel withdrawal
    ctx.session.withdrawalAddress = undefined;
    ctx.session.withdrawalAmount = undefined;

    // Format error message for display
    let errorMessage = error instanceof Error ? error.message : "Unknown error";
    // Escape special characters for MarkdownV2
    errorMessage = errorMessage.replace(/([_*\[\]()~`>#+\-=|{}.!])/g, '\\$1');

    await send(ctx, [
      "❌ Transfer failed",
      `Error: ${errorMessage}`
    ], {
      parse_mode: "MarkdownV2"
    });
  }
}

async function handleCancel(ctx: BotContext) {
  // Clear withdrawal-related session data
  ctx.session.withdrawalAddress = undefined;
  ctx.session.withdrawalAmount = undefined;

  await send(ctx, ["❌ Withdrawal cancelled"], {
    parse_mode: "MarkdownV2",
  });

  // // Delete the message containing the withdrawal menu
  // await deleteMessage(ctx);
}

const withdrawAllbtn = new Menu<BotContext>("withdraw-all")
  .text(
    {
      text: "Withdraw All",
      payload: getChatIdPayload,
    },
    handleSetWithdrawAll
  )
  .text(
    {
      text: "Cancel",
      payload: getChatIdPayload,
    },
    handleCancel
  );

const confirmTransactionBtn = new Menu<BotContext>("confirm-transaction")
  .text(
    {
      text: "Confirm",
      payload: getChatIdPayload,
    },
    handleTransfer
  )
  .text(
    {
      text: "Cancel",
      payload: getChatIdPayload,
    },
    handleCancel
  );

async function handleSetWithdrawAddress(ctx: BotContext) {
  const messageText = ctx.update?.message?.text;

  if (!messageText || !messageText.match(/^0x[0-9a-fA-F]{40}$/)) {
    await send(ctx, ["Invalid Ethereum address"]);
    return;
  }

  const userConfig = getConfig(ctx.session.settings.chainId);
  const nativeBalance = await getBalance(
    getWallet(ctx).address,
    userConfig.nativeCurrencyAddress,
    ctx.session.settings.chainId
  );

  const receiverWalletAddress = messageText;
  ctx.session.withdrawalAddress = receiverWalletAddress as `0x${string}`;

  await send(ctx, [
    `💼 Current wallet balance: ${nativeBalance} ${userConfig.chain.nativeCurrency.symbol}`,
    "",
    `📤 Transfer to: ${receiverWalletAddress}`,
    "",
    `💰 Please enter the amount to withdraw:`,
  ], {
    reply_markup: withdrawAllbtn,
    parse_mode: "MarkdownV2",
  });
}

async function handleSetWithdrawAll(ctx: BotContext) {
  ctx.session.withdrawalAmount = "withdraw-all";
  const userConfig = getConfig(ctx.session.settings.chainId);
  const nativeBalance = await getBalance(
    getWallet(ctx).address,
    userConfig.nativeCurrencyAddress,
    ctx.session.settings.chainId
  );

  await send(ctx, [
    `📤 Transfer to: ${ctx.session.withdrawalAddress}`,
    `💰 Amount to withdraw: All (${nativeBalance} ${userConfig.chain.nativeCurrency.symbol})`,
    "",
    "Please confirm the withdrawal:",
  ], {
    reply_markup: confirmTransactionBtn,
    parse_mode: "MarkdownV2",
  });
}

const cancelTranfer = new Menu<BotContext>("cancel-transfer")
  .text(
    {
      text: "Cancel",
      payload: getChatIdPayload,
    },
    handleCancel
  );

async function handleSetWithdrawAmount(ctx: BotContext) {
  const amount = ctx.message?.text;
  if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
    await send(ctx, ["The amount to withdraw must be a number greater than 0"], {
      reply_markup: cancelTranfer
    });
    return;
  }
  ctx.session.withdrawalAmount = amount;
  const userConfig = getConfig(ctx.session.settings.chainId);
  const nativeBalance = await getBalance(
    getWallet(ctx).address,
    userConfig.nativeCurrencyAddress,
    ctx.session.settings.chainId
  );

  await send(ctx, [
    `💼 Current wallet balance: ${nativeBalance} ${userConfig.chain.nativeCurrency.symbol}`,
    "",
    `📤 Transfer to: ${ctx.session.withdrawalAddress}`,
    `💰 Amount to withdraw: ${amount} ${userConfig.chain.nativeCurrency.symbol}`,
    "",
    "Please confirm the withdrawal:",
  ], {
    reply_markup: confirmTransactionBtn,
    parse_mode: "MarkdownV2",
  });
}

async function handleUserStats(ctx: BotContext) {
  const userStats = ctx.session.data;
  const messageLines = [
    "📊 User Stats",
    "",
    `*Trades Statistics*`,
    ...mapStatsData(
      (chainName, chain) =>
        `🔹 ${chainName}: ${convertToMaxDecimals(
          userStats.tradeValueInETH[chain.id as ESupportedChains]
        )} ${chain.nativeCurrency.symbol}`,
      [ESupportedChains.Core]
    ),
    ,
    "",
    `*Referral Rewards Statistics*`,
    ...mapStatsData(
      (chainName, chain) =>
        `🔹 ${chainName}: ${convertToMaxDecimals(
          userStats.referralRewardInETH[chain.id as ESupportedChains]
        )} ${chain.nativeCurrency.symbol}`,
      [ESupportedChains.Core]
    ),
    "",
    `*Snipe Statistics*`,
    ...mapStatsData(
      (chainName, chain) =>
        `🔹 ${chainName}: ${convertToMaxDecimals(
          userStats.snipeValueInETH[chain.id as ESupportedChains]
        )} ${chain.nativeCurrency.symbol}`,
      [ESupportedChains.Core]
    ),
  ] as string[];
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
  });
}

async function handleShowAdminStats(ctx: BotContext) {
  const admin = await storageInstance.read(process.env.ADMIN_ID ?? "");
  if (
    Number(ctx.from?.id) !== Number(process.env.ADMIN_ID) &&
    !admin.__d.adminOnly?.admins.includes(ctx.from?.id ?? 0)
  ) {
    console.log("Not an admin", Number(ctx.from?.id));
    return;
  }
  const totalStats = await Promise.all(
    admin.__d.adminOnly?.users.map(async (user) => {
      const userStats = await storageInstance.read(user.toString());
      return {
        tradeValueInETH: userStats.__d.data.tradeValueInETH,
        referralRewardInETH: userStats.__d.data.referralRewardInETH,
        snipeValueInETH: userStats.__d.data.snipeValueInETH,
      };
    }) ?? []
  );
  const adminStats = totalStats.reduce(
    (acc, val) => {
      acc.tradeValueInETH = {
        [ESupportedChains.Base]:
          acc.tradeValueInETH[ESupportedChains.Base] +
          val.tradeValueInETH[ESupportedChains.Base],
        [ESupportedChains.ZkSync]:
          acc.tradeValueInETH[ESupportedChains.ZkSync] +
          val.tradeValueInETH[ESupportedChains.ZkSync],
        [ESupportedChains.Blast]:
          acc.tradeValueInETH[ESupportedChains.Blast] +
          val.tradeValueInETH[ESupportedChains.Blast],
        [ESupportedChains.Core]:
          acc.tradeValueInETH[ESupportedChains.Core] +
          val.tradeValueInETH[ESupportedChains.Core],
        [ESupportedChains.Sonic]:
          acc.tradeValueInETH[ESupportedChains.Sonic] +
          val.tradeValueInETH[ESupportedChains.Sonic],
        [ESupportedChains.Avax]:
          acc.tradeValueInETH[ESupportedChains.Avax] +
          val.tradeValueInETH[ESupportedChains.Avax],
        [ESupportedChains.Bera]:
          acc.tradeValueInETH[ESupportedChains.Bera] +
          val.tradeValueInETH[ESupportedChains.Bera],
        [ESupportedChains.Unichain]:
          acc.tradeValueInETH[ESupportedChains.Unichain] +
          val.tradeValueInETH[ESupportedChains.Unichain],
      };
      acc.referralRewardInETH = {
        [ESupportedChains.Base]:
          acc.referralRewardInETH[ESupportedChains.Base] +
          val.referralRewardInETH[ESupportedChains.Base],
        [ESupportedChains.ZkSync]:
          acc.referralRewardInETH[ESupportedChains.ZkSync] +
          val.referralRewardInETH[ESupportedChains.ZkSync],
        [ESupportedChains.Blast]:
          acc.referralRewardInETH[ESupportedChains.Blast] +
          val.referralRewardInETH[ESupportedChains.Blast],
        [ESupportedChains.Core]:
          acc.referralRewardInETH[ESupportedChains.Core] +
          val.referralRewardInETH[ESupportedChains.Core],
        [ESupportedChains.Sonic]:
          acc.referralRewardInETH[ESupportedChains.Sonic] +
          val.referralRewardInETH[ESupportedChains.Sonic],
        [ESupportedChains.Avax]:
          acc.referralRewardInETH[ESupportedChains.Avax] +
          val.referralRewardInETH[ESupportedChains.Avax],
        [ESupportedChains.Bera]:
          acc.referralRewardInETH[ESupportedChains.Bera] +
          val.referralRewardInETH[ESupportedChains.Bera],
        [ESupportedChains.Unichain]:
          acc.referralRewardInETH[ESupportedChains.Unichain] +
          val.referralRewardInETH[ESupportedChains.Unichain],
      };
      acc.snipeValueInETH = {
        [ESupportedChains.Base]:
          acc.snipeValueInETH[ESupportedChains.Base] +
          val.snipeValueInETH[ESupportedChains.Base],
        [ESupportedChains.ZkSync]:
          acc.snipeValueInETH[ESupportedChains.ZkSync] +
          val.snipeValueInETH[ESupportedChains.ZkSync],
        [ESupportedChains.Blast]:
          acc.snipeValueInETH[ESupportedChains.Blast] +
          val.snipeValueInETH[ESupportedChains.Blast],
        [ESupportedChains.Core]:
          acc.snipeValueInETH[ESupportedChains.Core] +
          val.snipeValueInETH[ESupportedChains.Core],
        [ESupportedChains.Sonic]:
          acc.snipeValueInETH[ESupportedChains.Sonic] +
          val.snipeValueInETH[ESupportedChains.Sonic],
        [ESupportedChains.Avax]:
          acc.snipeValueInETH[ESupportedChains.Avax] +
          val.snipeValueInETH[ESupportedChains.Avax],
        [ESupportedChains.Bera]:
          acc.snipeValueInETH[ESupportedChains.Bera] +
          val.snipeValueInETH[ESupportedChains.Bera],
        [ESupportedChains.Unichain]:
          acc.snipeValueInETH[ESupportedChains.Unichain] +
          val.snipeValueInETH[ESupportedChains.Unichain],
      };
      return acc;
    },
    {
      tradeValueInETH: { ...defaultNumByChain },
      referralRewardInETH: { ...defaultNumByChain },
      snipeValueInETH: { ...defaultNumByChain },
    }
  );
  const messageLines = [
    "📊 Admin Stats",
    "",
    `*Trades Statistics*`,
    `🔹 Base: ${adminStats.tradeValueInETH[ESupportedChains.Base]}`,
    `🔹 zkSync: ${adminStats.tradeValueInETH[ESupportedChains.ZkSync]}`,
    `🔹 Blast: ${adminStats.tradeValueInETH[ESupportedChains.Blast]}`,
    `🔹 Core: ${adminStats.tradeValueInETH[ESupportedChains.Core]}`,
    `🔹 Sonic: ${adminStats.tradeValueInETH[ESupportedChains.Sonic]}`,
    `🔹 Avax: ${adminStats.tradeValueInETH[ESupportedChains.Avax]}`,
    `🔹 Bera: ${adminStats.tradeValueInETH[ESupportedChains.Bera]}`,
    `🔹 Unichain: ${adminStats.tradeValueInETH[ESupportedChains.Unichain]}`,
    "",
    `*Referral Rewards Statistics*`,
    `🔹 Base: ${adminStats.referralRewardInETH[ESupportedChains.Base]}`,
    `🔹 zkSync: ${adminStats.referralRewardInETH[ESupportedChains.ZkSync]}`,
    `🔹 Blast: ${adminStats.referralRewardInETH[ESupportedChains.Blast]}`,
    `🔹 Core: ${adminStats.referralRewardInETH[ESupportedChains.Core]}`,
    `🔹 Sonic: ${adminStats.referralRewardInETH[ESupportedChains.Sonic]}`,
    `🔹 Avax: ${adminStats.referralRewardInETH[ESupportedChains.Avax]}`,
    `🔹 Bera: ${adminStats.referralRewardInETH[ESupportedChains.Bera]}`,
    `🔹 Unichain: ${adminStats.referralRewardInETH[ESupportedChains.Unichain]}`,
    "",
    `*Snipe Statistics*`,
    `🔹 Base: ${adminStats.snipeValueInETH[ESupportedChains.Base]}`,
    `🔹 zkSync: ${adminStats.snipeValueInETH[ESupportedChains.ZkSync]}`,
    `🔹 Blast: ${adminStats.snipeValueInETH[ESupportedChains.Blast]}`,
    `🔹 Core: ${adminStats.snipeValueInETH[ESupportedChains.Core]}`,
    `🔹 Sonic: ${adminStats.snipeValueInETH[ESupportedChains.Sonic]}`,
    `🔹 Avax: ${adminStats.snipeValueInETH[ESupportedChains.Avax]}`,
    `🔹 Bera: ${adminStats.snipeValueInETH[ESupportedChains.Bera]}`,
    `🔹 Unichain: ${adminStats.snipeValueInETH[ESupportedChains.Unichain]}`,
  ];
  send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
  });
}

const mainMenu = new Menu<BotContext>("main-menu")
  .text(
    {
      text: "🎯 Manual Buy/AutoSnipe",
      payload: getChatIdPayload,
    },
    handleManualBuy,
    (ctx) => ctx.menu.update()
  )
  .row()
  .text(
    {
      text: "🔧 Settings",
      payload: getChatIdPayload,
    },
    handleSettings,
    (ctx) => ctx.menu.update()
  )
  .text(
    {
      text: "🔁 Token Transfer",
      payload: getChatIdPayload,
    },
    handleTokenTransfer,
    (ctx) => ctx.menu.update()
  )
  .row()
  .text(
    {
      text: "🔒 Wallets",
      payload: getChatIdPayload,
    },
    handleWallets,
    (ctx) => ctx.menu.update()
  )
  .text("Import Wallet", handleImportWallet)
  .row()
  .text(
    {
      text: "📊 Portfolio",
      payload: getChatIdPayload,
    },
    handlePortfolio
  )
  .text({
    text: "🤑 Refer & Earn",
    payload: getChatIdPayload,
  })
  .row()
  .url(
    "📖 Docs",
    "https://anubis-8.gitbook.io/anubis-trading-bot/"
  )
  .url("💬 Official Chat", `https://t.me/${getConfig().chatId}`)
  .url("🌍 Website", `http://www.anubistrade.xyz/`)
  .row()
  .text(
    {
      text: "🏠 Main Menu",
      payload: getClosePayload,
    },
    async (ctx) => {
      const messageLines = [
        `${ctx.me.first_name}!`,
        "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
        "",
        "*🚀 Main Menu*",
        "Select an option to get started or paste a token address.",
      ];

      // Delete the current message
      const [chatId, messageId] = ctx.match?.split("::") ?? [];
      if (chatId && messageId) {
        await ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => { });
      }

      // Send the main menu without directly referencing mainMenu
      return send(ctx, messageLines, {
        parse_mode: "MarkdownV2",
        reply_markup: {
          inline_keyboard: [
            [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
            [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
            [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
            [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
            [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
            [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
          ]
        }
      });
    }
  );

const settingsMenu = new Menu<BotContext>("settings-menu")
  .text("🔗 Network:")
  .row()
  .text(
    (ctx) => {
      const isSetPrefix =
        ctx.session.settings.chainId === ESupportedChains.Base ? "🟢 " : "";
      return `${isSetPrefix} Base`;
    },
    (ctx) => {
      ctx.session.settings.chainId = ESupportedChains.Base;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const isSetPrefix =
        ctx.session.settings.chainId === ESupportedChains.Sonic ? "🟢 " : "";
      return `${isSetPrefix} Sonic`;
    },
    (ctx) => {
      ctx.session.settings.chainId = ESupportedChains.Sonic;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const isSetPrefix =
        ctx.session.settings.chainId === ESupportedChains.Avax ? "🟢 " : "";
      return `${isSetPrefix} Avax`;
    },
    (ctx) => {
      ctx.session.settings.chainId = ESupportedChains.Avax;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const isSetPrefix =
        ctx.session.settings.chainId === ESupportedChains.Bera ? "🟢 " : "";
      return `${isSetPrefix} Bera`;
    },
    (ctx) => {
      ctx.session.settings.chainId = ESupportedChains.Bera;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const isSetPrefix =
        ctx.session.settings.chainId === ESupportedChains.Unichain ? "🟢 " : "";
      return `${isSetPrefix} Unichain`;
    },
    (ctx) => {
      ctx.session.settings.chainId = ESupportedChains.Unichain;
      ctx.menu.update();
    }
  )
  // .text(
  //   (ctx) => {
  //     const isSetPrefix =
  //       ctx.session.settings.chainId === ESupportedChains.Core ? "🟢 " : "";
  //     return `${isSetPrefix} Core`;
  //   },
  //   (ctx) => {
  //     ctx.session.settings.chainId = ESupportedChains.Core;
  //     ctx.menu.update();
  //   }
  // )
  .row()
  .text("🔒 Slippage")
  .row()
  .text(
    (ctx) => {
      const value = 0.01;
      const isSetPrefix = ctx.session.settings.slippage === value ? "🟢 " : "";
      return `${isSetPrefix}${value * 10}%`;
    },
    (ctx) => {
      ctx.session.settings.slippage = 0.01;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const value = 0.05;
      const isSetPrefix = ctx.session.settings.slippage === value ? "🟢 " : "";
      return `${isSetPrefix}${value * 10}%`;
    },
    (ctx) => {
      ctx.session.settings.slippage = 0.05;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const value = 0.1;
      const isSetPrefix = ctx.session.settings.slippage === value ? "🟢 " : "";
      return `${isSetPrefix}${value * 10}%`;
    },
    (ctx) => {
      ctx.session.settings.slippage = 0.1;
      ctx.menu.update();
    }
  )
  .row()
  .text("⛽ Gas Price")
  .row()
  .text(
    (ctx) => {
      const value = 10 * 1e9;
      const isSetPrefix = ctx.session.settings.gasPrice === value ? "🟢 " : "";
      return `${isSetPrefix}${value / 1e9} Gwei`;
    },
    (ctx) => {
      ctx.session.settings.gasPrice = 10 * 1e9;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const value = 20 * 1e9;
      const isSetPrefix = ctx.session.settings.gasPrice === value ? "🟢 " : "";
      return `${isSetPrefix}${value / 1e9} Gwei`;
    },
    (ctx) => {
      ctx.session.settings.gasPrice = 20 * 1e9;
      ctx.menu.update();
    }
  )
  .text(
    (ctx) => {
      const value = 30 * 1e9;
      const isSetPrefix = ctx.session.settings.gasPrice === value ? "🟢 " : "";
      return `${isSetPrefix}${value / 1e9} Gwei`;
    },
    (ctx) => {
      ctx.session.settings.gasPrice = 30 * 1e9;
      ctx.menu.update();
    }
  )
  .row()
  .text(
    {
      text() {
        return "🏠 Main Menu";
      },
      payload(ctx) {
        const payload = getClosePayload(ctx);
        return payload;
      },
    },
    async (ctx) => {
      const messageLines = [
        `${ctx.me.first_name}!`,
        "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
        "",
        "*🚀 Main Menu*",
        "Select an option to get started or paste a token address.",
      ];

      // Delete the current message
      const [chatId, messageId] = ctx.match?.split("::") ?? [];
      if (chatId && messageId) {
        await ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => { });
      }

      // Send the main menu without directly referencing mainMenu
      return send(ctx, messageLines, {
        parse_mode: "MarkdownV2",
        reply_markup: {
          inline_keyboard: [
            [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
            [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
            [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
            [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
            [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
            [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
          ]
        }
      });
    }
  );

const dismissMenu = new Menu<BotContext>("dismiss-menu").text(
  "Return to Main Menu",
  async (ctx) => {
    const messageLines = [
      `${ctx.me.first_name}!`,
      "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
      "",
      "*🚀 Main Menu*",
      "Select an option to get started or paste a token address.",
    ];

    // Delete the current message
    const [chatId, messageId] = ctx.match?.split("::") ?? [];
    if (chatId && messageId) {
      await ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => { });
    }

    // Send the main menu without directly referencing mainMenu
    return send(ctx, messageLines, {
      parse_mode: "MarkdownV2",
      reply_markup: {
        inline_keyboard: [
          [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
          [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
          [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
          [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
          [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
          [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
        ]
      }
    });
  }
);

const walletsMenu = new Menu<BotContext>("wallets-menu")
  .text("Show Private Key", (ctx) => {
    const wallet = ctx.session.settings.wallets[ctx.session.walletId]!;

    send(ctx, ["Private Key: ", `\`${wallet.privateKey}\``], {
      parse_mode: "MarkdownV2",
    });
  })
  .text("Remove Wallet", (ctx) => {
    if (ctx.session.settings.wallets.length > 1) {
      ctx.session.settings.wallets = ctx.session.settings.wallets.filter(
        (_, index) => index !== ctx.session.walletId
      );
      send(ctx, ["Removed wallet successfully"]);
    } else {
      send(ctx, ["You must have at least one active wallet"]);
    }
  })
  .row()
  .text("🏠 Main Menu", async (ctx) => {
    // Show main menu
    const messageLines = [
      `${ctx.me.first_name}!`,
      "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
      "",
      "*🚀 Main Menu*",
      "Select an option to get started or paste a token address.",
    ];

    await send(ctx, messageLines, {
      parse_mode: "MarkdownV2",
      reply_markup: {
        inline_keyboard: [
          [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
          [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
          [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
          [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
          [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
          [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
        ]
      }
    });
  });

const contractTokenMenu = new Menu<BotContext>("contract-token-menu")
  .text(
    {
      text: "🎯 Snipe",
      payload: getTokenContextPayload,
    },
    handleSnipe
  )

const tokenMenu = new Menu<BotContext>("token-menu")
  .text(
    {
      text: (ctx) => {
        const amount = 1;
        return `💰 Buy ${amount} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
          }`;
      },
      payload: getTokenContextPayload,
    },
    (ctx) => handleBuyToken(ctx, 1, ctx.match)
  )
  .text(
    {
      text: (ctx) => {
        const amount = 5;
        return `💰 Buy ${amount} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
          }`;
      },
      payload: getTokenContextPayload,
    },
    (ctx) => handleBuyToken(ctx, 5, ctx.match)
  )
  .row()
  .text(
    {
      text: (ctx) => {
        const amount = 10;
        return `💰 Buy ${amount} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
          }`;
      },
      payload: getTokenContextPayload,
    },
    (ctx) => handleBuyToken(ctx, 10, ctx.match)
  )
  .text(
    {
      text: (ctx) => {
        const amount = 100;
        return `💰 Buy ${amount} ${getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol
          }`;
      },
      payload: getTokenContextPayload,
    },
    (ctx) => handleBuyToken(ctx, 100, ctx.match)
  )
  .row()
  .text(
    {
      text: "💰 Sell 25%",
      payload: getTokenContextPayload,
    },
    (ctx) => handleSellToken(ctx, 25, ctx.match)
  )
  .text(
    {
      text: "💰 Sell 50%",
      payload: getTokenContextPayload,
    },
    (ctx) => handleSellToken(ctx, 50, ctx.match)
  )
  .text(
    {
      text: "💰 Sell 100%",
      payload: getTokenContextPayload,
    },
    (ctx) => handleSellToken(ctx, 100, ctx.match)
  )
  .row()
  .text(
    {
      text: "💲 Custom Buy",
      payload: getTokenContextPayload,
    },
    handleCustomBuy
  )
  .text(
    {
      text: "💲 Custom Sell",
      payload: getTokenContextPayload,
    },
    handleCustomSell
  )
  .row()
  .text(
    {
      text: "🔄 Refresh",
      payload: getTokenContextPayload,
    },
    async (ctx) => {
      const [chatId, messageId, tokenId] = ctx.match.split("::");
      const tokenAddress = ctx.session.data.tokens[Number(tokenId)];
      await sendTokenInfo(
        ctx,
        {
          chat: { id: Number(chatId) },
          message_id: Number(messageId),
        } as Message,
        tokenAddress!
      );
      ctx.menu.update();
    }
  )
  .text(
    {
      text: "🏠 Main Menu",
      payload: getClosePayload,
    },
    async (ctx) => {
      const messageLines = [
        `${ctx.me.first_name}!`,
        "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
        "",
        "*🚀 Main Menu*",
        "Select an option to get started or paste a token address.",
      ];

      // Delete the current message
      const [chatId, messageId] = ctx.match?.split("::") ?? [];
      if (chatId && messageId) {
        await ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => { });
      }

      // Send the main menu without directly referencing mainMenu
      return send(ctx, messageLines, {
        parse_mode: "MarkdownV2",
        reply_markup: {
          inline_keyboard: [
            [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
            [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
            [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
            [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
            [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
            [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
          ]
        }
      });
    }
  );
// .text("🔙 Back", (ctx) => ctx.menu.back());

// Install the session middleware
bot.use(
  session({
    initial: (): SessionData => {
      const wallet = ethers.Wallet.createRandom();
      return {
        walletId: 0,
        data: {
          tokens: [],
          tradeValueInETH: { ...defaultNumByChain },
          referrals: [],
          referralRewardInETH: { ...defaultNumByChain },
          snipeValueInETH: { ...defaultNumByChain },
          snipes: [],
        },
        settings: {
          chainId: getConfig().chain.id,
          slippage: 0.01,
          gasPrice: 10 * 1e9,
          wallets: [
            {
              address: wallet.address as Address,
              phrase: wallet.mnemonic?.phrase,
              privateKey: wallet.privateKey as Address,
            },
          ],
        },
      };
    },
    storage,
    getSessionKey,
  })
);

// Install the plugin.
bot.use(hydrateReply);

// Sets default parse_mode for ctx.reply
bot.api.config.use(parseMode("MarkdownV2"));

// Register all menus
// Important: Register mainMenu last to avoid circular reference issues
bot.use(walletsMenu);
bot.use(settingsMenu);
bot.use(dismissMenu);
bot.use(contractTokenMenu);
bot.use(tokenMenu);
bot.use(confirmTransactionBtn);
bot.use(withdrawAllbtn);
bot.use(cancelTranfer);

// Register mainMenu last to avoid circular reference issues
bot.use(mainMenu);


bot.use(async (ctx, next) => {
  await dispatchSnipes(ctx);
  return next();
});


// Install the referral middleware
bot.use(
  referrals({
    enabled: true,
    onStart: async (ctx) => {
      console.log("onStart", ctx.from?.id);
      if (ctx.session.settings.viewedOnboarding) return;
      const admin = await storageInstance.read(process.env.ADMIN_ID ?? "");
      if (admin?.__d?.adminOnly && ctx.from?.id) {
        admin.__d.adminOnly.users.push(ctx.from?.id);
        await storageInstance.write(process.env.ADMIN_ID ?? "", admin);
      }
      const messageLines = [
        `👋 Welcome to ${ctx.me.first_name}, ${ctx.from?.first_name}!`,
        "",
        "A new wallet has been generated for you. Save the private key below❗",
        "",
        `🔑 Private Key: \`${getWallet(ctx).privateKey}\``,
        "",
        "To get started, please join our group!",
        `👥 [Join Group](https://t.me/+ufloRBNcyogzMGE0)`,
        "",
        "You can also refer friends to earn rewards!",
        `🎁 \`https://t.me/${ctx.me.username}?start=${ctx.from?.id}\``,
      ];
      ctx.session.settings.viewedOnboarding = true;
      await send(ctx, messageLines, {
        reply_markup: dismissMenu,
        parse_mode: "MarkdownV2",
      });
    },
    onReferral: async (ctx, id) => {
      const referrerId = id.toString();
      ctx.session.referredBy = referrerId;
      const referrerUser = await storageInstance.read(referrerId);
      if (referrerUser) {
        const isValidated = referrerUser.__d.data.referrals.find(
          (v) => v.id === ctx.from?.id
        );
        if (!isValidated) {
          referrerUser.__d.data.referrals.push({
            id: ctx.from?.id ?? 0,
            isValid: true,
            timestamp: Date.now(),
          });
          await storageInstance.write(referrerId, referrerUser);
        }
      }
    },
    userExists: async (_ctx, id) => {
      const user = await storageInstance.read(id.toString());
      return !!user;
    },
  })
);

bot.command("start", async (ctx) => {
  // Check if this is a deep link with a token parameter
  const startParam = ctx.message?.text?.split(' ')[1];

  if (startParam && startParam.startsWith("terminal")) {
    // Check if there's an existing OTP and if it hasn't expired
    if (ctx.session.otp && ctx.session.otpTTL && ctx.session.otpTTL > Date.now()) {
      // Calculate remaining time in minutes and seconds
      const remainingTime = Math.ceil((ctx.session.otpTTL - Date.now()) / 1000);
      const minutes = Math.floor(remainingTime / 60);
      const seconds = remainingTime % 60;

      // Compose login URL
      const loginUrl = `${process.env.TERMINAL_URL}login/auth/${ctx.from?.id}`;

      await ctx.reply(
        `⚠️ You already have an active OTP.\n\nYour current code is: <b><code>${ctx.session.otp}</code></b>\n\n<i>Expires in ${minutes}m ${seconds}s. Please wait for it to expire before requesting a new one.</i>`,
        {
          parse_mode: "HTML",
          reply_markup: {
            inline_keyboard: [
              [{ text: "🔑 Login", url: loginUrl }]
            ]
          }
        }

      );
      return;
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const ttl = Date.now() + 5 * 60 * 1000; // 5 minutes from now

    ctx.session.otp = otp;
    ctx.session.otpTTL = ttl;

    // Compose login URL
    const loginUrl = `${process.env.TERMINAL_URL}login/auth/${ctx.from?.id}`;

    await ctx.reply(
      `🔗 Your one-time link code is: <b><code>${otp}</code></b>\n\n<i>This code expires in 5 minutes. Use it to link your account in the web/terminal interface.</i>`,
      {
        parse_mode: "HTML",
        reply_markup: {
          inline_keyboard: [
            [{ text: "🔑 Login", url: loginUrl }]
          ]
        }
      }
    );

    // Check if User already exists and has completed onboarding
    const user = await storageInstance.read((ctx.from?.id ?? "").toString());
    if (user && user.__d.settings.viewedOnboarding) {
      return;
    }
  }

  if (startParam && startParam.startsWith('token_')) {
    try {
      const tokenAddressWithout0x = startParam.replace('token_', '');
      const tokenAddress = `0x${tokenAddressWithout0x}` as Address;

      console.log(`Processing token deep link for address: ${tokenAddress}`);

      if (!tokenAddress.match(/^0x[0-9a-fA-F]{40}$/)) {
        console.error(`Invalid token address format: ${tokenAddress}`);
        await send(ctx, ["Invalid token address format. Please try again."]);
        return;
      }

      let tokenIndex = ctx.session.data.tokens.findIndex(t => t.toLowerCase() === tokenAddress.toLowerCase());

      if (tokenIndex === -1) {
        ctx.session.data.tokens.push(tokenAddress);
        tokenIndex = ctx.session.data.tokens.length - 1;
        console.log(`Added new token at index ${tokenIndex}: ${tokenAddress}`);
      } else {
        console.log(`Found existing token at index ${tokenIndex}: ${tokenAddress}`);
      }

      ctx.session.token = tokenIndex.toString();

      const msg = await send(ctx, ["🔍 Loading token info..."]);
      if (msg !== true) {
        await sendTokenInfo(
          ctx,
          {
            chat: { id: msg?.chat?.id },
            message_id: msg?.message_id ?? 0,
          } as Message,
          tokenAddress
        );
      }
      return;
    } catch (error) {
      console.error('Error processing token deep link:', error);
      await send(ctx, ["An error occurred while processing the token. Please try again."]);
      return;
    }
  } else if (startParam && startParam.includes('_')) {
    const [tokenAddress, chainName] = startParam.split('_');

    if (ethers.utils.isAddress(tokenAddress as Address)) {
      const chain = Object.entries(supportedChains).find(([_, name]) => name === chainName?.toLowerCase());

      if (chain) {
        const chainId = Number(chain[0]) as ESupportedChains;
        ctx.session.settings.chainId = chainId;

        await send(ctx, [`Switched to ${chainName} to find the token.`]);

        const msg = await send(ctx, ["🔍 Searching for Token"]);
        if (msg !== true) {
          sendTokenInfo(
            ctx,
            {
              chat: { id: msg?.chat?.id },
              message_id: msg?.message_id ?? 0,
            } as Message,
            tokenAddress as Address
          );
        }
      } else {
        await send(ctx, [`Chain "${chainName}" is not supported.`]);
      }
    } else {
      await send(ctx, ["Invalid token address in the link."]);
    }
  } else {
    const wallet = getWallet(ctx);
    if (wallet) {
      await send(ctx, [
        `Welcome back, ${ctx.from?.first_name}!`, "", "Paste a contract address or pick an option to get started.",
      ], {
        reply_markup: mainMenu,
        parse_mode: "MarkdownV2",
      });
    }
  }

  // Regular start command without parameters
  const messageLines = [
    `${ctx.me.first_name}!`,
    "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience. Fully compatible with Dexscreener, Anubis enables traders to seamlessly analyse market trends and execute trades without ever switching between interfaces.",
    "",
    "*🚀 Some Commands*",
    "🔹 /settings - Change settings",
    "🔹 /transfer - Transfer tokens",
    "🔹 /wallets - Manage wallets",
    "🔹 /import - Import a new wallet",
    "🔹 /referral - Refer friends to earn rewards 🎁",
    "",
    `🔗 [Website](http://www.anubistrade.xyz/) | [Docs](https://anubis-8.gitbook.io/anubis-trading-bot/) | [Chat](https://t.me/${getConfig(ctx.session.settings.chainId).chatId
    })`,
    "Paste a contract address or pick an option to get started.",
  ];

  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: [
        [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
        [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
        [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
        [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
        [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
        [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
      ]
    }
  });
});
bot.command("settings", handleSettings);
bot.command("transfer", handleTokenTransfer);
bot.command("wallets", handleWallets);
bot.command("referral", handleReferrals);
bot.command("portfolio", handlePortfolio);
bot.command("import", handleImportWallet);
bot.command("stats", handleUserStats);
bot.command("admin", handleShowAdminStats);

bot.command("link", async (ctx) => {
  // Check if there's an existing OTP and if it hasn't expired
  if (ctx.session.otp && ctx.session.otpTTL && ctx.session.otpTTL > Date.now()) {
    // Calculate remaining time in minutes and seconds
    const remainingTime = Math.ceil((ctx.session.otpTTL - Date.now()) / 1000);
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;

    // Compose login URL
    const loginUrl = `${process.env.TERMINAL_URL}login/auth/${ctx.from?.id}`;

    await ctx.reply(
      `⚠️ You already have an active OTP.\n\nYour current code is: <b><code>${ctx.session.otp}</code></b>\n\n<i>Expires in ${minutes}m ${seconds}s. Please wait for it to expire before requesting a new one.</i>`,
      {
        parse_mode: "HTML",
        reply_markup: {
          inline_keyboard: [
            [{ text: "🔑 Login", url: loginUrl }]
          ]
        }
      }
    );
    return;
  }

  // Generate 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const ttl = Date.now() + 5 * 60 * 1000; // 5 minutes from now

  ctx.session.otp = otp;
  ctx.session.otpTTL = ttl;

  // Compose login URL
  const loginUrl = `${process.env.TERMINAL_URL}login/auth/${ctx.from?.id}`;

  await ctx.reply(
    `🔗 Your one-time link code is: <b><code>${otp}</code></b>\n\n<i>This code expires in 5 minutes. Use it to link your account in the web/terminal interface.</i>`,
    {
      parse_mode: "HTML",
      reply_markup: {
        inline_keyboard: [
          [{ text: "🔑 Login", url: loginUrl }]
        ]
      }
    }
  );
});

// Add handler for callback queries
bot.on('callback_query:data', async (ctx) => {
  const callbackData = ctx.callbackQuery.data;

  // Handle trade button clicks
  if (callbackData.startsWith('trade::')) {
    // Extract token index from callback data
    const parts = callbackData.split('::');
    if (parts.length < 2) return;

    const tokenIndex = parseInt(parts[1] as string);
    if (isNaN(tokenIndex) || tokenIndex < 0 || tokenIndex >= ctx.session.data.tokens.length) return;

    const tokenAddress = ctx.session.data.tokens[tokenIndex] as Address;

    // Set the token in the session
    ctx.session.token = tokenIndex.toString();

    // Answer the callback query to stop loading indicator
    await ctx.answerCallbackQuery();

    // Show token info with trading options
    const msg = await send(ctx, ["🔍 Loading token info..."]);
    if (msg !== true) {
      sendTokenInfo(
        ctx,
        {
          chat: { id: msg?.chat?.id },
          message_id: msg?.message_id ?? 0,
        } as Message,
        tokenAddress
      );
    }
  }
  // We've removed the portfolio pagination and buttons
  // Handle wallet actions
  else if (callbackData.startsWith('wallet_action:')) {
    // Extract the action and parameters from the callback data
    const parts = callbackData.split(':');
    if (parts.length < 2) return;

    const action = parts[1];
    const walletIndex = parts.length > 2 ? parseInt(parts[2] as string) : -1;

    // Answer the callback query
    await ctx.answerCallbackQuery();

    // Handle different wallet actions
    switch (action) {
      case 'generate_new':
        // Generate a new wallet
        await handleGenerateNewWallet(ctx);
        break;
      case 'manage':
        // Manage a specific wallet
        if (walletIndex >= 0) {
          await handleManageWallet(ctx, walletIndex);
        }
        break;
      case 'show_key':
        // Show private key for a wallet
        if (walletIndex >= 0) {
          await handleShowWalletKey(ctx, walletIndex);
        }
        break;
      case 'remove':
        // Remove a wallet
        if (walletIndex >= 0) {
          await handleRemoveWallet(ctx, walletIndex);
        }
        break;
      case 'set_active':
        // Set a wallet as active
        if (walletIndex >= 0) {
          await handleSetActiveWallet(ctx, walletIndex);
        }
        break;
    }
  }
  // Handle main menu actions
  else if (callbackData.startsWith('main_menu_action:')) {
    // Extract the action from the callback data
    const action = callbackData.split(':')[1];

    // Answer the callback query
    await ctx.answerCallbackQuery();

    // Handle different actions
    switch (action) {
      case 'manual_buy':
        await handleManualBuy(ctx);
        break;
      case 'settings':
        await handleSettings(ctx);
        break;
      case 'transfer':
        await handleTokenTransfer(ctx);
        break;
      case 'wallets':
        await handleWallets(ctx);
        break;
      case 'import':
        await handleImportWallet(ctx);
        break;
      case 'portfolio':
        await handlePortfolio(ctx);
        break;
      case 'referral':
        await handleReferrals(ctx);
        break;
      case 'main_menu':
        // Show main menu
        const messageLines = [
          `${ctx.me.first_name}!`,
          "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
          "",
          "*🚀 Main Menu*",
          "Select an option to get started or paste a token address.",
        ];

        await send(ctx, messageLines, {
          parse_mode: "MarkdownV2",
          reply_markup: {
            inline_keyboard: [
              [{ text: "🎯 Manual Buy/AutoSnipe", callback_data: "main_menu_action:manual_buy" }],
              [{ text: "🔧 Settings", callback_data: "main_menu_action:settings" }, { text: "🔁 Token Transfer", callback_data: "main_menu_action:transfer" }],
              [{ text: "🔒 Wallets", callback_data: "main_menu_action:wallets" }, { text: "Import Wallet", callback_data: "main_menu_action:import" }],
              [{ text: "📊 Portfolio", callback_data: "main_menu_action:portfolio" }, { text: "🤑 Refer & Earn", callback_data: "main_menu_action:referral" }],
              [{ text: "📖 Docs", url: "https://anubis-8.gitbook.io/anubis-trading-bot/" }, { text: "💬 Official Chat", url: `https://t.me/${getConfig().chatId}` }, { text: "🌍 Website", url: "http://www.anubistrade.xyz/" }],
              [{ text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }]
            ]
          }
        });
        break;
    }
  }
});

bot.on("message:text", async (ctx) => {
  const isMessageATokenAddress = !!ctx.message.text.match(/0x[0-9a-fA-F]{40}/);
  const isMessageQuoted = !!ctx.message.reply_to_message;
  const isTriggerTransferAction = ctx.session.withdrawalAddress && ctx.session.withdrawalAmount;
  const isSetTransferAmountAction = ctx.session.withdrawalAddress && !ctx.session.withdrawalAmount;

  if (isMessageATokenAddress && !isMessageQuoted) {
    await routeTokenAction(ctx);
    return;
  }

  if (isMessageQuoted) {
    switch (ctx.message.reply_to_message?.text?.split("\n")[0]) {
      case "💲 Custom Buy":
        const amount = parseFloat(ctx.message.text);
        await handleBuyToken(ctx, amount, getTokenContextPayload(ctx));
        break;
      case "💲 Custom Sell":
        const percent = parseFloat(ctx.message.text);
        await handleSellToken(ctx, percent, getTokenContextPayload(ctx));
        break;
      case "💸 Withdraw Tokens":
        await routeWithdrawToken(ctx);
        break;
      case "🔒 Wallets":
        if (isMessageATokenAddress) {
          await routeSwitchWallet(ctx);
        } else {
          ctx.reply("Invalid wallet address");
        }
        break;
      case "🔁 Token Transfer":
        await handleSetWithdrawAddress(ctx);
        break;
      case "🎯 Snipe":
        await routeSnipeToken(ctx);
        break;
      case "🔒 Import Wallet":
        await routeImportWallet(ctx);
        break;
    }
  }

  if (isTriggerTransferAction) {
    await handleTransfer(ctx);
  }

  if (isSetTransferAmountAction) {
    await handleSetWithdrawAmount(ctx)
  }
});

// Function to generate a new wallet
async function handleGenerateNewWallet(ctx: BotContext) {
  // Create a new random wallet
  const wallet = ethers.Wallet.createRandom();

  // Add the wallet to the user's wallets
  ctx.session.settings.wallets.push({
    address: wallet.address as Address,
    phrase: wallet.mnemonic?.phrase,
    privateKey: wallet.privateKey as Address,
  });

  // Show success message with private key
  await send(ctx, [
    "✅ *New Wallet Generated*",
    "",
    "Your new wallet has been created and added to your wallet list.",
    "",
    "🔑 *IMPORTANT: Save this private key in a secure location!*",
    `\`${wallet.privateKey}\``,
    "",
    "🔒 *Mnemonic Phrase (Backup):*",
    `\`${wallet.mnemonic?.phrase}\``,
    "",
    "Address:",
    `\`${wallet.address}\``,
  ], {
    parse_mode: "MarkdownV2",
  });

  // Show the updated wallet list
  await handleWallets(ctx);
}

// Function to handle wallet management
async function handleManageWallet(ctx: BotContext, walletIndex: number) {
  // Set the selected wallet as active
  ctx.session.walletId = walletIndex;
  const wallet = ctx.session.settings.wallets[walletIndex];

  if (!wallet) {
    await send(ctx, ["❌ Wallet not found"]);
    return;
  }

  // Get wallet balance
  const balance = await getBalance(
    wallet.address,
    getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
    ctx.session.settings.chainId
  );

  const symbol = getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol;

  // Create message with wallet details
  const messageLines = [
    "🔒 *Wallet Details*",
    "",
    `Address: \`${wallet.address}\``,
    `Balance: ${balance} ${symbol}`,
    "",
    "Select an action:"
  ];

  // Create inline keyboard with wallet actions
  const inlineKeyboard = [
    [
      { text: "✅ Set as Active", callback_data: `wallet_action:set_active:${walletIndex}` },
      { text: "🔑 Show Private Key", callback_data: `wallet_action:show_key:${walletIndex}` }
    ],
    [
      { text: "🔄 Transfer Tokens", callback_data: "main_menu_action:transfer" },
      { text: "❌ Remove Wallet", callback_data: `wallet_action:remove:${walletIndex}` }
    ],
    [
      { text: "🔙 Back to Wallets", callback_data: "main_menu_action:wallets" },
      { text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }
    ]
  ];

  // Send the message with inline keyboard
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: inlineKeyboard
    }
  });
}

// Function to show wallet private key
async function handleShowWalletKey(ctx: BotContext, walletIndex: number) {
  const wallet = ctx.session.settings.wallets[walletIndex];

  if (!wallet) {
    await send(ctx, ["❌ Wallet not found"]);
    return;
  }

  // Show private key with warning
  await send(ctx, [
    "🔑 *Private Key*",
    "",
    "⚠️ *CAUTION: Never share your private key with anyone!*",
    "",
    `\`${wallet.privateKey}\``,
    "",
    "Mnemonic Phrase (if available):",
    wallet.phrase ? `\`${wallet.phrase}\`` : "Not available"
  ], {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: [
        [{ text: "🔙 Back to Wallet", callback_data: `wallet_action:manage:${walletIndex}` }]
      ]
    }
  });
}

// Function to remove a wallet
async function handleRemoveWallet(ctx: BotContext, walletIndex: number) {
  // Check if this is the only wallet
  if (ctx.session.settings.wallets.length <= 1) {
    await send(ctx, [
      "❌ Cannot remove wallet",
      "",
      "You must have at least one wallet. Please create or import another wallet before removing this one."
    ], {
      parse_mode: "MarkdownV2",
      reply_markup: {
        inline_keyboard: [
          [{ text: "🔙 Back to Wallet", callback_data: `wallet_action:manage:${walletIndex}` }]
        ]
      }
    });
    return;
  }

  // Remove the wallet
  ctx.session.settings.wallets = ctx.session.settings.wallets.filter((_, index) => index !== walletIndex);

  // If the removed wallet was active, set the first wallet as active
  if (ctx.session.walletId === walletIndex) {
    ctx.session.walletId = 0;
  } else if (ctx.session.walletId > walletIndex) {
    // Adjust the active wallet index if it was after the removed wallet
    ctx.session.walletId--;
  }

  // Show success message
  await send(ctx, [
    "✅ Wallet removed successfully"
  ], {
    parse_mode: "MarkdownV2"
  });

  // Show the updated wallet list
  await handleWallets(ctx);
}

// Function to set a wallet as active
async function handleSetActiveWallet(ctx: BotContext, walletIndex: number) {
  // Set the selected wallet as active
  ctx.session.walletId = walletIndex;

  // Show success message
  await send(ctx, [
    "✅ Wallet set as active"
  ], {
    parse_mode: "MarkdownV2"
  });

  // Show the updated wallet list
  await handleWallets(ctx);
}

// Add error handler to prevent bot from crashing
bot.catch((err) => {
  console.error('Bot error:', err);
});

process.once("SIGINT", () => bot.stop());
process.once("SIGTERM", () => bot.stop());

// Start the bot (using long polling)
bot
  .start()
  .then(() => {
    console.log("Bot is running");
  })
  .catch(console.error);
