import { z } from 'zod';
import { <PERSON>o } from "hono";
import { storageInstance } from '../../../storage';
import { zValidator } from '@hono/zod-validator';
import { Address } from "viem";
import isLoggedIn from "../middleware/is-logged-in";
import { extractTokenFromPair } from '../../../libs/gecko';
import { swapTokenFor } from '../../../bot-logic';
import { getConfig } from '../../../config';
import { sendMessage } from '../helper/bot';
import { getBalance } from '../../../libs/token';

const trade = new Hono();

// Trade Schema
const tradeSchema = z.object({
    token: z.string(),
    amount: z.number(),
})

// Extend schema for sell: token (string), percent (number 0-100)
const sellSchema = z.object({
    token: z.string(),
    percent: z.number().min(1).max(100),
});

// Trade Token
trade.post("/token", isLoggedIn, zValidator('json', tradeSchema), async (c: any) => {
    const tgUserId = c.get('user');
    const user = await storageInstance.read(tgUserId);

    if (!user) {
        return c.json({
            error: "User not found",
            message: "There was an error fetching the user's data. Please try again."
        }, 400)
    }

    const { token, amount } = c.req.valid('json');
    // Add token to user's session if not present
    if (!user.__d.data.tokens.includes(token)) {
        user.__d.data.tokens.push(token);
        user.__d.token = user.__d.data.tokens.length.toString();
    } else {
        user.__d.token = user.__d.data.tokens.findIndex((v: string) => v === token).toString();
    }
    // Prepare trade execution
    const chainId = user.__d.settings.chainId;
    try {
        const pairData = await extractTokenFromPair(chainId, token);
        if (!pairData) {
            await sendMessage({
                chat_id: tgUserId,
                text: `❌ Trade failed: Token not found on supported DEX.`,
                parse_mode: "HTML"
            });
            return c.json({ error: true, message: "Token not found on supported DEX." }, 400);
        }
        // Construct a minimal BotContext-like object for swapTokenFor
        const ctx = {
            session: user.__d,
            match: '',
        } as any; // Satisfy BotContext type
        await swapTokenFor(
            ctx,
            getConfig(chainId).nativeCurrencyAddress,
            token,
            pairData,
            amount
        );
        await storageInstance.write(tgUserId, user);
        await sendMessage({
            chat_id: tgUserId,
            text: `✅ Trade executed successfully!\nBought <b>${amount}</b> ${pairData.baseToken.symbol} (${pairData.baseToken.name})`,
            parse_mode: "HTML"
        });
        return c.json({ success: true, message: "Trade executed.", token, amount });
    } catch (err: any) {
        await sendMessage({
            chat_id: tgUserId,
            text: `❌ Trade failed: ${err.message || "Trade failed."}`,
            parse_mode: "HTML"
        });
        return c.json({ error: true, message: err.message || "Trade failed." }, 400);
    }
});

// Snipe Trade
trade.post("/snipe", isLoggedIn, zValidator('json', tradeSchema), async (c: any) => {
    const tgUserId = c.get('user');
    const user = await storageInstance.read(tgUserId);

    if (!user) {
        return c.json({
            error: "User not found",
            message: "There was an error fetching the user's data. Please try again."
        }, 400)
    }

    const { token, amount } = c.req.valid('json');
    // Add token to user's session if not present
    if (!user.__d.data.tokens.includes(token)) {
        user.__d.data.tokens.push(token);
        user.__d.token = user.__d.data.tokens.length.toString();
    } else {
        user.__d.token = user.__d.data.tokens.findIndex((v: string) => v === token).toString();
    }
    // Add snipe data
    const chainId = user.__d.settings.chainId;
    user.__d.data.snipeValueInETH = {
        ...user.__d.data.snipeValueInETH,
        [chainId]: (user.__d.data.snipeValueInETH[chainId] || 0) + amount,
    };
    user.__d.data.snipes.push({
        chainId,
        token,
        amount,
        timestamp: Date.now(),
    });
    await storageInstance.write(tgUserId, user);
    await sendMessage({
        chat_id: tgUserId,
        text: `🎯 Snipe scheduled for <b>${amount}</b> of token <code>${token}</code>!\nYou'll be notified when the snipe is executed.`,
        parse_mode: "HTML"
    });
    return c.json({ success: true, message: "Snipe request registered.", token, amount });
});

// Sell Token
trade.post("/sell", isLoggedIn, zValidator('json', sellSchema), async (c: any) => {
    const tgUserId = c.get('user');
    const user = await storageInstance.read(tgUserId);

    if (!user) {
        return c.json({
            error: "User not found",
            message: "There was an error fetching the user's data. Please try again."
        }, 400)
    }

    const { token, percent } = c.req.valid('json');
    // Ensure token is in session
    let tokenIndex = user.__d.data.tokens.findIndex((v: string) => v === token);
    if (tokenIndex === -1) {
        user.__d.data.tokens.push(token);
        tokenIndex = user.__d.data.tokens.length - 1;
        user.__d.token = tokenIndex.toString();
    } else {
        user.__d.token = tokenIndex.toString();
    }
    const chainId = user.__d.settings.chainId;
    try {
        const pairData = await extractTokenFromPair(chainId, token);
        if (!pairData) {
            await sendMessage({
                chat_id: tgUserId,
                text: `❌ Sell failed: Token not found on supported DEX.`,
                parse_mode: "HTML"
            });
            return c.json({ error: true, message: "Token not found on supported DEX." }, 400);
        }
        // Get user's wallet address and token balance
        const wallet = user.__d.settings.wallets[user.__d.walletId];
        if (!wallet) {
            await sendMessage({
                chat_id: tgUserId,
                text: `❌ Sell failed: No wallet found.`,
                parse_mode: "HTML"
            });
            return c.json({ error: true, message: "No wallet found." }, 400);
        }
        // Get balance using imported getBalance
        const balanceStr = await getBalance(wallet.address, token, chainId);
        const balance = Number(balanceStr);
        if (!balance || isNaN(balance)) {
            await sendMessage({
                chat_id: tgUserId,
                text: `❌ Sell failed: No balance for this token.`,
                parse_mode: "HTML"
            });
            return c.json({ error: true, message: "No balance for this token." }, 400);
        }
        const amount = (balance * percent) / 100;
        if (amount <= 0) {
            await sendMessage({
                chat_id: tgUserId,
                text: `❌ Sell failed: Calculated sell amount is zero.`,
                parse_mode: "HTML"
            });
            return c.json({ error: true, message: "Calculated sell amount is zero." }, 400);
        }
        // Construct a minimal BotContext-like object for swapTokenFor
        const ctx = {
            session: user.__d,
            match: '',
        } as any;
        await swapTokenFor(
            ctx,
            token, // tokenIn: the token to sell
            getConfig(chainId).nativeCurrencyAddress, // tokenOut: native currency
            pairData,
            amount
        );
        await storageInstance.write(tgUserId, user);
        await sendMessage({
            chat_id: tgUserId,
            text: `✅ Sell executed successfully!\nSold <b>${amount}</b> ${pairData.baseToken.symbol} (${pairData.baseToken.name}) for native currency.`,
            parse_mode: "HTML"
        });
        return c.json({ success: true, message: "Sell executed.", token, amount });
    } catch (err: any) {
        await sendMessage({
            chat_id: tgUserId,
            text: `❌ Sell failed: ${err.message || "Sell failed."}`,
            parse_mode: "HTML"
        });
        return c.json({ error: true, message: err.message || "Sell failed." }, 400);
    }
});

export default trade;