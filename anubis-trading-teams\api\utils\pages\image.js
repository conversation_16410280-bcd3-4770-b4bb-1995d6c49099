const { infoParseText } = require("../lib");
const { page_update_msg } = require("../message-structure");

async function setLogo(pageId, ctx) {
  const { id: chatId, photo } = infoParseText(ctx);

  const page = await Page.findOne({ id: pageId });
  const user = await User.findOne({ chatId });

  if (page) {
    try {
      await Page.updateOne({ id: page.id }).set({
        pageLogo: photo,
      });

      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      const keyboard = page_update_msg(page.id, page.pageTheme);

      await sails.helpers.sendMessage(
        ctx.message.from.id,
        "✅ Media uploaded successfully!"
      );

      await sails.helpers.sendMessage(
        ctx.message.from.id,
        "Customize your page below:",
        keyboard
      );

      return;
    } catch (error) {
      await sails.helpers.sendMessage(
        chatId,
        "Failed to process photo, please try again"
      );

      throw error;
    }
  }

  await sails.helpers.sendMessage(
    chatId,
    "There was a problem uploading the logo... Please try again later"
  );

  return;
}

async function setBannerImage(pageId, ctx) {
  const { id: chatId, photo } = infoParseText(ctx);

  const page = await Page.findOne({ id: pageId });
  const user = await User.findOne({ chatId });

  if (page) {
    try {
      await Page.updateOne({ id: page.id }).set({
        banner: photo,
      });

      await User.updateOne({ id: user.id }).set({
        mode: "conversation",
        currentConfiguredGroup: "",
        currentConfiguredService: "",
        currentServiceType: "",
      });

      const keyboard = page_update_msg(page.id, page.pageTheme);

      await sails.helpers.sendMessage(
        ctx.message.from.id,
        "✅ Media uploaded successfully!"
      );

      await sails.helpers.sendMessage(
        ctx.message.from.id,
        "Customize your page below:",
        keyboard
      );

      return;
    } catch (error) {
      await sails.helpers.sendMessage(
        chatId,
        "Failed to process photo, please try again"
      );

      throw error;
    }
  }

  await sails.helpers.sendMessage(
    chatId,
    "There was a problem uploading the logo... Please try again later"
  );

  return;
}

module.exports = {
  setLogo,
  setBannerImage,
};

