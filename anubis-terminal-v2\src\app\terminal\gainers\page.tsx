"use client";

import { useState, useEffect } from "react";
import FeedStatus from "@/components/Terminal/FeedStatus"
import TimeFilter from "@/components/Terminal/TimeFilter"
import StatsFilter from "@/components/Terminal/TimeFilter/StatsFilter"
import TokenTable from "@/components/Terminal/TokenTable"
import { useWeb3 } from '@/contexts/Web3Context';
import { useGetMarketData } from '@/hooks/useGetMarketData';
import { TailSpin } from 'react-loader-spinner';
import { ImSpinner2 } from "react-icons/im";


const timeOptions = ["1h", "4h", "12h", "24h"];
type TimeOption = string;

export default function Gainers() {
    const { selectedChain } = useWeb3();
    const [selectedTime, setSelectedTime] = useState<TimeOption>("24h");
    const { gainers, loading, error, refetch } = useGetMarketData({ networks: selectedChain?.slug, time: selectedTime, pollingInterval: 5000 });

    if (!selectedChain) {
        return (
            <div className="flex items-center justify-center min-h-[calc(100vh-80px)]">
                <ImSpinner2 className="animate-spin text-white text-2xl" />
            </div>
        );
    }
    
    const handleTimeSelect = (time: TimeOption) => {
        setSelectedTime(time);
    };

    useEffect(() => {
       document.title = "🚀 Top Gainers - Live Crypto Price Movements | Anubis Terminal";
    }, []);

    if (error) {
        return (
            <div className="min-h-[calc(100vh-80px)] flex flex-col items-center justify-center relative overflow-hidden px-4 bg-[#0a0a0a]">
                {/* Background elements */}
                <div className="absolute inset-0 bg-gradient-to-b from-[#0a0a0a] to-[#1a1a1a] z-0"></div>

                {/* Error container */}
                <div className="relative z-10 flex flex-col items-center justify-center gap-8 max-w-4xl mx-auto text-center">
                    {/* Error icon */}
                    <div className="w-24 h-24 rounded-full border-2 border-red-500 flex items-center justify-center mb-4">
                        <span className="text-red-500 text-5xl font-bold">!</span>
                    </div>

                    {/* Error message */}
                    <div className="space-y-4">
                        <h2 className="text-xl sm:text-2xl md:text-3xl font-orbitron font-bold text-white">
                            DATA FETCH ERROR
                        </h2>
                        <p className="text-gray-400 font-space-grotesk max-w-md mx-auto">
                            We're having trouble loading gainer tokens. Please try again.
                        </p>
                    </div>

                    {/* Error details */}
                    <div className="w-full max-w-md bg-black/50 border border-white/20 rounded-md p-4 font-mono text-sm text-left">
                        <div className="flex items-center gap-2 mb-2">
                            <div className="w-3 h-3 rounded-full bg-red-500"></div>
                            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                            <div className="w-3 h-3 rounded-full bg-green-500"></div>
                            <span className="ml-2 text-gray-400">Terminal</span>
                        </div>
                        <div className="font-geist-mono text-white">
                            <span className="text-green-500">anubis@terminal:~$</span> fetch.gainers
                            <br />
                            <span className="text-red-500">Error:</span> {error.message || "Failed to fetch gainers tokens"}
                        </div>
                    </div>

                    {/* Retry button */}
                    <button
                        className="flex items-center gap-2 font-orbitron font-semibold bg-white text-black px-6 py-3 rounded-md hover:shadow-lg hover:shadow-white/20"
                        onClick={refetch}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[calc(100vh-80px)]">
                <TailSpin
                    visible={true}
                    height="80"
                    width="80"
                    color="#FFF"
                    ariaLabel="tail-spin-loading"
                    radius="1"
                />
            </div>
        );
    }

    return (
        <section className="min-h-[calc(100vh-80px)] overflow-hidden p-3 md:p-10">
            <div className="flex flex-col md:flex-row justify-between w-full items-start md:items-center mb-4 gap-4 md:gap-0">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-3">
                    <div className="flex items-center gap-x-2">
                        <h3 className="font-space-grotesk">Gainers</h3>
                        <TimeFilter timeOptions={timeOptions} selectedTime={selectedTime} handleTimeSelect={handleTimeSelect} />
                    </div>
                    <StatsFilter />
                </div>

                <FeedStatus status="LIVE" />
            </div>

            <div className="mt-4">
                {gainers && <TokenTable data={gainers} selectedTime={selectedTime} />}
            </div>
        </section>
    )
}