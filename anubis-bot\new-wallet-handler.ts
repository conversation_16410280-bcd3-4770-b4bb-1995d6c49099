async function handleWallets(ctx: BotContext) {
  // Get wallet balances
  const walletInfos = await Promise.all(
    ctx.session.settings.wallets.map(async (wallet, index) => {
      if (!wallet) return null;
      const balance = await getBalance(
        wallet.address,
        getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
        ctx.session.settings.chainId
      );
      return {
        index,
        address: wallet.address,
        balance,
        isActive: ctx.session.walletId === index
      };
    })
  );

  // Filter out null values
  const validWallets = walletInfos.filter(w => w !== null) as {
    index: number;
    address: Address;
    balance: string;
    isActive: boolean;
  }[];

  // Create message lines
  const messageLines = [
    "🔒 *Wallet Management*",
    "",
    "Select a wallet to manage or create a new one:",
    ""
  ];

  // Create inline keyboard with wallet buttons
  const inlineKeyboard: Array<Array<{ text: string; callback_data: string }>> = [];

  // Add wallet buttons
  validWallets.forEach(wallet => {
    const symbol = getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol;
    const shortAddress = `${wallet.address.substring(0, 6)}...${wallet.address.substring(wallet.address.length - 4)}`;
    
    // Add wallet info to message
    messageLines.push(`${wallet.isActive ? "🟢" : "🔴"} Wallet ${wallet.index + 1}: ${shortAddress} - ${wallet.balance} ${symbol}`);
    
    // Add button for this wallet
    inlineKeyboard.push([{
      text: `${wallet.isActive ? "✅" : ""} Manage Wallet ${wallet.index + 1}`,
      callback_data: `wallet_action:manage:${wallet.index}`
    }]);
  });

  // Add a button to generate a new wallet
  inlineKeyboard.push([{
    text: "🔑 Generate New Wallet",
    callback_data: "wallet_action:generate_new"
  }]);

  // Add import wallet button
  inlineKeyboard.push([{
    text: "📥 Import Wallet",
    callback_data: "main_menu_action:import"
  }]);

  // Add main menu button
  inlineKeyboard.push([{
    text: "🏠 Main Menu",
    callback_data: "main_menu_action:main_menu"
  }]);

  // Send the message with inline keyboard
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: inlineKeyboard
    }
  });
}
