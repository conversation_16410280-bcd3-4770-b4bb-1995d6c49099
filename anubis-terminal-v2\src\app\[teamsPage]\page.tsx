"use client";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { getTeam } from "@/services/bot/teams";
import { getTokenData } from "@/services/api/dexscreener";
import GlitchText from "@/components/ui/GlitchText";
import Tooltip from "@/components/ui/Tooltip";
import { motion, AnimatePresence } from "framer-motion";
import {
    FaTwitter,
    FaTelegram,
    FaGlobe,
    FaCheckCircle,
    FaLink,
    FaChartLine,
    FaInstagram,
    FaTiktok,
    FaDiscord,
    FaYoutube,
    FaCoins,
    FaExternalLinkAlt,
    FaHeart,
    FaStar,
    FaCrown,
    FaRocket,
    FaShieldAlt,
} from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import Link from "next/link";
import Head from "next/head";

// Enhanced Types based on the actual models
interface Social {
    type: string;
    url: string;
}

interface Website {
    label: string;
    url: string;
}

interface Token {
    contractAddress: string;
    chain: string;
    name: string;
    logo: string;
    symbol: string;
    websites: Website[];
    socials: Social[];
}

interface SocialMedia {
    website?: string;
    twitter?: string;
    instagram?: string;
    telegram?: string;
    tiktok?: string;
    discord?: string;
    coinmarketcap?: string;
    coingecko?: string;
    youtube?: string;
}

interface TeamPage {
    owner: string;
    createdAt: number;
    updatedAt: number;
    id: string;
    displayName: string;
    pageBio: string;
    domainName: string;
    pageLogo: string;
    banner: string;
    pageTheme: "dark" | "light";
    pageColor: string;
    pageType: "content_creator" | "token_page";
    xVerified: boolean;
    tokenData: Token;
    favoriteTokens: Token[];
    group: string;
    socialMedia?: SocialMedia;
    customSocialMedia?: Social[];
}

// Social media configuration
const SOCIAL_CONFIG = {
    twitter: { icon: FaXTwitter, label: "Twitter", color: "#1DA1F2" },
    x: { icon: FaXTwitter, label: "X", color: "#000000" },
    telegram: { icon: FaTelegram, label: "Telegram", color: "#0088cc" },
    instagram: { icon: FaInstagram, label: "Instagram", color: "#E4405F" },
    tiktok: { icon: FaTiktok, label: "TikTok", color: "#000000" },
    discord: { icon: FaDiscord, label: "Discord", color: "#5865F2" },
    youtube: { icon: FaYoutube, label: "YouTube", color: "#FF0000" },
    website: { icon: FaGlobe, label: "Website", color: "#3B82F6" },
    coinmarketcap: { icon: FaCoins, label: "CoinMarketCap", color: "#3861FB" },
    coingecko: { icon: FaCoins, label: "CoinGecko", color: "#8DC647" },
};

// Animation variants
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2,
        },
    },
};

const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut",
        },
    },
};

// Social Links Component
function SocialLinks({ socialMedia, customSocialMedia }: {
    socialMedia?: SocialMedia;
    customSocialMedia?: Social[]
}) {
    const socialLinks: Array<{ type: string; url: string; label: string }> = [];

    // Add standard social media
    if (socialMedia) {
        Object.entries(socialMedia).forEach(([key, url]) => {
            if (url && SOCIAL_CONFIG[key as keyof typeof SOCIAL_CONFIG]) {
                socialLinks.push({
                    type: key,
                    url,
                    label: SOCIAL_CONFIG[key as keyof typeof SOCIAL_CONFIG].label,
                });
            }
        });
    }

    // Add custom social media
    if (customSocialMedia) {
        customSocialMedia.forEach((social) => {
            if (social.url) {
                socialLinks.push({
                    type: social.type,
                    url: social.url,
                    label: social.type.charAt(0).toUpperCase() + social.type.slice(1),
                });
            }
        });
    }

    if (socialLinks.length === 0) return null;

    return (
        <motion.div
            className="flex gap-4 mt-6 flex-wrap justify-center"
            variants={itemVariants}
        >
            {socialLinks.map((social, index) => {
                const config = SOCIAL_CONFIG[social.type as keyof typeof SOCIAL_CONFIG];
                const Icon = config?.icon || FaLink;

                return (
                    <Tooltip key={index} content={social.label}>
                        <motion.a
                            href={social.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-3 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 hover:scale-110"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            style={{
                                '--hover-color': config?.color || '#3B82F6'
                            } as React.CSSProperties}
                        >
                            <Icon className="text-xl text-white group-hover:text-[var(--hover-color)] transition-colors" />
                            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                        </motion.a>
                    </Tooltip>
                );
            })}
        </motion.div>
    );
}

// Website Links Component
function WebsiteLinks({ websites }: { websites: Website[] }) {
    if (!websites || websites.length === 0) return null;

    return (
        <motion.div
            className="flex flex-col gap-3 mt-6 w-full max-w-md mx-auto"
            variants={itemVariants}
        >
            {websites.map((website, index) => (
                <motion.a
                    key={index}
                    href={website.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group relative overflow-hidden rounded-xl bg-gradient-to-r from-white/10 to-white/5 hover:from-white/20 hover:to-white/10 p-4 transition-all duration-300 border border-white/10 hover:border-white/20"
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                >
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <FaGlobe className="text-lg text-white/70 group-hover:text-white transition-colors" />
                            <span className="font-medium text-white group-hover:text-white/90 transition-colors">
                                {website.label}
                            </span>
                        </div>
                        <FaExternalLinkAlt className="text-sm text-white/50 group-hover:text-white/70 transition-colors" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                </motion.a>
            ))}
        </motion.div>
    );
}

// Enhanced Token Card Component
function TokenCard({ token, isMain = false, isLight = false, primaryColor }: { token: Token; isMain?: boolean; isLight?: boolean; primaryColor: string }) {
    const [dexData, setDexData] = useState<any>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [imgError, setImgError] = useState(false);

    useEffect(() => {
        if (token.contractAddress && token.chain) {
            setIsLoading(true);
            getTokenData(token.contractAddress as `0x${string}`, token.chain)
                .then(setDexData)
                .catch(() => setDexData(null))
                .finally(() => setIsLoading(false));
        }
    }, [token.contractAddress, token.chain]);

    // Reset image error if token.logo changes
    useEffect(() => { setImgError(false); }, [token.logo]);

    return (
        <motion.div
            className={`relative overflow-hidden rounded-2xl p-6 border transition-all duration-300 ${isLight ? 'bg-white/90 border-gray-200' : 'bg-white/5 border-white/10 hover:border-white/20 hover:bg-white/10'}`}
            style={{ boxShadow: `0 4px 24px 0 ${primaryColor}33` }}
            variants={itemVariants}
            whileHover={{ scale: 1.02, y: -4 }}
            whileTap={{ scale: 0.98 }}
        >
            {isMain && (
                <div className="absolute top-3 right-3">
                    <FaCrown className="text-lg" style={{ color: primaryColor }} />
                </div>
            )}

            <div className="flex items-center gap-4 mb-4">
                <div className="relative">
                    {token.logo && !imgError ? (
                        <img
                            src={token.logo}
                            alt={token.name}
                            width={isMain ? 80 : 60}
                            height={isMain ? 80 : 60}
                            className="rounded-full border-2 border-white/20 shadow-lg"
                            onError={() => setImgError(true)}
                        />
                    ) : (
                        <div className={`rounded-full border-2 border-white/20 flex items-center justify-center text-2xl ${isLight ? 'text-gray-400' : 'text-white/60'} ${isMain ? 'w-20 h-20' : 'w-15 h-15'}`}>
                            {token.name?.[0] || '?'}
                        </div>
                    )}
                    {isMain && (
                        <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <FaCheckCircle className="text-white text-xs" />
                        </div>
                    )}
                </div>

                <div className="flex-1">
                    <div className={`font-orbitron text-lg font-bold flex items-center gap-2 ${isLight ? 'text-gray-900' : 'text-white'}`}>
                        {token.name}
                        <span className={`text-sm font-normal ${isLight ? 'text-gray-500' : 'text-white/60'}`}>{token.symbol}</span>
                    </div>
                    {token.chain && (
                        <div className={`text-xs mt-1 ${isLight ? 'text-gray-400' : 'text-white/50'}`}>
                            {token.chain.charAt(0).toUpperCase() + token.chain.slice(1)}
                        </div>
                    )}
                </div>
            </div>

            {/* Social Links for Token */}
            {token.socials && token.socials.length > 0 && (
                <div className="flex flex-row items-center gap-2 mb-4 flex-wrap">
                    {token.socials.map((social, index) => {
                        const config = SOCIAL_CONFIG[social.type as keyof typeof SOCIAL_CONFIG];
                        const Icon = config?.icon || FaLink;
                        return (
                            <Tooltip key={index} content={social.type}>
                                <a
                                    href={social.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors flex items-center justify-center min-w-[36px] min-h-[36px] group"
                                    aria-label={social.type}
                                    style={{ color: undefined }}
                                >
                                    <Icon className="text-base text-white/70 group-hover:text-[var(--primary-color)] transition-colors" style={{ '--primary-color': primaryColor } as React.CSSProperties} />
                                </a>
                            </Tooltip>
                        );
                    })}
                </div>
            )}

            {/* Token Websites */}
            {token.websites && token.websites.length > 0 && (
                <div className="space-y-2 mb-4">
                    {token.websites.map((website, index) => (
                        <a
                            key={index}
                            href={website.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block text-sm text-blue-400 hover:text-blue-300 transition-colors flex items-center gap-2"
                        >
                            <FaGlobe className="text-xs" />
                            {website.label}
                        </a>
                    ))}
                </div>
            )}

            {/* Dexscreener Link */}
            {dexData && (
                <motion.a
                    href={dexData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 px-4 py-2 rounded-lg text-white text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
                    style={{ background: primaryColor, boxShadow: `0 2px 8px 0 ${primaryColor}55` }}
                    whileHover={{ scale: 1.05, background: primaryColor + 'cc' }}
                    whileTap={{ scale: 0.95 }}
                >
                    <FaChartLine />
                    View on Dexscreener
                </motion.a>
            )}

            {isLoading && (
                <div className="absolute inset-0 bg-black/50 rounded-2xl flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                </div>
            )}
        </motion.div>
    );
}

// Main Component
export default function TeamsPage() {
    const { teamsPage } = useParams<{ teamsPage: string }>();
    const [data, setData] = useState<TeamPage | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!teamsPage) return;
        setLoading(true);
        getTeam(teamsPage as string)
            .then(setData)
            .catch(() => setError("Page not found"))
            .finally(() => setLoading(false));
    }, [teamsPage]);

    if (loading || error || !data) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-white dark:bg-black">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-neutral-400 dark:border-white mx-auto mb-4"></div>
                    <div className="text-neutral-700 dark:text-white text-lg font-orbitron">Loading Profile...</div>
                </div>
            </div>
        );
    }

    if (error || !data) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-white dark:bg-black">
                <div className="text-center">
                    <div className="text-red-400 text-6xl mb-4">⚠️</div>
                    <div className="text-neutral-800 dark:text-white text-xl font-orbitron mb-2">Profile Not Found</div>
                    <div className="text-neutral-500 dark:text-white/60">The profile you're looking for doesn't exist</div>
                </div>
            </div>
        );
    }

    // Theme configuration
    const isLight = data.pageTheme === "light";
    const primaryColor = data.pageColor || "#3c90dd";
    const cardTheme = isLight
        ? "bg-white/90 backdrop-blur-md border-gray-200"
        : "bg-neutral-900/90 backdrop-blur-md border-white/10";

    const title = data.displayName || "Anubis Team Page";
    const description = data.pageBio || "Discover this Anubis team profile, tokens, and links.";

    return (
        <>
            <Head>
                <title>{title}</title>
                <meta name="description" content={description} />
            </Head>
            <div
                className="min-h-screen w-full relative transition-all duration-500 bg-white dark:bg-black"
            >
                {/* Banner Image */}
                {data.banner ? (
                    <div className="relative w-full h-48 md:h-64 lg:h-80 overflow-hidden">
                        <img
                            src={data.banner}
                            alt="Banner"
                            className="absolute inset-0 w-full h-full object-cover object-center animate-fadein"
                            style={{ zIndex: 1 }}
                        />
                        {/* Bottom overlay for content separation */}
                        <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-black/60 to-transparent pointer-events-none" style={{ zIndex: 2 }} />
                    </div>
                ) : (
                    <div className="w-full h-24 md:h-32 lg:h-40" />
                )}

                <div className="relative z-10 flex flex-col items-center -mt-20 md:-mt-28">
                    <motion.div
                        className="w-full max-w-2xl mx-auto px-4 py-8"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        {/* Profile Card */}
                        <motion.div
                            className={`${cardTheme} rounded-3xl p-8 mb-8 border relative`}
                            style={{ zIndex: 3, boxShadow: `0 8px 32px 0 ${primaryColor}33` }}
                            variants={itemVariants}
                        >
                            {/* Profile Header */}
                            <div className="relative flex flex-col items-center text-center">
                                {/* Profile Picture */}
                                <div className="relative -mt-20 mb-4">
                                    {data.pageLogo ? (
                                        <img
                                            src={data.pageLogo}
                                            alt={data.displayName}
                                            width={120}
                                            height={120}
                                            className="rounded-full border-4 border-white shadow-2xl bg-white object-cover object-center"
                                        />
                                    ) : (
                                        <div className="w-30 h-30 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 border-4 border-white shadow-2xl flex items-center justify-center">
                                            <span className="text-4xl font-bold text-white">
                                                {data.displayName?.[0] || 'A'}
                                            </span>
                                        </div>
                                    )}
                                    {/* Verification Badge */}
                                    {data.xVerified && (
                                        <div className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center shadow-lg" style={{ background: primaryColor }}>
                                            <FaCheckCircle className="text-white text-sm" />
                                        </div>
                                    )}
                                </div>
                                {/* Name and Bio */}
                                <div className="mb-6">
                                    <span className={`block text-3xl md:text-4xl font-orbitron font-bold mb-2 ${isLight ? 'text-gray-900' : 'text-white'}`}>
                                        <GlitchText text={data.displayName} />
                                    </span>
                                    {data.pageBio && (
                                        <p className={`text-lg max-w-md leading-relaxed ${isLight ? 'text-gray-700' : 'text-white/70'}`}>{data.pageBio}</p>
                                    )}
                                </div>
                                {/* Social Links */}
                                <SocialLinks
                                    socialMedia={data.socialMedia}
                                    customSocialMedia={data.customSocialMedia}
                                />
                            </div>
                        </motion.div>
                        {/* Token Section */}
                        <AnimatePresence>
                            {data.pageType === "token_page" && data.tokenData && data.tokenData.contractAddress && (
                                <motion.div
                                    className={`${cardTheme} rounded-3xl p-8 mb-8 border relative`}
                                    style={{ boxShadow: `0 4px 24px 0 ${primaryColor}33` }}
                                    variants={itemVariants}
                                    initial="hidden"
                                    animate="visible"
                                    exit="hidden"
                                >
                                    <div className="text-center mb-6">
                                        <div className="flex items-center justify-center gap-2 mb-2">
                                            <FaRocket className="text-2xl" style={{ color: primaryColor }} />
                                            <h2 className={`text-2xl font-orbitron font-bold ${isLight ? 'text-gray-900' : 'text-white'}`}>Featured Token</h2>
                                        </div>
                                        <p className={`${isLight ? 'text-gray-600' : 'text-white/60'}`}>The main token for this project</p>
                                    </div>
                                    <div className="flex justify-center">
                                        <TokenCard token={data.tokenData} isMain={true} isLight={isLight} primaryColor={primaryColor} />
                                    </div>
                                </motion.div>
                            )}
                            {data.pageType === "content_creator" && data.favoriteTokens && data.favoriteTokens.length > 0 && (
                                <motion.div
                                    className={`${cardTheme} rounded-3xl p-8 mb-8 border relative`}
                                    style={{ boxShadow: `0 4px 24px 0 ${primaryColor}33` }}
                                    variants={itemVariants}
                                    initial="hidden"
                                    animate="visible"
                                    exit="hidden"
                                >
                                    <div className="text-center mb-6">
                                        <div className="flex items-center justify-center gap-2 mb-2">
                                            <FaHeart className="text-2xl" style={{ color: primaryColor }} />
                                            <h2 className={`text-2xl font-orbitron font-bold ${isLight ? 'text-gray-900' : 'text-white'}`}>Favorite Tokens</h2>
                                        </div>
                                        <p className={`${isLight ? 'text-gray-600' : 'text-white/60'}`}>Tokens recommended by this creator</p>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {data.favoriteTokens
                                            .filter(token => token.contractAddress)
                                            .map((token, index) => (
                                                <TokenCard key={index} token={token} isLight={isLight} primaryColor={primaryColor} />
                                            ))}
                                    </div>
                                </motion.div>
                            )}
                            {/* Website Links */}
                            {data.tokenData && data.tokenData.websites && data.tokenData.websites.length > 0 && (
                                <motion.div
                                    className={`${cardTheme} rounded-3xl p-8 mb-8 border relative`}
                                    style={{ boxShadow: `0 4px 24px 0 ${primaryColor}33` }}
                                    variants={itemVariants}
                                    initial="hidden"
                                    animate="visible"
                                    exit="hidden"
                                >
                                    <div className="text-center mb-6">
                                        <div className="flex items-center justify-center gap-2 mb-2">
                                            <FaGlobe className="text-2xl" style={{ color: primaryColor }} />
                                            <h2 className={`text-2xl font-orbitron font-bold ${isLight ? 'text-gray-900' : 'text-white'}`}>Important Links</h2>
                                        </div>
                                        <p className={`${isLight ? 'text-gray-600' : 'text-white/60'}`}>Official websites and resources</p>
                                    </div>
                                    <WebsiteLinks websites={data.tokenData.websites} />
                                </motion.div>
                            )}
                        </AnimatePresence>
                        {/* Footer */}
                        <motion.div
                            className="text-center py-8"
                            variants={itemVariants}
                        >
                            <div className="flex items-center justify-center gap-2 text-sm" style={{ color: primaryColor }}>
                                <FaShieldAlt />
                                <span>Powered by</span>
                                <Link
                                    href="https://anubis.bot"
                                    className="hover:underline font-semibold"
                                    style={{ color: primaryColor }}
                                >
                                    Anubis
                                </Link>
                            </div>
                        </motion.div>
                    </motion.div>
                </div>
            </div>
        </>
    );
}