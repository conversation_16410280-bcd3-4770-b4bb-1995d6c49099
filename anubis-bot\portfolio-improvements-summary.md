# Portfolio Display Improvements

## Issues Fixed

1. **Fixed Null Price Display**: 
   - Modified the `convertToUSD` function to handle "null" string values
   - Added additional checks to prevent displaying "$null" in the portfolio

2. **Improved Token Links**:
   - Changed token links in the portfolio to open the trade inquiry flow instead of going to DexScreener
   - Implemented a custom command handler for the trade links

## Implementation Details

### 1. Price Display Fix

- Updated the `convertToUSD` function in `libs/common.ts` to handle "null" string values:
  ```typescript
  if (numberString === undefined || numberString === null || numberString === "null") {
    return "Not available";
  }
  ```

### 2. Token Links Improvement

- Modified the portfolio display to create clickable token names that trigger the trade flow:
  ```typescript
  // Store token index in session data if not already there
  let tokenIndex = ctx.session.data.tokens.findIndex(t => t === token);
  if (tokenIndex === -1) {
    ctx.session.data.tokens.push(token);
    tokenIndex = ctx.session.data.tokens.length - 1;
  }
  
  // Create a callback data for the token to open trade inquiry
  return `🔹 [${tokenMeta.quoteToken.name}](tg://bot_command?command=trade&token=${tokenIndex}): ${priceDisplay} (${tokenMeta.priceChange.h24}% in 24hr) - ${balance} ${tokenMeta.quoteToken.symbol}`;
  ```

- Added a command handler for the trade links:
  ```typescript
  // Add handler for trade command from portfolio links
  bot.command('trade', async (ctx) => {
    // Extract token index from the command parameters
    const params = ctx.message?.text?.split(' ')[1];
    if (!params) return;
    
    const tokenParam = params.split('=')[1];
    if (!tokenParam) return;
    
    const tokenIndex = parseInt(tokenParam);
    if (isNaN(tokenIndex) || tokenIndex < 0 || tokenIndex >= ctx.session.data.tokens.length) return;
    
    const tokenAddress = ctx.session.data.tokens[tokenIndex];
    
    // Set the token in the session
    ctx.session.token = tokenIndex.toString();
    
    // Show token info with trading options
    const msg = await send(ctx, ["🔍 Loading token info..."]);
    if (msg !== true) {
      sendTokenInfo(
        ctx,
        {
          chat: { id: msg?.chat?.id },
          message_id: msg?.message_id ?? 0,
        } as Message,
        tokenAddress
      );
    }
  });
  ```

## Benefits

1. **Better User Experience**: 
   - No more confusing "$null" or "$undefined" price displays
   - Consistent fallback to "Not available" when price data is missing

2. **Improved Workflow**:
   - Users can now click directly on token names to view details and trade options
   - Eliminates the need to manually paste token addresses
   - Creates a more intuitive and streamlined trading experience

3. **Reduced Friction**:
   - Users can go from portfolio view to trading in a single click
   - Keeps users within the bot interface rather than sending them to external websites
