"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { FaTwitter, FaTelegram, FaGlobe, FaSearch, FaEye, FaCopy, FaDiscord } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { BsArrowDownShort, BsArrowUpShort } from "react-icons/bs";
import Tooltip from "@/components/ui/Tooltip";
import { calculateFDV, getTimeAgo, getVolumeByTimeOption, getUniqueBuysByTimeOption, getUniqueSellsByTimeOption } from "@/utils/data";
import { useWeb3 } from "@/contexts/Web3Context";
import { toast } from 'react-toastify';
import { useRouter } from "next/navigation";

// Format price with appropriate precision
const formatPrice = (price: number): string => {
  if (Number(price) < 0.001) return Number(price).toFixed(6);
  if (Number(price) < 0.01) return Number(price).toFixed(5);
  if (Number(price) < 0.1) return Number(price).toFixed(4);
  if (Number(price) < 1) return Number(price).toFixed(4);
  return Number(price).toFixed(2);
};

// interface TokenTableProps {
//   data?: typeof sampleTokenData;
// }

export default function TokenTable({ data, selectedTime }: { data: any, selectedTime: string }) {
  const { selectedChain } = useWeb3();
  const { push } = useRouter();

  function copyTokenAddress(CA: string) {
    navigator.clipboard.writeText(CA).then(() => {
      toast.success("Address Copied");
    }).catch((err) => {
      toast.error("Failed to copy address");
      console.error("Failed to copy: ", err);
    });
  }

  return (
    <div className="w-full overflow-x-auto min-h-full overflow-y-hidden">
      <table className="w-full min-w-full text-left text-sm">
        <thead>
          <tr className="border-b border-white/10">
            <th className="py-2 px-3 font-medium text-white/70">Asset</th>
            <th className="py-2 px-3 font-medium text-white/70">Socials</th>
            <th className="py-2 px-3 font-medium text-white/70">Age</th>
            <th className="py-2 px-3 font-medium text-white/70">{selectedTime}</th>
            <th className="py-2 px-3 font-medium text-white/70">Price USD</th>
            <th className="py-2 px-3 font-medium text-white/70">
              <div className="flex items-center">
                <BsArrowDownShort className="mr-1" />
                Trades
              </div>
            </th>
            <th className="py-2 px-3 font-medium text-white/70">Volume USD</th>
            <th className="py-2 px-3 font-medium text-white/70">Market Cap</th>
          </tr>
        </thead>
        <tbody>
          {data.map((token: any, index:number) => (
            <tr key={index} className="border-b border-white/5 hover:bg-white/5 hover:cursor-pointer" onClick={() => push(`/terminal/trade/${selectedChain?.slug}/${token.details.address}`)}>
              <td className="py-1 px-3">
                <div className="flex items-center">
                  <div className="w-6 h-6 mr-2 relative">
                    {token.details.imageThumbUrl ? (
                      <img
                        src={token.details.imageThumbUrl}
                        alt={token.details.name}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-xs">
                        {token.details.name.charAt(0)}
                      </div>
                    )}
                    <div className="absolute -bottom-1 -right-1">
                      <img src={selectedChain?.logo} alt="" className="w-3 h-3 object-contain" />
                    </div>
                  </div>
                  <motion.button
                    className="hover:cursor-pointer"
                    whileHover={{
                      scale: 1.03
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      copyTokenAddress(token.details.address);
                    }}
                  >
                    <div className="font-medium">{token.details.name}</div>
                    <div className="text-xs text-white/50 flex items-center gap-x-2">{token.details.symbol} <FaCopy className="text-white text-xs" /></div>
                  </motion.button>
                </div>
              </td>
              <td className="py-1 px-3">
                <div className="flex space-x-2">
                  {token.socialLinks.twitter && (
                    <Tooltip content="Twitter">
                      <Link
                        href={token.socialLinks.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FaXTwitter className="text-white/70 hover:text-white" />
                      </Link>
                    </Tooltip>
                  )}
                  {token.socialLinks.telegram && (
                    <Tooltip content="Telegram">
                      <Link
                        href={token.socialLinks.telegram}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FaTelegram className="text-white/70 hover:text-white" />
                      </Link>
                    </Tooltip>
                  )}
                  {token.socialLinks.website && (
                    <Tooltip content="Website">
                      <Link
                        href={token.socialLinks.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FaGlobe className="text-white/70 hover:text-white" />
                      </Link>
                    </Tooltip>
                  )}
                  {token.socialLinks.discord && (
                    <Tooltip content="Discord">
                      <Link
                        href={token.socialLinks.discord}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FaDiscord className="text-white/70 hover:text-white" />
                      </Link>
                    </Tooltip>
                  )}
                </div>
              </td>
              <td className="py-1 px-3 text-white/70 text-md">{getTimeAgo(token.activity.createdAt)}</td>
              <td className="py-1 px-3">
                <span
                  className={`${Number(token.activity.change1) > 0 ? "text-green-500" : Number(token.activity.change1) < 0 ? "text-red-500" : "text-white/70"
                    }`}
                >
                  {Number(token.activity.change1) > 0 ? "+" : ""}
                  {Number(token.activity.change1).toFixed(1)}%
                </span>
              </td>
              <td className="py-1 px-3 font-mono">${token.marketData.priceUSD < 0.1 ? `0.${formatPrice(Number(token.marketData.priceUSD)).split(".")[1]}` : formatPrice(token.marketData.priceUSD)}</td>
              <td className="py-1 px-3">
                <div className="flex items-center">
                  <span className="mr-1 text-white/70">{token.activity.trades24h}</span>
                  <div className="h-[4px] flex items-center w-10 bg-white/20 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getUniqueBuysByTimeOption(token.activity, selectedTime) > 0 ? "bg-green-500" : "bg-red-500"}`}
                      style={{ width: `${Math.min(100, (getUniqueBuysByTimeOption(token.activity, selectedTime) / 700) * 100)}%` }}
                    ></div>
                    <div
                      className={`h-full ${getUniqueSellsByTimeOption(token.activity, selectedTime) > 0 ? "bg-red-500" : "bg-green-500"}`}
                      style={{ width: `${Math.max(0, 100 - (getUniqueSellsByTimeOption(token.activity, selectedTime) / 700) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </td>
              <td className="py-1 px-3 font-mono">${getVolumeByTimeOption(token.activity, selectedTime).toLocaleString()}</td>
              <td className="py-1 px-3 text-left">
                <span className="text-white/70">${Number(token.marketData.marketCap).toLocaleString()}</span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
