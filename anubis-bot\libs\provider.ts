import { ethers } from "ethers";
import { ESupportedChains, getConfig } from "../config";

// Provider cache to avoid creating new providers for each request
const providerCache = new Map<ESupportedChains, ethers.providers.BaseProvider>();

// Provider Functions
export function getProvider(
  chainId: ESupportedChains
): ethers.providers.BaseProvider {
  // Check if we have a cached provider
  if (providerCache.has(chainId)) {
    return providerCache.get(chainId)!;
  }

  // Create a new provider and cache it
  const provider = new ethers.providers.JsonRpcProvider(
    getConfig(chainId).chain.rpcUrls.default.http[0],
    getConfig(chainId).chain.id
  );

  providerCache.set(chainId, provider);
  return provider;
}
