"use client";
import { useWeb3 } from "@/contexts/Web3Context";
import { getTimeAgo } from "@/utils/data";
import React from "react";

export default function RecentlyViewed() {
    const { recentlyViewed, selectedChain } = useWeb3();
    const filtered = recentlyViewed.filter(token => token.chain === selectedChain?.slug);
    return (
        <div className="row-span-1 w-full p-3 min-h-[30%] overflow-auto scrollbar-hidden border-b border-white/20">
            <h5 className="font-space-grotesk text-xs font-bold">Recently Viewed</h5>
            <div className="overflow-x-scroll scrollbar-hidden">
                <table className="w-full min-w-[420px] text-left text-xs border-separate border-spacing-y-1">
                    <thead>
                        <tr className="text-white/60">
                            <th className="px-2 py-1 font-semibold">Asset</th>
                            <th className="px-2 py-1 font-semibold">Price</th>
                            <th className="px-2 py-1 font-semibold">Market Cap</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filtered.map((token, index) => {
                            const tokenData = token.data?.results?.[0];
                            if (!tokenData) return null;
                            const { details, marketData, activity } = tokenData;
                            return (
                                <tr key={index} className="hover:bg-white/5 rounded transition cursor-pointer">
                                    <td className="px-2 py-1">
                                        <div className="flex items-center gap-2">
                                            {details?.imageThumbUrl ? (
                                                <div className="w-5 h-5 relative">
                                                    <img src={details.imageThumbUrl} alt={details.name} className="w-5 h-5 rounded-full" />
                                                    <img src={selectedChain?.logo} alt="" className="w-2.5 h-2.5 absolute -bottom-1 -right-1" />
                                                </div>
                                            ) : (
                                                <div className="w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center text-[10px] relative">
                                                    {details?.name?.charAt(0) ?? '?'}
                                                    <img src={selectedChain?.logo} alt="" className="w-2.5 h-2.5 absolute -bottom-1 -right-1" />
                                                </div>
                                            )}
                                            <div className="flex flex-col">
                                                <span className="font-medium text-white truncate max-w-[70px]">{details?.name ?? 'Unknown'}</span>
                                                <span className="text-white/40 text-[10px]">{details?.symbol ?? ''}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-2 py-1 text-white/70">{marketData?.priceUSD ? `$${parseFloat(marketData.priceUSD).toLocaleString(undefined, { maximumFractionDigits: 6 })}` : '-'}</td>
                                    <td className="px-2 py-1 text-white/70">{marketData?.marketCap ? `$${parseFloat(marketData.marketCap).toLocaleString()}` : '-'}</td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    )
}
