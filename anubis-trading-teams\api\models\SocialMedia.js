/**
 * SocialMedia.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
  attributes: {
    page: {
      model: "page",
      unique: true,
    },
    website: {
      type: "string",
    },
    twitter: {
      type: "string",
    },
    instagram: {
      type: "string",
    },
    telegram: {
      type: "string",
    },
    tiktok: {
      type: "string",
    },
    discord: {
      type: "string",
    },
    coinmarketcap: {
      type: "string",
    },
    coingecko: {
      type: "string",
    },
    youtube: {
      type: "string",
    },
  },
};
