{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/FeedStatus/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { FaCircle } from \"react-icons/fa6\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface FeedStatusType {\r\n    status: \"LIVE\" | \"OFFLINE\" | \"UPDATING\";\r\n}\r\n\r\n\r\nexport default function FeedStatus({ status }: FeedStatusType) {\r\n    const statusMap = {\r\n        LIVE: <div className=\"flex gap-x-1 items-center\">\r\n            <motion.span\r\n                animate={{ scale: [0.8, 1.1, 0.8] }}\r\n                transition={{ duration: 1, repeat: Infinity, ease: \"easeInOut\" }}\r\n            >\r\n                <FaCircle className=\"text-green-500 text-xs\" />\r\n            </motion.span>\r\n            <span className=\"text-green-500 text-xs\">LIVE</span>\r\n        </div>,\r\n        OFFLINE: <div className=\"flex gap-x-1 items-center\">\r\n            <motion.span\r\n            >\r\n                <FaCircle className=\"text-red-500 text-xs\" />\r\n            </motion.span>\r\n            <span className=\"text-red-500 text-xs\">OFFLINE</span>\r\n        </div>,\r\n        UPDATING: <div className=\"flex gap-x-1 items-center\">\r\n            <motion.span\r\n                animate={{ scale: [0.8, 1.1, 0.8] }}\r\n                transition={{ duration: 1.2, repeat: Infinity, ease: \"easeInOut\" }}\r\n            >\r\n                <FaCircle className=\"text-yellow-500 text-xs\" />\r\n            </motion.span>\r\n            <span className=\"text-yellow-500 text-xs\">UPDATING</span>\r\n        </div>\r\n    };\r\n\r\n    return statusMap[status];\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AASe,SAAS,WAAW,EAAE,MAAM,EAAkB;IACzD,MAAM,YAAY;QACd,oBAAM,6LAAC;YAAI,WAAU;;8BACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBACR,SAAS;wBAAE,OAAO;4BAAC;4BAAK;4BAAK;yBAAI;oBAAC;oBAClC,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAY;8BAE/D,cAAA,6LAAC,kJAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAExB,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;;;;;;;QAE7C,uBAAS,6LAAC;YAAI,WAAU;;8BACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;8BAER,cAAA,6LAAC,kJAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAExB,6LAAC;oBAAK,WAAU;8BAAuB;;;;;;;;;;;;QAE3C,wBAAU,6LAAC;YAAI,WAAU;;8BACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBACR,SAAS;wBAAE,OAAO;4BAAC;4BAAK;4BAAK;yBAAI;oBAAC;oBAClC,YAAY;wBAAE,UAAU;wBAAK,QAAQ;wBAAU,MAAM;oBAAY;8BAEjE,cAAA,6LAAC,kJAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAExB,6LAAC;oBAAK,WAAU;8BAA0B;;;;;;;;;;;;IAElD;IAEA,OAAO,SAAS,CAAC,OAAO;AAC5B;KA9BwB", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TimeFilter/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { FaCaretDown } from \"react-icons/fa\";\r\n\r\nexport default function TimeFilter({ timeOptions, selectedTime, handleTimeSelect }: {\r\n    timeOptions: string[],\r\n    selectedTime: string,\r\n    handleTimeSelect: (time: string) => void\r\n}) {\r\n\r\n\r\n    return (\r\n        <div className=\"rounded-sm border border-white/10 flex\">\r\n            {timeOptions.map((time, index) => (\r\n                <button\r\n                    key={index}\r\n                    onClick={() => handleTimeSelect(time)}\r\n                    className={`px-2.5 py-1 rounded-sm hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? \"bg-white/10\" : \"\"\r\n                        }`}\r\n                >\r\n                    {time.toUpperCase()}\r\n                </button>\r\n            ))}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport function TimeFilterDropdown({ timeOptions, selectedTime, handleTimeSelect }: {\r\n    timeOptions: string[],\r\n    selectedTime: string,\r\n    handleTimeSelect: (time: string) => void\r\n}) {\r\n    const [isOpen, setIsOpen] = useState(false);\r\n    return (\r\n        <div className=\"relative flex items-center gap-x-1\">\r\n            <button\r\n                onClick={() => setIsOpen(!isOpen)}\r\n                className=\"px-2.5 py-1 rounded-sm border border-white/10 text-xs hover:bg-white/10 transition-colors flex items-center gap-x-1\"\r\n            >\r\n                {selectedTime.toUpperCase()}\r\n                <FaCaretDown className=\"text-white\" />\r\n            </button>\r\n            {isOpen && (\r\n                <div className=\"absolute top-8 left-0 mt-1 w-20 rounded-sm border border-white/10 bg-black z-10 flex flex-col gap-y-1\">\r\n                    {timeOptions.map((time, index) => (\r\n                        <motion.button\r\n                            key={index}\r\n                            onClick={() => {\r\n                                handleTimeSelect(time);\r\n                                setIsOpen(false);\r\n                            }}\r\n                            className={`px-2.5 py-1 hover:bg-white/10 transition-colors text-xs hover:cursor-pointer ${time === selectedTime ? \"bg-white/10\" : \"\"}`}\r\n                        >{time.toUpperCase()}\r\n                        </motion.button>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAI/E;IAGG,qBACI,6LAAC;QAAI,WAAU;kBACV,YAAY,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gBAEG,SAAS,IAAM,iBAAiB;gBAChC,WAAW,CAAC,wFAAwF,EAAE,SAAS,eAAe,gBAAgB,IACxI;0BAEL,KAAK,WAAW;eALZ;;;;;;;;;;AAUzB;KArBwB;AAuBjB,SAAS,mBAAmB,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAI/E;;IACG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBACG,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;oBAET,aAAa,WAAW;kCACzB,6LAAC,iJAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAE1B,wBACG,6LAAC;gBAAI,WAAU;0BACV,YAAY,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEV,SAAS;4BACL,iBAAiB;4BACjB,UAAU;wBACd;wBACA,WAAW,CAAC,6EAA6E,EAAE,SAAS,eAAe,gBAAgB,IAAI;kCACzI,KAAK,WAAW;uBANT;;;;;;;;;;;;;;;;AAajC;GAhCgB;MAAA", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TimeFilter/StatsFilter/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { IoFilter } from \"react-icons/io5\";\r\n\r\n\r\nexport default function StatsFilter() {\r\n\r\n    return (\r\n        <motion.button className=\"flex items-center gap-x-2 border border-white/30 px-2.5 py-1 rounded-sm  text-xs\"\r\n            whileTap={{\r\n                scale: 1.03\r\n            }}\r\n            whileHover={{\r\n                cursor: \"pointer\"\r\n            }}\r\n        >\r\n            <IoFilter className=\"text-white\" /> <span>Filter</span>\r\n        </motion.button>\r\n    )\r\n}\r\n\r\nfunction FilterDropdown() {\r\n    return (\r\n        <>\r\n\r\n\r\n        </>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IAEpB,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QAAC,WAAU;QACrB,UAAU;YACN,OAAO;QACX;QACA,YAAY;YACR,QAAQ;QACZ;;0BAEA,6LAAC,kJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAe;0BAAC,6LAAC;0BAAK;;;;;;;;;;;;AAGtD;KAdwB;AAgBxB,SAAS;IACL,qBACI;AAKR;MAPS", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/ui/Tooltip.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, ReactNode } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\ninterface TooltipProps {\r\n  children: ReactNode;\r\n  content: ReactNode;\r\n  position?: \"top\" | \"bottom\" | \"left\" | \"right\";\r\n  delay?: number;\r\n  className?: string;\r\n}\r\n\r\nexport default function Tooltip({\r\n  children,\r\n  content,\r\n  position = \"top\",\r\n  delay = 0.2,\r\n  className = \"\",\r\n}: TooltipProps) {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const showTooltip = () => {\r\n    if (timeoutRef.current) clearTimeout(timeoutRef.current);\r\n    timeoutRef.current = setTimeout(() => {\r\n      setIsVisible(true);\r\n      setIsMounted(true);\r\n    }, delay * 1000);\r\n  };\r\n\r\n  const hideTooltip = () => {\r\n    if (timeoutRef.current) clearTimeout(timeoutRef.current);\r\n    setIsVisible(false);\r\n    timeoutRef.current = setTimeout(() => {\r\n      setIsMounted(false);\r\n    }, 200);\r\n  };\r\n\r\n  // Position styles\r\n  const getPositionStyles = () => {\r\n    switch (position) {\r\n      case \"top\":\r\n        return \"bottom-full left-1/2 -translate-x-1/2 mb-2\";\r\n      case \"bottom\":\r\n        return \"top-full left-1/2 -translate-x-1/2 mt-2\";\r\n      case \"left\":\r\n        return \"right-full top-1/2 -translate-y-1/2 mr-2\";\r\n      case \"right\":\r\n        return \"left-full top-1/2 -translate-y-1/2 ml-2\";\r\n      default:\r\n        return \"bottom-full left-1/2 -translate-x-1/2 mb-2\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"relative inline-block\"\r\n      onMouseEnter={showTooltip}\r\n      onMouseLeave={hideTooltip}\r\n      onFocus={showTooltip}\r\n      onBlur={hideTooltip}\r\n    >\r\n      {children}\r\n      <AnimatePresence>\r\n        {isMounted && (\r\n          <motion.div\r\n            className={`absolute z-50 ${getPositionStyles()} ${className}`}\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: isVisible ? 1 : 0, scale: isVisible ? 1 : 0.8 }}\r\n            exit={{ opacity: 0, scale: 0.8 }}\r\n            transition={{ duration: 0.15 }}\r\n          >\r\n            <div className=\"bg-black border border-white/20 text-white text-xs rounded py-1 px-2 whitespace-nowrap\">\r\n              {content}\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAae,SAAS,QAAQ,EAC9B,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,QAAQ,GAAG,EACX,YAAY,EAAE,EACD;;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,cAAc;QAClB,IAAI,WAAW,OAAO,EAAE,aAAa,WAAW,OAAO;QACvD,WAAW,OAAO,GAAG,WAAW;YAC9B,aAAa;YACb,aAAa;QACf,GAAG,QAAQ;IACb;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW,OAAO,EAAE,aAAa,WAAW,OAAO;QACvD,aAAa;QACb,WAAW,OAAO,GAAG,WAAW;YAC9B,aAAa;QACf,GAAG;IACL;IAEA,kBAAkB;IAClB,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;;YAEP;0BACD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,cAAc,EAAE,oBAAoB,CAAC,EAAE,WAAW;oBAC9D,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS,YAAY,IAAI;wBAAG,OAAO,YAAY,IAAI;oBAAI;oBAClE,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,YAAY;wBAAE,UAAU;oBAAK;8BAE7B,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAOf;GArEwB;KAAA", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/components/Terminal/TokenTable/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\";\r\nimport { FaTwitter, FaTelegram, FaGlobe, FaSearch, FaEye, FaCopy, FaDiscord } from \"react-icons/fa\";\r\nimport { FaXTwitter } from \"react-icons/fa6\";\r\nimport { BsArrowDownShort, BsArrowUpShort } from \"react-icons/bs\";\r\nimport Tooltip from \"@/components/ui/Tooltip\";\r\nimport { calculateFDV, getTimeAgo, getVolumeByTimeOption, getUniqueBuysByTimeOption, getUniqueSellsByTimeOption } from \"@/utils/data\";\r\nimport { useWeb3 } from \"@/contexts/Web3Context\";\r\nimport { toast } from 'react-toastify';\r\nimport { useRouter } from \"next/navigation\";\r\n\r\n// Format price with appropriate precision\r\nconst formatPrice = (price: number): string => {\r\n  if (Number(price) < 0.001) return Number(price).toFixed(6);\r\n  if (Number(price) < 0.01) return Number(price).toFixed(5);\r\n  if (Number(price) < 0.1) return Number(price).toFixed(4);\r\n  if (Number(price) < 1) return Number(price).toFixed(4);\r\n  return Number(price).toFixed(2);\r\n};\r\n\r\n// interface TokenTableProps {\r\n//   data?: typeof sampleTokenData;\r\n// }\r\n\r\nexport default function TokenTable({ data, selectedTime }: { data: any, selectedTime: string }) {\r\n  const { selectedChain } = useWeb3();\r\n  const { push } = useRouter();\r\n\r\n  function copyTokenAddress(CA: string) {\r\n    navigator.clipboard.writeText(CA).then(() => {\r\n      toast.success(\"Address Copied\");\r\n    }).catch((err) => {\r\n      toast.error(\"Failed to copy address\");\r\n      console.error(\"Failed to copy: \", err);\r\n    });\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full overflow-x-auto min-h-full overflow-y-hidden\">\r\n      <table className=\"w-full min-w-full text-left text-sm\">\r\n        <thead>\r\n          <tr className=\"border-b border-white/10\">\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">Asset</th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">Socials</th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">Age</th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">{selectedTime}</th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">Price USD</th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">\r\n              <div className=\"flex items-center\">\r\n                <BsArrowDownShort className=\"mr-1\" />\r\n                Trades\r\n              </div>\r\n            </th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">Volume USD</th>\r\n            <th className=\"py-2 px-3 font-medium text-white/70\">Market Cap</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {data.map((token: any, index:number) => (\r\n            <tr key={index} className=\"border-b border-white/5 hover:bg-white/5 hover:cursor-pointer\" onClick={() => push(`/terminal/trade/${selectedChain?.slug}/${token.details.address}`)}>\r\n              <td className=\"py-1 px-3\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"w-6 h-6 mr-2 relative\">\r\n                    {token.details.imageThumbUrl ? (\r\n                      <img\r\n                        src={token.details.imageThumbUrl}\r\n                        alt={token.details.name}\r\n                        width={24}\r\n                        height={24}\r\n                        className=\"rounded-full\"\r\n                      />\r\n                    ) : (\r\n                      <div className=\"w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-xs\">\r\n                        {token.details.name.charAt(0)}\r\n                      </div>\r\n                    )}\r\n                    <div className=\"absolute -bottom-1 -right-1\">\r\n                      <img src={selectedChain?.logo} alt=\"\" className=\"w-3 h-3 object-contain\" />\r\n                    </div>\r\n                  </div>\r\n                  <motion.button\r\n                    className=\"hover:cursor-pointer\"\r\n                    whileHover={{\r\n                      scale: 1.03\r\n                    }}\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      copyTokenAddress(token.details.address);\r\n                    }}\r\n                  >\r\n                    <div className=\"font-medium\">{token.details.name}</div>\r\n                    <div className=\"text-xs text-white/50 flex items-center gap-x-2\">{token.details.symbol} <FaCopy className=\"text-white text-xs\" /></div>\r\n                  </motion.button>\r\n                </div>\r\n              </td>\r\n              <td className=\"py-1 px-3\">\r\n                <div className=\"flex space-x-2\">\r\n                  {token.socialLinks.twitter && (\r\n                    <Tooltip content=\"Twitter\">\r\n                      <Link\r\n                        href={token.socialLinks.twitter}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        onClick={(e) => e.stopPropagation()}\r\n                      >\r\n                        <FaXTwitter className=\"text-white/70 hover:text-white\" />\r\n                      </Link>\r\n                    </Tooltip>\r\n                  )}\r\n                  {token.socialLinks.telegram && (\r\n                    <Tooltip content=\"Telegram\">\r\n                      <Link\r\n                        href={token.socialLinks.telegram}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        onClick={(e) => e.stopPropagation()}\r\n                      >\r\n                        <FaTelegram className=\"text-white/70 hover:text-white\" />\r\n                      </Link>\r\n                    </Tooltip>\r\n                  )}\r\n                  {token.socialLinks.website && (\r\n                    <Tooltip content=\"Website\">\r\n                      <Link\r\n                        href={token.socialLinks.website}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        onClick={(e) => e.stopPropagation()}\r\n                      >\r\n                        <FaGlobe className=\"text-white/70 hover:text-white\" />\r\n                      </Link>\r\n                    </Tooltip>\r\n                  )}\r\n                  {token.socialLinks.discord && (\r\n                    <Tooltip content=\"Discord\">\r\n                      <Link\r\n                        href={token.socialLinks.discord}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        onClick={(e) => e.stopPropagation()}\r\n                      >\r\n                        <FaDiscord className=\"text-white/70 hover:text-white\" />\r\n                      </Link>\r\n                    </Tooltip>\r\n                  )}\r\n                </div>\r\n              </td>\r\n              <td className=\"py-1 px-3 text-white/70 text-md\">{getTimeAgo(token.activity.createdAt)}</td>\r\n              <td className=\"py-1 px-3\">\r\n                <span\r\n                  className={`${Number(token.activity.change1) > 0 ? \"text-green-500\" : Number(token.activity.change1) < 0 ? \"text-red-500\" : \"text-white/70\"\r\n                    }`}\r\n                >\r\n                  {Number(token.activity.change1) > 0 ? \"+\" : \"\"}\r\n                  {Number(token.activity.change1).toFixed(1)}%\r\n                </span>\r\n              </td>\r\n              <td className=\"py-1 px-3 font-mono\">${token.marketData.priceUSD < 0.1 ? `0.${formatPrice(Number(token.marketData.priceUSD)).split(\".\")[1]}` : formatPrice(token.marketData.priceUSD)}</td>\r\n              <td className=\"py-1 px-3\">\r\n                <div className=\"flex items-center\">\r\n                  <span className=\"mr-1 text-white/70\">{token.activity.trades24h}</span>\r\n                  <div className=\"h-[4px] flex items-center w-10 bg-white/20 rounded-full overflow-hidden\">\r\n                    <div\r\n                      className={`h-full ${getUniqueBuysByTimeOption(token.activity, selectedTime) > 0 ? \"bg-green-500\" : \"bg-red-500\"}`}\r\n                      style={{ width: `${Math.min(100, (getUniqueBuysByTimeOption(token.activity, selectedTime) / 700) * 100)}%` }}\r\n                    ></div>\r\n                    <div\r\n                      className={`h-full ${getUniqueSellsByTimeOption(token.activity, selectedTime) > 0 ? \"bg-red-500\" : \"bg-green-500\"}`}\r\n                      style={{ width: `${Math.max(0, 100 - (getUniqueSellsByTimeOption(token.activity, selectedTime) / 700) * 100)}%` }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td className=\"py-1 px-3 font-mono\">${getVolumeByTimeOption(token.activity, selectedTime).toLocaleString()}</td>\r\n              <td className=\"py-1 px-3 text-left\">\r\n                <span className=\"text-white/70\">${Number(token.marketData.marketCap).toLocaleString()}</span>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;AAeA,0CAA0C;AAC1C,MAAM,cAAc,CAAC;IACnB,IAAI,OAAO,SAAS,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC;IACxD,IAAI,OAAO,SAAS,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC;IACvD,IAAI,OAAO,SAAS,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC;IACtD,IAAI,OAAO,SAAS,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC;IACpD,OAAO,OAAO,OAAO,OAAO,CAAC;AAC/B;AAMe,SAAS,WAAW,EAAE,IAAI,EAAE,YAAY,EAAuC;;IAC5F,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEzB,SAAS,iBAAiB,EAAU;QAClC,UAAU,SAAS,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;YACrC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,GAAG,KAAK,CAAC,CAAC;YACR,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAM,WAAU;;8BACf,6LAAC;8BACC,cAAA,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;0CAIzC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;;;;;;8BAGxD,6LAAC;8BACE,KAAK,GAAG,CAAC,CAAC,OAAY,sBACrB,6LAAC;4BAAe,WAAU;4BAAgE,SAAS,IAAM,KAAK,CAAC,gBAAgB,EAAE,eAAe,KAAK,CAAC,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;;8CAC7K,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,OAAO,CAAC,aAAa,iBAC1B,6LAAC;wDACC,KAAK,MAAM,OAAO,CAAC,aAAa;wDAChC,KAAK,MAAM,OAAO,CAAC,IAAI;wDACvB,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;kEAG/B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,KAAK,eAAe;4DAAM,KAAI;4DAAG,WAAU;;;;;;;;;;;;;;;;;0DAGpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,WAAU;gDACV,YAAY;oDACV,OAAO;gDACT;gDACA,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,iBAAiB,MAAM,OAAO,CAAC,OAAO;gDACxC;;kEAEA,6LAAC;wDAAI,WAAU;kEAAe,MAAM,OAAO,CAAC,IAAI;;;;;;kEAChD,6LAAC;wDAAI,WAAU;;4DAAmD,MAAM,OAAO,CAAC,MAAM;4DAAC;0EAAC,6LAAC,iJAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAIhH,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,WAAW,CAAC,OAAO,kBACxB,6LAAC,sIAAA,CAAA,UAAO;gDAAC,SAAQ;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,MAAM,WAAW,CAAC,OAAO;oDAC/B,QAAO;oDACP,KAAI;oDACJ,SAAS,CAAC,IAAM,EAAE,eAAe;8DAEjC,cAAA,6LAAC,kJAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAI3B,MAAM,WAAW,CAAC,QAAQ,kBACzB,6LAAC,sIAAA,CAAA,UAAO;gDAAC,SAAQ;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,MAAM,WAAW,CAAC,QAAQ;oDAChC,QAAO;oDACP,KAAI;oDACJ,SAAS,CAAC,IAAM,EAAE,eAAe;8DAEjC,cAAA,6LAAC,iJAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAI3B,MAAM,WAAW,CAAC,OAAO,kBACxB,6LAAC,sIAAA,CAAA,UAAO;gDAAC,SAAQ;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,MAAM,WAAW,CAAC,OAAO;oDAC/B,QAAO;oDACP,KAAI;oDACJ,SAAS,CAAC,IAAM,EAAE,eAAe;8DAEjC,cAAA,6LAAC,iJAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAIxB,MAAM,WAAW,CAAC,OAAO,kBACxB,6LAAC,sIAAA,CAAA,UAAO;gDAAC,SAAQ;0DACf,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,MAAM,WAAW,CAAC,OAAO;oDAC/B,QAAO;oDACP,KAAI;oDACJ,SAAS,CAAC,IAAM,EAAE,eAAe;8DAEjC,cAAA,6LAAC,iJAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM/B,6LAAC;oCAAG,WAAU;8CAAmC,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ,CAAC,SAAS;;;;;;8CACpF,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCACC,WAAW,GAAG,OAAO,MAAM,QAAQ,CAAC,OAAO,IAAI,IAAI,mBAAmB,OAAO,MAAM,QAAQ,CAAC,OAAO,IAAI,IAAI,iBAAiB,iBACxH;;4CAEH,OAAO,MAAM,QAAQ,CAAC,OAAO,IAAI,IAAI,MAAM;4CAC3C,OAAO,MAAM,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;8CAG/C,6LAAC;oCAAG,WAAU;;wCAAsB;wCAAE,MAAM,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,EAAE,YAAY,OAAO,MAAM,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,YAAY,MAAM,UAAU,CAAC,QAAQ;;;;;;;8CACnL,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsB,MAAM,QAAQ,CAAC,SAAS;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAW,CAAC,OAAO,EAAE,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,QAAQ,EAAE,gBAAgB,IAAI,iBAAiB,cAAc;wDAClH,OAAO;4DAAE,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,QAAQ,EAAE,gBAAgB,MAAO,KAAK,CAAC,CAAC;wDAAC;;;;;;kEAE7G,6LAAC;wDACC,WAAW,CAAC,OAAO,EAAE,CAAA,GAAA,uHAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,QAAQ,EAAE,gBAAgB,IAAI,eAAe,gBAAgB;wDACnH,OAAO;4DAAE,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,AAAC,CAAA,GAAA,uHAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,QAAQ,EAAE,gBAAgB,MAAO,KAAK,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;;;;;;;8CAKxH,6LAAC;oCAAG,WAAU;;wCAAsB;wCAAE,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,QAAQ,EAAE,cAAc,cAAc;;;;;;;8CACxG,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAK,WAAU;;4CAAgB;4CAAE,OAAO,MAAM,UAAU,CAAC,SAAS,EAAE,cAAc;;;;;;;;;;;;;2BApH9E;;;;;;;;;;;;;;;;;;;;;AA4HrB;GA/JwB;;QACI,kIAAA,CAAA,UAAO;QAChB,qIAAA,CAAA,YAAS;;;KAFJ", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/hooks/useGetMarketData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { getTrendingTokens, getNewTokenPairs, getGainerTokenPairs } from '@/services/api/flooz';\r\n\r\ninterface UseGetMarketDataOptions {\r\n    networks: string | undefined;\r\n    time: string;\r\n    pollingInterval?: number; // in ms\r\n}\r\n\r\ninterface UseGetMarketDataResult<TokensType = any, PairsType = any> {\r\n    trendingTokens: TokensType | null;\r\n    newPairs: PairsType | null;\r\n    gainers: PairsType | null;\r\n    loading: boolean;\r\n    isRefetching: boolean;\r\n    error: Error | null;\r\n    refetch: () => void;\r\n}\r\n\r\nexport function useGetMarketData({ networks, time, pollingInterval }: UseGetMarketDataOptions): UseGetMarketDataResult {\r\n    const [trendingTokens, setTrendingTokens] = useState<any | null>(null);\r\n    const [newPairs, setNewPairs] = useState<any | null>(null);\r\n    const [gainers, setGainers] = useState<any | null>(null);\r\n    const [loading, setLoading] = useState<boolean>(false);\r\n    const [isRefetching, setIsRefetching] = useState<boolean>(false);\r\n    const [error, setError] = useState<Error | null>(null);\r\n    const pollingRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n    const fetchData = useCallback(async (isPolling = false) => {\r\n        if (!isPolling) {\r\n            setLoading(true);\r\n        } else {\r\n            setIsRefetching(true);\r\n        }\r\n        setError(null);\r\n        try {\r\n            const [tokens, pairs, gainers] = await Promise.all([\r\n                getTrendingTokens(networks as string, time),\r\n                getNewTokenPairs(networks as string, time),\r\n                getGainerTokenPairs(networks as string, time)\r\n            ]);\r\n            setTrendingTokens(tokens);\r\n            setNewPairs(pairs);\r\n            setGainers(gainers);\r\n        } catch (err) {\r\n            setError(err as Error);\r\n        } finally {\r\n            if (!isPolling) {\r\n                setLoading(false);\r\n            } else {\r\n                setIsRefetching(false);\r\n            }\r\n        }\r\n    }, [networks, time]);\r\n\r\n    // Initial fetch and refetch on param change\r\n    useEffect(() => {\r\n        fetchData();\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [fetchData]);\r\n\r\n    // Polling logic\r\n    useEffect(() => {\r\n        if (pollingInterval && pollingInterval > 0) {\r\n            pollingRef.current = setInterval(() => fetchData(true), pollingInterval);\r\n            return () => {\r\n                if (pollingRef.current) clearInterval(pollingRef.current);\r\n            };\r\n        }\r\n        return undefined;\r\n    }, [fetchData, pollingInterval]);\r\n\r\n    const refetch = useCallback(() => {\r\n        fetchData(true);\r\n    }, [fetchData]);\r\n\r\n    return {\r\n        trendingTokens,\r\n        newPairs,\r\n        gainers,\r\n        loading,\r\n        isRefetching,\r\n        error,\r\n        refetch,\r\n    };\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;;;;AAkBO,SAAS,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,EAA2B;;IACzF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO,YAAY,KAAK;YAClD,IAAI,CAAC,WAAW;gBACZ,WAAW;YACf,OAAO;gBACH,gBAAgB;YACpB;YACA,SAAS;YACT,IAAI;gBACA,MAAM,CAAC,QAAQ,OAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC/C,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAoB;oBACtC,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,UAAoB;oBACrC,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD,EAAE,UAAoB;iBAC3C;gBACD,kBAAkB;gBAClB,YAAY;gBACZ,WAAW;YACf,EAAE,OAAO,KAAK;gBACV,SAAS;YACb,SAAU;gBACN,IAAI,CAAC,WAAW;oBACZ,WAAW;gBACf,OAAO;oBACH,gBAAgB;gBACpB;YACJ;QACJ;kDAAG;QAAC;QAAU;KAAK;IAEnB,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN;QACA,uDAAuD;QAC3D;qCAAG;QAAC;KAAU;IAEd,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,IAAI,mBAAmB,kBAAkB,GAAG;gBACxC,WAAW,OAAO,GAAG;kDAAY,IAAM,UAAU;iDAAO;gBACxD;kDAAO;wBACH,IAAI,WAAW,OAAO,EAAE,cAAc,WAAW,OAAO;oBAC5D;;YACJ;YACA,OAAO;QACX;qCAAG;QAAC;QAAW;KAAgB;IAE/B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,UAAU;QACd;gDAAG;QAAC;KAAU;IAEd,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;GAlEgB", "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file://F%3A/WorkProjects/anubis/anubis-terminal-v2/src/app/terminal/trending/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n    useState, useEffect\r\n} from 'react';\r\nimport FeedStatus from \"@/components/Terminal/FeedStatus\"\r\nimport TimeFilter from \"@/components/Terminal/TimeFilter\"\r\nimport StatsFilter from \"@/components/Terminal/TimeFilter/StatsFilter\"\r\nimport TokenTable from \"@/components/Terminal/TokenTable\"\r\nimport { useWeb3 } from '@/contexts/Web3Context';\r\nimport { useGetMarketData } from '@/hooks/useGetMarketData';\r\nimport { TailSpin } from 'react-loader-spinner';\r\nimport { ImSpinner2 } from \"react-icons/im\";\r\n\r\nconst timeOptions = [\"1h\", \"4h\", \"12h\", \"24h\"];\r\ntype TimeOption = string;\r\n\r\nexport default function TrendingTokens() {\r\n    const { selectedChain } = useWeb3();\r\n    const [selectedTime, setSelectedTime] = useState<TimeOption>(\"24h\");\r\n    const { trendingTokens, loading, error, refetch } = useGetMarketData({ \r\n        networks: selectedChain?.slug, \r\n        time: selectedTime, \r\n        pollingInterval: 5000 \r\n    });\r\n\r\n    const isLoading = !selectedChain || loading;\r\n\r\n    const handleTimeSelect = (time: TimeOption) => {\r\n        setSelectedTime(time);\r\n    };\r\n\r\n    useEffect(() => {\r\n        document.title = \"🔥 Trending Tokens - Top Performing Crypto Assets | Anubis Terminal\";\r\n    }, []);\r\n\r\n    if (!selectedChain) {\r\n        return (\r\n            <div className=\"flex items-center justify-center min-h-[calc(100vh-80px)]\">\r\n                <ImSpinner2 className=\"animate-spin text-white text-2xl\" />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"min-h-[calc(100vh-80px)] flex flex-col items-center justify-center relative overflow-hidden px-4 bg-[#0a0a0a]\">\r\n                {/* Background elements */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-b from-[#0a0a0a] to-[#1a1a1a] z-0\"></div>\r\n\r\n                {/* Error container */}\r\n                <div className=\"relative z-10 flex flex-col items-center justify-center gap-8 max-w-4xl mx-auto text-center\">\r\n                    {/* Error icon */}\r\n                    <div className=\"w-24 h-24 rounded-full border-2 border-red-500 flex items-center justify-center mb-4\">\r\n                        <span className=\"text-red-500 text-5xl font-bold\">!</span>\r\n                    </div>\r\n\r\n                    {/* Error message */}\r\n                    <div className=\"space-y-4\">\r\n                        <h2 className=\"text-xl sm:text-2xl md:text-3xl font-orbitron font-bold text-white\">\r\n                            DATA FETCH ERROR\r\n                        </h2>\r\n                        <p className=\"text-gray-400 font-space-grotesk max-w-md mx-auto\">\r\n                            We're having trouble loading trending tokens. Please try again.\r\n                        </p>\r\n                    </div>\r\n\r\n                    {/* Error details */}\r\n                    <div className=\"w-full max-w-md bg-black/50 border border-white/20 rounded-md p-4 font-mono text-sm text-left\">\r\n                        <div className=\"flex items-center gap-2 mb-2\">\r\n                            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\r\n                            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\r\n                            <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\r\n                            <span className=\"ml-2 text-gray-400\">Terminal</span>\r\n                        </div>\r\n                        <div className=\"font-geist-mono text-white\">\r\n                            <span className=\"text-green-500\">anubis@terminal:~$</span> fetch.trending\r\n                            <br />\r\n                            <span className=\"text-red-500\">Error:</span> {error.message || \"Failed to fetch trending tokens\"}\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Retry button */}\r\n                    <button\r\n                        className=\"flex items-center gap-2 font-orbitron font-semibold bg-white text-black px-6 py-3 rounded-md hover:shadow-lg hover:shadow-white/20\"\r\n                        onClick={refetch}\r\n                    >\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                            <path fillRule=\"evenodd\" d=\"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\" clipRule=\"evenodd\" />\r\n                        </svg>\r\n                        Retry\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (isLoading) {\r\n        return (\r\n            <div className=\"flex justify-center items-center min-h-[calc(100vh-80px)]\">\r\n                <TailSpin\r\n                    visible={true}\r\n                    height=\"80\"\r\n                    width=\"80\"\r\n                    color=\"#FFF\"\r\n                    ariaLabel=\"tail-spin-loading\"\r\n                    radius=\"1\"\r\n                />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <section className=\"min-h-[calc(100vh-150px)] overflow-hidden p-3 md:p-10\">\r\n            <div className=\"flex flex-col md:flex-row justify-between w-full items-start md:items-center mb-4 gap-4 md:gap-0\">\r\n                <div className=\"flex flex-col md:flex-row items-start md:items-center gap-3\">\r\n                    <div className=\"flex items-center gap-x-2\">\r\n                        <h3 className=\"font-space-grotesk\">Trending</h3>\r\n                        <TimeFilter timeOptions={timeOptions} selectedTime={selectedTime} handleTimeSelect={handleTimeSelect} />\r\n                    </div>\r\n                    <StatsFilter />\r\n                </div>\r\n\r\n                <FeedStatus status=\"LIVE\" />\r\n            </div>\r\n\r\n            <div className=\"mt-4\">\r\n                {trendingTokens && <TokenTable data={trendingTokens} selectedTime={selectedTime}\r\n                />}\r\n            </div>\r\n        </section>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;AAcA,MAAM,cAAc;IAAC;IAAM;IAAM;IAAO;CAAM;AAG/B,SAAS;;IACpB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC7D,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;QACjE,UAAU,eAAe;QACzB,MAAM;QACN,iBAAiB;IACrB;IAEA,MAAM,YAAY,CAAC,iBAAiB;IAEpC,MAAM,mBAAmB,CAAC;QACtB,gBAAgB;IACpB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,SAAS,KAAK,GAAG;QACrB;mCAAG,EAAE;IAEL,IAAI,CAAC,eAAe;QAChB,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;IAGlC;IAEA,IAAI,OAAO;QACP,qBACI,6LAAC;YAAI,WAAU;;8BAEX,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAK,WAAU;0CAAkC;;;;;;;;;;;sCAItD,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,6LAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;sCAMrE,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;wCAAyB;sDAC1D,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAa;wCAAE,MAAM,OAAO,IAAI;;;;;;;;;;;;;sCAKvE,6LAAC;4BACG,WAAU;4BACV,SAAS;;8CAET,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAU,SAAQ;oCAAY,MAAK;8CACjF,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAuS,UAAS;;;;;;;;;;;gCACzU;;;;;;;;;;;;;;;;;;;IAM1B;IAEA,IAAI,WAAW;QACX,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC,+JAAA,CAAA,WAAQ;gBACL,SAAS;gBACT,QAAO;gBACP,OAAM;gBACN,OAAM;gBACN,WAAU;gBACV,QAAO;;;;;;;;;;;IAIvB;IAEA,qBACI,6LAAC;QAAQ,WAAU;;0BACf,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,6LAAC,wJAAA,CAAA,UAAU;wCAAC,aAAa;wCAAa,cAAc;wCAAc,kBAAkB;;;;;;;;;;;;0CAExF,6LAAC,uKAAA,CAAA,UAAW;;;;;;;;;;;kCAGhB,6LAAC,wJAAA,CAAA,UAAU;wBAAC,QAAO;;;;;;;;;;;;0BAGvB,6LAAC;gBAAI,WAAU;0BACV,gCAAkB,6LAAC,wJAAA,CAAA,UAAU;oBAAC,MAAM;oBAAgB,cAAc;;;;;;;;;;;;;;;;;AAKnF;GAnHwB;;QACM,kIAAA,CAAA,UAAO;QAEmB,mIAAA,CAAA,mBAAgB;;;KAHhD", "debugId": null}}]}