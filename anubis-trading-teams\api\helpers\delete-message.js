const axios = require('axios');
const retry = require('async-retry');

module.exports = {


  friendlyName: 'Delete message',


  description: '',


  inputs: {
    chatId: {
      type: 'string',
      description: 'The chat ID where the message is located',
      required: true
    },
    messageId: {
      type: 'number',
      description: 'The ID of the message to be deleted',
      required: true
    }

  },


  exits: {

    success: {
      description: 'All done.',
    },

  },


  fn: async function (inputs) {
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;
    const TELEGRAM_API = `https://api.telegram.org/bot${TOKEN}`;

    try {
      await retry(
        async () => {
          const response = await axios.post(`${TELEGRAM_API}/deleteMessage`, {
            chat_id: inputs.chatId,
            message_id: inputs.messageId
          });
          if (response.data.ok) {
            sails.log.info('Message deleted successfully:', response.data);
            return response;
          } else {
            throw new Error(
              `Error deleting message: ${response.data.description}`
            );
          }
        },
        {
          retries: 5,
          minTimeout: 500,
          maxTimeout: 2000,
        }
      );
    } catch (error) {
      sails.log.error('Error deleting message:', error.message);
      if (error.response) {
        sails.log.error('Error response data:', error.response.data);
      }
    }
  }


};
