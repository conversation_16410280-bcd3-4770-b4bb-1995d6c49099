"use client"
import { motion } from "framer-motion";
import { FaTelegram, FaWallet, FaRocket } from "react-icons/fa";

interface StepProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  step: number;
}

const Step = ({ icon, title, description, step }: StepProps) => {
  return (
    <motion.div
      className="flex flex-col md:flex-row items-center gap-6 relative"
      initial={{ opacity: 0, x: -50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: step * 0.2 }}
      viewport={{ once: true, margin: "-100px" }}
    >
      {/* Step number with animated border */}
      <div className="relative">
        <motion.div
          className="w-16 h-16 rounded-full bg-white/5 flex items-center justify-center text-2xl font-orbitron font-bold relative z-10"
          whileInView={{
            boxShadow: [
              "0 0 0 0px rgba(255, 255, 255, 0.3)",
              "0 0 0 4px rgba(255, 255, 255, 0.1)",
              "0 0 0 0px rgba(255, 255, 255, 0.3)"
            ]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "loop"
          }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {step}
        </motion.div>
        
        {/* Icon */}
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xl text-white"
          initial={{ scale: 0 }}
          whileInView={{ scale: 1 }}
          transition={{ 
            type: "spring", 
            stiffness: 260, 
            damping: 20,
            delay: 0.3 + step * 0.2 
          }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {icon}
        </motion.div>
      </div>
      
      {/* Content */}
      <div className="flex-1 md:ml-4">
        <h3 className="text-xl font-orbitron font-bold mb-2 text-white text-center md:text-left">{title}</h3>
        <p className="text-gray-400 font-space-grotesk text-center md:text-left">{description}</p>
      </div>
      
      {/* Connecting line */}
      {step < 3 && (
        <motion.div 
          className="hidden md:block absolute h-full w-px bg-white/10 left-8 top-16"
          initial={{ height: 0 }}
          whileInView={{ height: "calc(100% - 4rem)" }}
          transition={{ duration: 0.5, delay: step * 0.2 + 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        />
      )}
    </motion.div>
  );
};

export default function HowItWorks() {
  const steps = [
    {
      icon: <FaTelegram />,
      title: "Connect via Telegram",
      description: "Start by connecting your Telegram account to Anubis Terminal for secure and private access to the platform."
    },
    {
      icon: <FaWallet />,
      title: "Link Your Wallet",
      description: "Connect your preferred crypto wallet to access your funds and enable seamless trading across multiple blockchains."
    },
    {
      icon: <FaRocket />,
      title: "Start Trading",
      description: "Begin trading with lightning-fast execution, MEV protection, and advanced analytics at your fingertips."
    }
  ];

  return (
    <section className="py-20 px-4 md:px-8 lg:px-12 bg-[#0a0a0a]">
      <div className="max-w-4xl mx-auto">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
            How It Works
          </h2>
          <p className="text-gray-400 font-space-grotesk max-w-2xl mx-auto text-lg">
            Get started with Anubis Terminal in three simple steps
          </p>
        </motion.div>

        <div className="space-y-16">
          {steps.map((step, index) => (
            <Step
              key={index}
              icon={step.icon}
              title={step.title}
              description={step.description}
              step={index + 1}
            />
          ))}
        </div>
        
        <motion.div 
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <a 
            href="/login" 
            className="inline-flex items-center gap-2 font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 transition-all duration-300"
          >
            <FaTelegram className="text-[#0088cc]" />
            Get Started Now
          </a>
        </motion.div>
      </div>
    </section>
  );
}
