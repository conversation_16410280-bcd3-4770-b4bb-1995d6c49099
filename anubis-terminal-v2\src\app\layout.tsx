"use client";

import { useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, Orbitron, Space_Grotesk } from "next/font/google";
import "./globals.css";
import { Web3Provider } from "@/contexts/Web3Context";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastContainer } from "react-toastify";
import { motion } from "framer-motion";
import { IoMdClose } from "react-icons/io";
import { CookiesProvider } from 'react-cookie';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  display: "swap",
});

const spaceGrotesk = Space_Grotesk({
  variable: "--font-space-grotesk",
  subsets: ["latin"],
  display: "swap",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  useEffect(() => {
    document.title = "Anubis | Snipe and Sell Tokens On-Chain Instantly";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Snipe and Sell Tokens On-Chain Instantly');
    }
  }, []);

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${orbitron.variable} ${spaceGrotesk.variable} antialiased`}
      >
        <CookiesProvider>
          <AuthProvider>
            <Web3Provider>
              {children}
              <ToastContainer
                position="bottom-right"
                autoClose={3000}
                hideProgressBar={false}
                newestOnTop={true}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme="dark"
                toastClassName="font-orbitron bg-black/95 border border-white/20 rounded-sm backdrop-blur-xl shadow-lg shadow-black/20"
                className="text-xs tracking-wide"
                progressClassName="bg-gradient-to-r from-white/20 via-white to-white/20"
                closeButton={({ closeToast }) => (
                  <motion.button
                    className="text-white/70 hover:text-white transition-colors ml-auto"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={closeToast}
                  >
                    <IoMdClose className="text-xs" />
                  </motion.button>
                )}
              />
            </Web3Provider>
          </AuthProvider>
        </CookiesProvider>
      </body>
    </html>
  );
}
