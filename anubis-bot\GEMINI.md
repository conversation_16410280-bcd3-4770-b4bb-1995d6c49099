## Anubis Trading Bot

Anubis is a Telegram-based cryptocurrency trading bot that provides a comprehensive suite of tools for decentralized finance (DeFi) enthusiasts. The bot is designed to be intuitive and powerful, offering a seamless trading experience across multiple EVM-compatible blockchains.

### Key Features:

*   **Multi-Chain Functionality:** Anubis supports a variety of blockchains, including Base, Sonic, Avax, Bera, and Unichain, allowing users to trade assets across different ecosystems.
*   **Wallet Management:** The bot features robust wallet management capabilities, enabling users to generate new wallets, import existing ones via private keys, and effortlessly switch between multiple wallets.
*   **Advanced Trading Options:** Anubis integrates with leading DEX aggregators like 0x and Odos to provide optimal trade execution. Users can perform manual token swaps or utilize the auto-sniping feature to purchase newly listed tokens automatically.
*   **Portfolio and Token Insights:** The bot offers a clear overview of a user's portfolio, displaying token balances and their corresponding values. It also provides detailed information about specific tokens, including real-time price data, market capitalization, and liquidity.
*   **Referral Program:** Anubis includes a referral system that rewards users for inviting others to the platform. Referrers earn a percentage of the trading fees generated by their referees.
*   **Customizable Settings:** Users can tailor their trading experience by adjusting key parameters suchs as the network, slippage tolerance, and gas price to suit their individual strategies.
*   **Administrative Oversight:** The bot includes an admin-only dashboard that provides a high-level view of user activity and overall bot performance.
*   **Extensible API:** The bot is built with an accompanying API that allows for programmatic interaction with its core features, enabling developers to build custom integrations and extensions.
