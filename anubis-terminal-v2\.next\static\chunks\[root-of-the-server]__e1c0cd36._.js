(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[next]/internal/font/google/geist_35b02a57.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "geist_35b02a57-module__BOCH1a__className",
  "variable": "geist_35b02a57-module__BOCH1a__variable",
});
}}),
"[next]/internal/font/google/geist_35b02a57.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_35b02a57$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_35b02a57.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_35b02a57$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist', 'Geist Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_35b02a57$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_35b02a57$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/geist_mono_87bc4ef9.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "geist_mono_87bc4ef9-module__963okG__className",
  "variable": "geist_mono_87bc4ef9-module__963okG__variable",
});
}}),
"[next]/internal/font/google/geist_mono_87bc4ef9.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_87bc4ef9$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_mono_87bc4ef9.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_87bc4ef9$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Geist Mono', 'Geist Mono Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_87bc4ef9$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_87bc4ef9$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/orbitron_94041d18.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "orbitron_94041d18-module__LQRA4G__className",
  "variable": "orbitron_94041d18-module__LQRA4G__variable",
});
}}),
"[next]/internal/font/google/orbitron_94041d18.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$orbitron_94041d18$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/orbitron_94041d18.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$orbitron_94041d18$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Orbitron', 'Orbitron Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$orbitron_94041d18$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$orbitron_94041d18$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/space_grotesk_b1377d82.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "space_grotesk_b1377d82-module__c9cn1G__className",
  "variable": "space_grotesk_b1377d82-module__c9cn1G__variable",
});
}}),
"[next]/internal/font/google/space_grotesk_b1377d82.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$space_grotesk_b1377d82$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/space_grotesk_b1377d82.module.css [app-client] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$space_grotesk_b1377d82$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Space Grotesk', 'Space Grotesk Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$space_grotesk_b1377d82$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$space_grotesk_b1377d82$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/utils/data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "baseURL": (()=>baseURL),
    "calculateFDV": (()=>calculateFDV),
    "chains": (()=>chains),
    "getPriceChangeByTimeOption": (()=>getPriceChangeByTimeOption),
    "getShareLink": (()=>getShareLink),
    "getTimeAgo": (()=>getTimeAgo),
    "getUniqueBuysByTimeOption": (()=>getUniqueBuysByTimeOption),
    "getUniqueSellsByTimeOption": (()=>getUniqueSellsByTimeOption),
    "getVolumeByTimeOption": (()=>getVolumeByTimeOption),
    "globalNavLinks": (()=>globalNavLinks),
    "terminalNavLinks": (()=>terminalNavLinks),
    "tokenData": (()=>tokenData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const globalNavLinks = [
    {
        title: "Features",
        url: "#features"
    },
    {
        title: "Anubis Teams",
        url: "/anubis-teams"
    },
    {
        title: "Documentation",
        url: "/documentation"
    },
    {
        title: "Browser Extension",
        url: "#browser-extension"
    },
    {
        title: "Tutorials",
        url: "/tutorials"
    }
];
const terminalNavLinks = [
    {
        title: "Trending",
        url: "/terminal/trending"
    },
    {
        title: "Gainers",
        url: "/terminal/gainers"
    },
    {
        title: "New",
        url: "/terminal/new"
    },
    {
        title: "portfolio",
        url: "/terminal/portfolio"
    },
    {
        title: "Wallets",
        url: "/terminal/wallets"
    },
    {
        title: "Sniping",
        url: "/terminal/sniping"
    }
];
const tokenData = [
    {
        name: "Bitcoin",
        symbol: "BTC",
        price: "$20,000",
        change: "+2.5%",
        volume: "$100,000,000",
        marketCap: "$1,000,000,000",
        logo: "/logo/icon.png"
    }
];
const chains = [
    {
        chain: "Avax",
        logo: "/chains/avax.svg",
        slug: "avalanche",
        scanUrl: "https://snowtrace.io",
        chainId: 43114,
        symbol: "AVAX"
    },
    {
        chain: "Base",
        logo: "/chains/base.svg",
        slug: "base",
        scanUrl: "https://basescan.org",
        chainId: 8453,
        symbol: "ETH"
    },
    {
        chain: "BeraChain",
        logo: "/chains/berachain.svg",
        slug: "berachain",
        scanUrl: "https://artio.beratrail.io",
        chainId: 80094,
        symbol: "BERA"
    },
    {
        chain: "Sonic",
        logo: "/chains/sonic.svg",
        slug: "sonic",
        scanUrl: "https://explorer.sonic.ooo",
        chainId: 146,
        symbol: "S"
    },
    {
        chain: "Unichain",
        logo: "/chains/unichain.svg",
        slug: "unichain",
        scanUrl: "https://unichainscan.com",
        chainId: 130,
        symbol: "U ETH"
    }
];
function calculateFDV(totalSupplyStr, priceStr) {
    const totalSupply = parseFloat(totalSupplyStr);
    const priceUSD = parseFloat(priceStr);
    if (isNaN(totalSupply) || isNaN(priceUSD)) {
        throw new Error("Invalid number format in input");
    }
    const fdv = totalSupply * priceUSD;
    return parseFloat(fdv.toFixed(2)); // rounded to 2 decimals (USD convention)
}
function getTimeAgo(timestamp) {
    const now = Date.now() / 1000; // Current time in seconds
    const secondsAgo = now - timestamp;
    if (secondsAgo < 60) {
        return "<1M";
    }
    const minutes = Math.floor(secondsAgo / 60);
    if (minutes < 60) {
        return `${minutes}M`;
    }
    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
        return `${hours}H`;
    }
    const days = Math.floor(hours / 24);
    return `${days}D`;
}
function getVolumeByTimeOption(activity, timeOption) {
    const mapping = {
        "1h": "volume1",
        "4h": "volume4",
        "12h": "volume12",
        "24h": "volume24"
    };
    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }
    const volume = parseFloat(activity[key]);
    return isNaN(volume) ? 0 : volume;
}
function getPriceChangeByTimeOption(activity, timeOption) {
    const mapping = {
        "1h": "change1",
        "4h": "change4",
        "12h": "change12",
        "24h": "change24"
    };
    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }
    const change = parseFloat(activity[key]);
    return isNaN(change) ? 0 : change;
}
function getUniqueBuysByTimeOption(activity, timeOption) {
    const mapping = {
        "1h": "uniqueBuys1",
        "4h": "uniqueBuys4",
        "12h": "uniqueBuys12",
        "24h": "uniqueBuys24"
    };
    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }
    const value = activity[key];
    return typeof value === "number" ? value : 0;
}
function getUniqueSellsByTimeOption(activity, timeOption) {
    const mapping = {
        "1h": "uniqueSells1",
        "4h": "uniqueSells4",
        "12h": "uniqueSells12",
        "24h": "uniqueSells24"
    };
    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }
    const value = activity[key];
    return typeof value === "number" ? value : 0;
}
const baseURL = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "http://localhost:3000";
function getShareLink(chain, address) {
    return `${baseURL}/terminal/trade/${chain}/${address}`;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/bot/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": (()=>api),
    "generateNewWallet": (()=>generateNewWallet),
    "getNativeBalance": (()=>getNativeBalance),
    "getSettings": (()=>getSettings),
    "getUser": (()=>getUser),
    "getUserPortfolio": (()=>getUserPortfolio),
    "importNewWallet": (()=>importNewWallet),
    "send2FA": (()=>send2FA),
    "terminalAuth": (()=>terminalAuth),
    "tokenSearchByContract": (()=>tokenSearchByContract),
    "updateSettings": (()=>updateSettings),
    "verify2FA": (()=>verify2FA)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
// Export wallet functions directly
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/bot/wallets.ts [app-client] (ecmascript)");
;
;
const baseURL = "https://api.anubistrade.xyz";
console.log("Bot API URL:", baseURL);
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL
});
// Example interceptor: log requests
api.interceptors.request.use((config)=>{
    // You can add auth tokens or logging here
    // console.log("Request:", config);
    return config;
}, (error)=>Promise.reject(error));
// Example interceptor: log responses
api.interceptors.response.use((response)=>response, (error)=>{
    // You can handle global errors here
    return Promise.reject(error);
});
const terminalAuth = async (otp, tgUserId)=>{
    try {
        console.log("Axios Instance:", api);
        const response = await api.post("/auth/verify-otp", {
            otp,
            tgUserId
        });
        return response.data;
    } catch (error) {
        console.error("Error verifying OTP:", error);
        throw error;
    }
};
const getUser = async (token)=>{
    try {
        const response = await api.get("/bot/user", {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching tg user:", error);
        return {
            error: "Error fetching tg user"
        };
    }
};
;
const getSettings = async (token)=>{
    try {
        const response = await api.get("/bot/user/settings", {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching settings", error);
        throw error;
    }
};
const updateSettings = async (token, settings)=>{
    try {
        const response = await api.post("/bot/user/settings", settings, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error updating settings", error);
        throw error;
    }
};
const getNativeBalance = async (token)=>{
    try {
        const response = await api.get("/bot/user/wallets/balance", {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching native balance", error);
        throw error;
    }
};
const generateNewWallet = async (token)=>{
    try {
        const response = await api.post("/bot/wallet/create", {}, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error generating new wallet", error);
        throw error;
    }
};
const getUserPortfolio = async (token)=>{
    try {
        const response = await api.get("/bot/user/portfolio", {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching user portfolio", error);
        throw error;
    }
};
const send2FA = async (token)=>{
    try {
        const response = await api.post("/bot/user/2fa/send", {}, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error sending 2FA code", error);
        throw error;
    }
};
const verify2FA = async (token, code)=>{
    try {
        const response = await api.post("/bot/user/2fa/verify", {
            code
        }, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error verifying 2FA code", error);
        throw error;
    }
};
const importNewWallet = async (token, privateKey)=>{
    try {
        const response = await api.post("/bot/user/wallets/import", {
            privateKey
        }, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error importing new wallet", error);
        throw error;
    }
};
const tokenSearchByContract = async (tokenContractAddress, token)=>{
    try {
        const response = await api.post("/bot/user/token/search", {
            address: tokenContractAddress
        }, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        return {
            error: "Token not found or fetch failed",
            message: "Could not fetch token info. Please check the contract address and try again."
        };
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/bot/wallets.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createWallet": (()=>createWallet),
    "getWalletBalance": (()=>getWalletBalance),
    "getWalletPrivateKey": (()=>getWalletPrivateKey),
    "getWallets": (()=>getWallets),
    "importWallet": (()=>importWallet),
    "setPrimaryWallet": (()=>setPrimaryWallet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <locals>");
;
const getWallets = async (token)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["api"].get('/bot/user/wallets', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data.wallets || [];
    } catch (error) {
        console.error('Error fetching wallets:', error);
        throw new Error('Failed to fetch wallets');
    }
};
const createWallet = async (token)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["api"].post('/bot/user/wallet/create', {}, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error creating wallet:', error);
        throw new Error('Failed to create wallet');
    }
};
const importWallet = async (token, params)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["api"].post('/bot/user/wallets/import', {
            privateKey: params.privateKey
        }, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error importing wallet:', error);
        throw new Error('Failed to import wallet');
    }
};
const setPrimaryWallet = async (token, walletAddress)=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["api"].post('/bot/user/wallets/primary', {
            address: walletAddress
        }, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('Error setting primary wallet:', error);
        throw new Error('Failed to set primary wallet');
    }
};
const getWalletPrivateKey = async (token, walletAddress)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["api"].get(`/bot/user/wallets/private/${walletAddress}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error getting wallet private key:', error);
        throw new Error('Failed to get wallet private key');
    }
};
const getWalletBalance = async (token, walletAddress, chainId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["api"].get(`/bot/user/wallets/balance?address=${walletAddress}&chainId=${chainId}`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error getting wallet balance:', error);
        throw new Error('Failed to get wallet balance');
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/bot/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/bot/wallets.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-cookie/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/bot/wallets.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    _s();
    const [isLoggedIn, setIsLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [cookies, setCookie, removeCookie] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useCookies"])([
        'anubis_terminal'
    ]);
    const [token, setToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { push } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    function storeCookie(token) {
        setCookie('anubis_terminal', token, {
            path: '/',
            maxAge: 3600 * 24 * 7
        }); // 7 days
        setToken(token);
        setIsLoggedIn(true);
    }
    function logout() {
        removeCookie('anubis_terminal', {
            path: '/'
        });
        setToken(null);
        setIsLoggedIn(false);
        push('/login');
    }
    function cacheProfile(user) {
        window.localStorage.setItem("anubis_user", JSON.stringify(user));
        setUser(user);
    }
    function deleteProfile() {
        window.localStorage.removeItem("anubis_user");
        setUser(null);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            if (cookies.anubis_terminal) {
                setToken(cookies.anubis_terminal);
                setIsLoggedIn(true);
            }
            const user = window.localStorage.getItem("anubis_user");
            if (user) {
                setUser(JSON.parse(user));
            }
            // Make API Calls to Grab Info
            if (isLoggedIn) {
                setLoading(true);
                ({
                    "AuthProvider.useEffect": async ()=>{
                        try {
                            const [userData, wallets, settings, balance, portfolio] = await Promise.all([
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])(token),
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWallets"])(token),
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSettings"])(token),
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getNativeBalance"])(token),
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUserPortfolio"])(token)
                            ]);
                            console.log(userData, wallets, settings, balance, portfolio);
                            setData({
                                userData,
                                wallets,
                                settings,
                                balance,
                                portfolio
                            });
                        } catch (error) {
                            console.error("Error fetching user data", error);
                        } finally{
                            setLoading(false);
                        }
                    }
                })["AuthProvider.useEffect"]();
            }
        }
    }["AuthProvider.useEffect"], [
        cookies.anubis_terminal
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: {
            data,
            isLoggedIn,
            token,
            user,
            loading,
            logout,
            storeCookie,
            cacheProfile,
            deleteProfile
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 106,
        columnNumber: 9
    }, this);
};
_s(AuthProvider, "mqDqVFw5kNBk3qw5KYFX+KmXr1g=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useCookies"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AuthProvider;
const useAuth = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/api/flooz.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getGainerTokenPairs": (()=>getGainerTokenPairs),
    "getNewTokenPairs": (()=>getNewTokenPairs),
    "getTokenData": (()=>getTokenData),
    "getTokenPriceHistory": (()=>getTokenPriceHistory),
    "getTrendingTokens": (()=>getTrendingTokens)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
const getGainerTokenPairs = async (networks, time)=>{
    const url = "https://api.flooz.trade/v2/trend";
    const params = {
        networks: networks,
        trendType: 'gainers',
        volumePeriod: time
    };
    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${("TURBOPACK compile-time value", "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY3ZDhjZWU0ZTYwYmYwMzYxNmM1ODg4NTJiMjA5MTZkNjRjMzRmYmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oMCdyvMKVcKnzCWaFVhhYJYZk8OHQz7boNT69t8RPboistfJsHCD6SmQHzEKa8qge0Z1NF7l8JyJGIxgAq5pI1Oku6zsNxDjziMrzq5WTulDf1KVm6IcXQA0u9hkuiZn39QzKzxrymrZKfMoIwwsdgYjKALBK-h0QgHN8eFT4Rzb0C5PbXL3fFGjeLuNjMOR6f8DCGLCAW8IBceRGwew_Fuh1qUIZiK07jc9AO4motyhkhtpxK4axAd5Xttn2wJtLhVar7tCmmKLKZlVhmTd4S5KIgPropORclBh6bexW1ZJLrv5Bfc-MSbNr50IBZOYNLmhXwkqScIJiYI2RF-D5Q")}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
            params,
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching new trends:', error);
        throw error;
    }
};
const getNewTokenPairs = async (networks, time)=>{
    const url = "https://api.flooz.trade/v2/trend";
    const params = {
        networks: networks,
        trendType: 'newPairs',
        volumePeriod: time
    };
    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${("TURBOPACK compile-time value", "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY3ZDhjZWU0ZTYwYmYwMzYxNmM1ODg4NTJiMjA5MTZkNjRjMzRmYmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oMCdyvMKVcKnzCWaFVhhYJYZk8OHQz7boNT69t8RPboistfJsHCD6SmQHzEKa8qge0Z1NF7l8JyJGIxgAq5pI1Oku6zsNxDjziMrzq5WTulDf1KVm6IcXQA0u9hkuiZn39QzKzxrymrZKfMoIwwsdgYjKALBK-h0QgHN8eFT4Rzb0C5PbXL3fFGjeLuNjMOR6f8DCGLCAW8IBceRGwew_Fuh1qUIZiK07jc9AO4motyhkhtpxK4axAd5Xttn2wJtLhVar7tCmmKLKZlVhmTd4S5KIgPropORclBh6bexW1ZJLrv5Bfc-MSbNr50IBZOYNLmhXwkqScIJiYI2RF-D5Q")}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
            params,
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching new trends:', error);
        throw error;
    }
};
const getTrendingTokens = async (networks, time)=>{
    const url = "https://api.flooz.trade/v2/trend";
    const params = {
        networks: networks,
        volumePeriod: time
    };
    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${("TURBOPACK compile-time value", "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY3ZDhjZWU0ZTYwYmYwMzYxNmM1ODg4NTJiMjA5MTZkNjRjMzRmYmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oMCdyvMKVcKnzCWaFVhhYJYZk8OHQz7boNT69t8RPboistfJsHCD6SmQHzEKa8qge0Z1NF7l8JyJGIxgAq5pI1Oku6zsNxDjziMrzq5WTulDf1KVm6IcXQA0u9hkuiZn39QzKzxrymrZKfMoIwwsdgYjKALBK-h0QgHN8eFT4Rzb0C5PbXL3fFGjeLuNjMOR6f8DCGLCAW8IBceRGwew_Fuh1qUIZiK07jc9AO4motyhkhtpxK4axAd5Xttn2wJtLhVar7tCmmKLKZlVhmTd4S5KIgPropORclBh6bexW1ZJLrv5Bfc-MSbNr50IBZOYNLmhXwkqScIJiYI2RF-D5Q")}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
            params,
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching new trends:', error);
        throw error;
    }
};
const getTokenData = async (networks, address)=>{
    const url = "https://api.flooz.trade/v2/trend";
    const body = {
        filters: {
            networks: [
                networks
            ]
        },
        tokens: [
            address
        ]
    };
    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${("TURBOPACK compile-time value", "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY3ZDhjZWU0ZTYwYmYwMzYxNmM1ODg4NTJiMjA5MTZkNjRjMzRmYmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oMCdyvMKVcKnzCWaFVhhYJYZk8OHQz7boNT69t8RPboistfJsHCD6SmQHzEKa8qge0Z1NF7l8JyJGIxgAq5pI1Oku6zsNxDjziMrzq5WTulDf1KVm6IcXQA0u9hkuiZn39QzKzxrymrZKfMoIwwsdgYjKALBK-h0QgHN8eFT4Rzb0C5PbXL3fFGjeLuNjMOR6f8DCGLCAW8IBceRGwew_Fuh1qUIZiK07jc9AO4motyhkhtpxK4axAd5Xttn2wJtLhVar7tCmmKLKZlVhmTd4S5KIgPropORclBh6bexW1ZJLrv5Bfc-MSbNr50IBZOYNLmhXwkqScIJiYI2RF-D5Q")}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(url, body, {
            headers
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching token data:', error);
        throw error;
    }
};
const getTokenPriceHistory = async (network, address)=>{
    const url = `https://api.flooz.trade/v2/tokens/${address}/price`;
    // Get current date in ISO format
    const till = new Date().toISOString();
    const params = {
        network,
        till,
        countBack: 300,
        resolution: 5
    };
    const headers = {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${("TURBOPACK compile-time value", "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY3ZDhjZWU0ZTYwYmYwMzYxNmM1ODg4NTJiMjA5MTZkNjRjMzRmYmEiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oMCdyvMKVcKnzCWaFVhhYJYZk8OHQz7boNT69t8RPboistfJsHCD6SmQHzEKa8qge0Z1NF7l8JyJGIxgAq5pI1Oku6zsNxDjziMrzq5WTulDf1KVm6IcXQA0u9hkuiZn39QzKzxrymrZKfMoIwwsdgYjKALBK-h0QgHN8eFT4Rzb0C5PbXL3fFGjeLuNjMOR6f8DCGLCAW8IBceRGwew_Fuh1qUIZiK07jc9AO4motyhkhtpxK4axAd5Xttn2wJtLhVar7tCmmKLKZlVhmTd4S5KIgPropORclBh6bexW1ZJLrv5Bfc-MSbNr50IBZOYNLmhXwkqScIJiYI2RF-D5Q")}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?1',
        'sec-ch-ua-platform': '"Android"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referer': 'https://flooz.xyz/',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
            params,
            headers
        });
        return response.data.data;
    } catch (error) {
        console.error('Error fetching token price history:', error);
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useUserData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useUserData": (()=>useUserData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/bot/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/bot/wallets.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
// Cache configuration
const CACHE_KEY_PREFIX = 'anubis_user_data_';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
function getCacheKey(token) {
    return `${CACHE_KEY_PREFIX}${token}`;
}
function getCachedUserData(token) {
    try {
        const cacheKey = getCacheKey(token);
        const cachedData = localStorage.getItem(cacheKey);
        if (!cachedData) return null;
        const { data, timestamp } = JSON.parse(cachedData);
        const now = new Date().getTime();
        // Check if cache is still valid
        if (now - timestamp < CACHE_DURATION) {
            return data;
        }
    } catch (error) {
        console.error('Error reading from cache:', error);
    }
    return null;
}
function cacheUserData(token, data) {
    try {
        const cacheKey = getCacheKey(token);
        const cacheData = {
            data,
            timestamp: new Date().getTime()
        };
        localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
        console.error('Error writing to cache:', error);
    }
}
function useUserData(token) {
    _s();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const fetchUserData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUserData.useCallback[fetchUserData]": async ()=>{
            console.log("Fetching user data...");
            if (!token) {
                setData(null);
                setError(null);
                return;
            }
            // Try to get data from cache first
            const cachedData = getCachedUserData(token);
            if (cachedData) {
                console.log("Using cached user data");
                setData(cachedData);
                setLoading(false);
                return;
            }
            setLoading(true);
            setError(null);
            try {
                const [userData, wallets, settings, balance, portfolio] = await Promise.all([
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWallets"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSettings"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getNativeBalance"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUserPortfolio"])(token)
                ]);
                const userDataResponse = {
                    ...userData,
                    ...wallets,
                    ...settings,
                    ...balance,
                    ...portfolio
                };
                // Cache the response
                cacheUserData(token, userDataResponse);
                setData(userDataResponse);
            } catch (err) {
                console.error('Error fetching user data:', err);
                setError(err);
                setData(null);
            } finally{
                setLoading(false);
            }
        }
    }["useUserData.useCallback[fetchUserData]"], [
        token
    ]);
    const refetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUserData.useCallback[refetch]": async ()=>{
            if (!token) return null;
            setLoading(true);
            setError(null);
            try {
                const [userData, wallets, settings, balance, portfolio] = await Promise.all([
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$wallets$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWallets"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSettings"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getNativeBalance"])(token),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$bot$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUserPortfolio"])(token)
                ]);
                const userDataResponse = {
                    ...userData,
                    ...wallets,
                    ...settings,
                    ...balance,
                    ...portfolio
                };
                // Update cache with fresh data
                cacheUserData(token, userDataResponse);
                setData(userDataResponse);
                return userDataResponse;
            } catch (err) {
                console.error('Error refetching user data:', err);
                setError(err);
                return null;
            } finally{
                setLoading(false);
            }
        }
    }["useUserData.useCallback[refetch]"], [
        token
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useUserData.useEffect": ()=>{
            // Only set loading to true if we don't have cached data
            if (!token || !getCachedUserData(token)) {
                setLoading(true);
            }
            setError(null);
            fetchUserData();
        }
    }["useUserData.useEffect"], [
        token,
        fetchUserData
    ]);
    return {
        data,
        loading,
        error,
        refetch
    };
}
_s(useUserData, "4BCj4ruQjI3/0vsT/YQaxm3JDqM=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/Web3Context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Web3Provider": (()=>Web3Provider),
    "useWeb3": (()=>useWeb3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/data.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$flooz$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/flooz.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useUserData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useUserData.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
const Web3Context = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const Web3Provider = ({ children })=>{
    _s();
    const [chainList] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chains"]);
    const { token } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { data } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useUserData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserData"])(token);
    const [selectedChain, setSelectedChain] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    // UseEffect to set selected chain from API
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Web3Provider.useEffect": ()=>{
            if (data?.settings?.chainId) {
                const locatedChain = chainList.find({
                    "Web3Provider.useEffect.locatedChain": (c)=>c.chainId === data.settings.chainId
                }["Web3Provider.useEffect.locatedChain"]);
                if (locatedChain) {
                    setSelectedChain(locatedChain);
                } else {
                    setSelectedChain(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chains"][0]); // fallback if chainId is invalid
                }
            } else if (data && !data?.settings?.chainId) {
                setSelectedChain(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chains"][0]); // fallback if no chainId at all
            }
        // else: leave as undefined (loading)
        }
    }["Web3Provider.useEffect"], [
        data?.settings?.chainId,
        data,
        chainList
    ]);
    // Initialize state from localStorage
    const [recentlyViewed, setRecentlyViewed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "Web3Provider.useState": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                const stored = localStorage.getItem('recentlyViewed');
                return stored ? JSON.parse(stored) : [];
            }
            "TURBOPACK unreachable";
        }
    }["Web3Provider.useState"]);
    // Update localStorage whenever recentlyViewed changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Web3Provider.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.setItem('recentlyViewed', JSON.stringify(recentlyViewed));
            }
        }
    }["Web3Provider.useEffect"], [
        recentlyViewed
    ]);
    const addRecentlyViewed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Web3Provider.useCallback[addRecentlyViewed]": async (address, chain)=>{
            setRecentlyViewed({
                "Web3Provider.useCallback[addRecentlyViewed]": (prev)=>{
                    // Check if item with same address and chain already exists
                    const exists = prev.some({
                        "Web3Provider.useCallback[addRecentlyViewed].exists": (item)=>item.address === address && item.chain === chain
                    }["Web3Provider.useCallback[addRecentlyViewed].exists"]);
                    if (exists) {
                        return prev;
                    }
                    // Optimistically add a placeholder while fetching
                    return [
                        ...prev,
                        {
                            address,
                            chain,
                            data: null
                        }
                    ];
                }
            }["Web3Provider.useCallback[addRecentlyViewed]"]);
            try {
                const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$flooz$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenData"])(chain, address);
                setRecentlyViewed({
                    "Web3Provider.useCallback[addRecentlyViewed]": (prev)=>{
                        // Replace the placeholder with the real data
                        return prev.map({
                            "Web3Provider.useCallback[addRecentlyViewed]": (item)=>item.address === address && item.chain === chain ? {
                                    ...item,
                                    data
                                } : item
                        }["Web3Provider.useCallback[addRecentlyViewed]"]);
                    }
                }["Web3Provider.useCallback[addRecentlyViewed]"]);
            } catch (error) {
                // Optionally handle error (e.g., remove placeholder)
                setRecentlyViewed({
                    "Web3Provider.useCallback[addRecentlyViewed]": (prev)=>prev.filter({
                            "Web3Provider.useCallback[addRecentlyViewed]": (item)=>!(item.address === address && item.chain === chain && item.data === null)
                        }["Web3Provider.useCallback[addRecentlyViewed]"])
                }["Web3Provider.useCallback[addRecentlyViewed]"]);
            }
        }
    }["Web3Provider.useCallback[addRecentlyViewed]"], []);
    const removeRecentlyViewed = (index)=>{
        setRecentlyViewed((prev)=>{
            const newState = prev.filter((_, i)=>i !== index);
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.setItem('recentlyViewed', JSON.stringify(newState));
            }
            return newState;
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Web3Context.Provider, {
        value: {
            chainList,
            selectedChain,
            setSelectedChain,
            recentlyViewed,
            addRecentlyViewed,
            removeRecentlyViewed
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/Web3Context.tsx",
        lineNumber: 97,
        columnNumber: 9
    }, this);
};
_s(Web3Provider, "UmHc/s3H3Q/hMSb7GiZ7OiTq9yM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useUserData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserData"]
    ];
});
_c = Web3Provider;
const useWeb3 = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(Web3Context);
    if (!context) {
        throw new Error('useWeb3 must be used within a Web3Provider');
    }
    return context;
};
_s1(useWeb3, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "Web3Provider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Preloader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Preloader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
function Preloader({ duration = 2000, onComplete }) {
    _s();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [progress, setProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Preloader.useEffect": ()=>{
            const startTime = Date.now();
            const updateProgress = {
                "Preloader.useEffect.updateProgress": ()=>{
                    const elapsed = Date.now() - startTime;
                    const newProgress = Math.min(elapsed / duration * 100, 100);
                    setProgress(newProgress);
                    if (newProgress >= 100) {
                        setTimeout({
                            "Preloader.useEffect.updateProgress": ()=>{
                                setIsLoading(false);
                                onComplete?.();
                            }
                        }["Preloader.useEffect.updateProgress"], 200); // Small delay before hiding
                    } else {
                        requestAnimationFrame(updateProgress);
                    }
                }
            }["Preloader.useEffect.updateProgress"];
            requestAnimationFrame(updateProgress);
        }
    }["Preloader.useEffect"], [
        duration,
        onComplete
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
        children: isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 1
            },
            exit: {
                opacity: 0
            },
            transition: {
                duration: 0.3
            },
            className: "fixed top-0 left-0 right-0 z-50 bg-transparent",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full h-1 bg-transparent relative overflow-hidden",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "h-full bg-white",
                    initial: {
                        width: "0%"
                    },
                    animate: {
                        width: `${progress}%`
                    },
                    transition: {
                        ease: "easeOut",
                        duration: 0.1
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/Preloader.tsx",
                    lineNumber: 48,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Preloader.tsx",
                lineNumber: 47,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/Preloader.tsx",
            lineNumber: 40,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Preloader.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_s(Preloader, "7Tu1FLaiJMfRGh4tkUqno3L8DdM=");
_c = Preloader;
var _c;
__turbopack_context__.k.register(_c, "Preloader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_35b02a57$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_35b02a57.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_87bc4ef9$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/geist_mono_87bc4ef9.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$orbitron_94041d18$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/orbitron_94041d18.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$space_grotesk_b1377d82$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/space_grotesk_b1377d82.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$Web3Context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/Web3Context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-cookie/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Preloader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Preloader.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
function RootLayout({ children }) {
    _s();
    const [showPreloader, setShowPreloader] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RootLayout.useEffect": ()=>{
            document.title = "Anubis | Snipe and Sell Tokens On-Chain Instantly";
            const metaDescription = document.querySelector('meta[name="description"]');
            if (metaDescription) {
                metaDescription.setAttribute('content', 'Snipe and Sell Tokens On-Chain Instantly');
            }
        }
    }["RootLayout.useEffect"], []);
    const handlePreloaderComplete = ()=>{
        setShowPreloader(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_35b02a57$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$geist_mono_87bc4ef9$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$orbitron_94041d18$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$space_grotesk_b1377d82$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].variable} antialiased`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["CookiesProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$Web3Context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Web3Provider"], {
                        children: [
                            showPreloader && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Preloader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                duration: 2000,
                                onComplete: handlePreloaderComplete
                            }, void 0, false, {
                                fileName: "[project]/src/app/layout.tsx",
                                lineNumber: 66,
                                columnNumber: 17
                            }, this),
                            children,
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ToastContainer"], {
                                position: "bottom-right",
                                autoClose: 3000,
                                hideProgressBar: false,
                                newestOnTop: true,
                                closeOnClick: true,
                                rtl: false,
                                pauseOnFocusLoss: true,
                                draggable: true,
                                pauseOnHover: true,
                                theme: "dark",
                                toastClassName: "font-orbitron bg-black/95 border border-white/20 rounded-sm backdrop-blur-xl shadow-lg shadow-black/20",
                                className: "text-xs tracking-wide",
                                progressClassName: "bg-gradient-to-r from-white/20 via-white to-white/20",
                                closeButton: ({ closeToast })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                                        className: "text-white/70 hover:text-white transition-colors ml-auto",
                                        whileHover: {
                                            scale: 1.05
                                        },
                                        whileTap: {
                                            scale: 0.95
                                        },
                                        onClick: closeToast,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoMdClose"], {
                                            className: "text-xs"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/layout.tsx",
                                            lineNumber: 93,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/layout.tsx",
                                        lineNumber: 87,
                                        columnNumber: 19
                                    }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/layout.tsx",
                                lineNumber: 72,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 64,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout.tsx",
                    lineNumber: 63,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 62,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/layout.tsx",
            lineNumber: 59,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 58,
        columnNumber: 5
    }, this);
}
_s(RootLayout, "wqyIf30TpRnv0aLB/8j6PcL2hAk=");
_c = RootLayout;
var _c;
__turbopack_context__.k.register(_c, "RootLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__e1c0cd36._.js.map