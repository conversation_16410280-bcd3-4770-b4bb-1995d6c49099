import { Address, Chain, define<PERSON>hain } from "viem";

export interface BotConfig {
  chain: Chain;
  chatId: string;
  tax: {
    default: number;
    snipe: number;
    address: Address;
  };
  nativeCurrencyAddress: Address;
}

export enum ESupportedChains {
  Core = 1116,
  Base = 8453,
  ZkSync = 324,
  Blast = 81457,
  Sonic = 146,
  Avax = 43114,
  Bera = 80094,
  Unichain = 130
}

export const odosSupportedChains = [
  1,
  324,
  8453,
  5000,
  137,
  10,
  34443,
  43114,
  59144,
  534352,
  42161,
  56,
  250,
  146
];

export type SupportedChainNames = Lowercase<keyof typeof ESupportedChains>;
export type SupportedDexes =
  | "gecko" // Gecko is for fetching data from CoinGecko
  | "sushiswap"
  | "longswap"
  | "uniswap"
  | "quickswap"
  | "pancakeswap"
  | "icecreamswap"
  | "syncswap";

export const supportedDexs: SupportedDexes[] = [
  "syncswap",
  // "sushiswap",
  // "longswap",
  "uniswap",
  // "uniswap", "quickswap",
  "pancakeswap",
];

export const supportedChains = Object.entries(ESupportedChains).reduce(
  (acc, [k, v]) => ({ ...acc, [v]: k.toLowerCase() }),
  {}
) as Record<ESupportedChains, SupportedChainNames>;

export interface ChainConfig {
  SCAN_URL: string;
  CURRENCY_SYMBOL: string;
  NATIVE_TOKEN_ADDRESS: Address;
}

export const CHAIN_CONFIG: Record<ESupportedChains, ChainConfig> = {
  [ESupportedChains.Core]: {
    SCAN_URL: "https://scan.coredao.org",
    CURRENCY_SYMBOL: "CORE",
    NATIVE_TOKEN_ADDRESS: "******************************************",
  },
  [ESupportedChains.Base]: {
    SCAN_URL: "https://basescan.org/",
    CURRENCY_SYMBOL: "ETH",
    NATIVE_TOKEN_ADDRESS: "******************************************",
  },
  [ESupportedChains.ZkSync]: {
    SCAN_URL: "https://explorer.zksync.io/",
    CURRENCY_SYMBOL: "ETH",
    NATIVE_TOKEN_ADDRESS: "******************************************",
  },
  [ESupportedChains.Blast]: {
    SCAN_URL: "https://blastscan.io",
    CURRENCY_SYMBOL: "ETH",
    NATIVE_TOKEN_ADDRESS: "******************************************",
  },
  [ESupportedChains.Sonic]: {
    SCAN_URL: "https://sonicscan.org",
    CURRENCY_SYMBOL: "S",
    NATIVE_TOKEN_ADDRESS: "******************************************",
  },
  [ESupportedChains.Avax]: {
    SCAN_URL: "https://avascan.info",
    CURRENCY_SYMBOL: "AVAX",
    NATIVE_TOKEN_ADDRESS: "****************************************** ",
  },
  [ESupportedChains.Bera]: {
    SCAN_URL: "https://berascan.com",
    CURRENCY_SYMBOL: "$BERA",
    NATIVE_TOKEN_ADDRESS: "****************************************** ",
  },
  [ESupportedChains.Unichain]: {
    SCAN_URL: "https://unichain.blockscout.com",
    CURRENCY_SYMBOL: "ETH",
    NATIVE_TOKEN_ADDRESS: "****************************************** ",
  },
};

// RPC endpoints configuration
const RPC_ENDPOINTS: Record<ESupportedChains, string[]> = {
  [ESupportedChains.Core]: [
    'https://rpc.coredao.org',
    'https://rpc.ankr.com/core'
  ],
  [ESupportedChains.Base]: [
    'https://base.llamarpc.com',
    'https://mainnet.base.org',
    'https://base-rpc.publicnode.com'
  ],
  [ESupportedChains.ZkSync]: [
    'https://mainnet.era.zksync.io',
    'https://zksync.drpc.org'
  ],
  [ESupportedChains.Blast]: [
    'https://rpc.blast.io',
    'https://blast.drpc.org'
  ],
  [ESupportedChains.Sonic]: [
    'https://rpc.soniclabs.com',
    'https://rpc.sonic.financial'
  ],
  [ESupportedChains.Avax]: [
    'https://avalanche.drpc.org',
    'https://api.avax.network/ext/bc/C/rpc'
  ],
  [ESupportedChains.Bera]: [
    'https://rpc.berachain-apis.com',
    'https://rpc.berachain.com'
  ],
  [ESupportedChains.Unichain]: [
    'https://unichain.drpc.org',
    'https://rpc.unichain.com'
  ]
};

export function getConfig(
  chainId: ESupportedChains = ESupportedChains.Base
): BotConfig {
  const { SCAN_URL, CURRENCY_SYMBOL, NATIVE_TOKEN_ADDRESS } =
    CHAIN_CONFIG[chainId] ?? CHAIN_CONFIG[ESupportedChains.Base];

  const chainName = supportedChains[chainId]!;

  return {
    chain: defineChain({
      id: chainId,
      name: chainName,
      nativeCurrency: {
        name: CURRENCY_SYMBOL,
        symbol: CURRENCY_SYMBOL,
        decimals: 18,
      },
      rpcUrls: {
        default: {
          http: RPC_ENDPOINTS[chainId] || ['https://rpc.ankr.com/eth'],
        },
      },
      blockExplorers: {
        default: {
          name: "Scan",
          key: chainName,
          url: SCAN_URL,
        },
      },
    }),
    chatId: "Anubis",
    tax: {
      default: Number(process.env.TAX_PERCENT) / 100,
      snipe: Number(process.env.TAX_SNIPE_PERCENT) / 100,
      address: process.env.TAX_ADDRESS as Address,
    },
    nativeCurrencyAddress: NATIVE_TOKEN_ADDRESS,
  };
}
