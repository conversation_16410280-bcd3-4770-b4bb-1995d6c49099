export interface GlobalNavLinks {
    title: string;
    url: string;
}

export const globalNavLinks = [
    {
        title: "Features",
        url: "#features"
    },
    {
        title: "Anubis Teams",
        url: "/anubis-teams"
    },
    {
        title: "Documentation",
        url: "/documentation"
    },
    {
        title: "Browser Extension",
        url: "#browser-extension"
    },
    {
        title: "Tutorials",
        url: "/tutorials"
    }
]

// export const socials = [
//     {
//         url:"https://t.me/AnubisBot"
//     }
// ]`


export const terminalNavLinks = [
    {
        title: "Trending",
        url: "/terminal/trending"
    },
    {
        title: "Gainers",
        url: "/terminal/gainers"
    },
    {
        title: "New",
        url: "/terminal/new"
    },
    {
        title: "portfolio",
        url: "/terminal/portfolio"
    },
    {
        title: "Wallets",
        url: "/terminal/wallets"
    },
    {
        title: "Sniping",
        url: "/terminal/sniping"
    }
]

export const tokenData = [
    {
        name: "Bitcoin",
        symbol: "BTC",
        price: "$20,000",
        change: "+2.5%",
        volume: "$100,000,000",
        marketCap: "$1,000,000,000",
        logo: "/logo/icon.png"
    }
]

export const chains = [
    {
        chain: "Avax",
        logo: "/chains/avax.svg",
        slug: "avalanche",
        scanUrl: "https://snowtrace.io",
        chainId: 43114,
        symbol: "AVAX"
    },
    {
        chain: "Base",
        logo: "/chains/base.svg",
        slug: "base",
        scanUrl: "https://basescan.org",
        chainId: 8453,
        symbol: "ETH"
    },
    {
        chain: "BeraChain",
        logo: "/chains/berachain.svg",
        slug: "berachain",
        scanUrl: "https://artio.beratrail.io",
        chainId: 80094,
        symbol: "BERA"
    },
    {
        chain: "Sonic",
        logo: "/chains/sonic.svg",
        slug: "sonic",
        scanUrl: "https://explorer.sonic.ooo",
        chainId: 146,
        symbol: "S"
    },
    {
        chain: "Unichain",
        logo: "/chains/unichain.svg",
        slug: "unichain",
        scanUrl: "https://unichainscan.com",
        chainId: 130,
        symbol: "U ETH"
    },
]

export function calculateFDV(totalSupplyStr: string, priceStr: string): number {
    const totalSupply = parseFloat(totalSupplyStr);
    const priceUSD = parseFloat(priceStr);

    if (isNaN(totalSupply) || isNaN(priceUSD)) {
        throw new Error("Invalid number format in input");
    }

    const fdv = totalSupply * priceUSD;
    return parseFloat(fdv.toFixed(2)); // rounded to 2 decimals (USD convention)
}

export function getTimeAgo(timestamp: number): string {
    const now = Date.now() / 1000; // Current time in seconds
    const secondsAgo = now - timestamp;

    if (secondsAgo < 60) {
        return "<1M";
    }

    const minutes = Math.floor(secondsAgo / 60);
    if (minutes < 60) {
        return `${minutes}M`;
    }

    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
        return `${hours}H`;
    }

    const days = Math.floor(hours / 24);
    return `${days}D`;
}

type Activity = {
    volume1: string;
    volume4: string;
    volume12: string;
    volume24: string;
};

export function getVolumeByTimeOption(activity: Activity, timeOption: string): number {
    const mapping: Record<string, keyof Activity> = {
        "1h": "volume1",
        "4h": "volume4",
        "12h": "volume12",
        "24h": "volume24"
    };

    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }

    const volume = parseFloat(activity[key]);
    return isNaN(volume) ? 0 : volume;
}

type PriceChange = {
    change1: string;
    change4: string;
    change12: string;
    change24: string;
};

export function getPriceChangeByTimeOption(activity: PriceChange, timeOption: string): number {
    const mapping: Record<string, keyof PriceChange> = {
        "1h": "change1",
        "4h": "change4",
        "12h": "change12",
        "24h": "change24"
    };

    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }

    const change = parseFloat(activity[key]);
    return isNaN(change) ? 0 : change;
}


type UniqueBuys = {
    uniqueBuys1: number;
    uniqueBuys4: number;
    uniqueBuys12: number;
    uniqueBuys24: number;
};

export function getUniqueBuysByTimeOption(activity: UniqueBuys, timeOption: string): number {
    const mapping: Record<string, keyof UniqueBuys> = {
        "1h": "uniqueBuys1",
        "4h": "uniqueBuys4",
        "12h": "uniqueBuys12",
        "24h": "uniqueBuys24"
    };

    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }

    const value = activity[key];
    return typeof value === "number" ? value : 0;
}

type UniqueSells = {
    uniqueSells1: number;
    uniqueSells4: number;
    uniqueSells12: number;
    uniqueSells24: number;
};

export function getUniqueSellsByTimeOption(activity: UniqueSells, timeOption: string): number {
    const mapping: Record<string, keyof UniqueSells> = {
        "1h": "uniqueSells1",
        "4h": "uniqueSells4",
        "12h": "uniqueSells12",
        "24h": "uniqueSells24"
    };

    const key = mapping[timeOption];
    if (!key) {
        throw new Error(`Invalid time option: ${timeOption}`);
    }

    const value = activity[key];
    return typeof value === "number" ? value : 0;
}


export const baseURL: string = process.env.NODE_ENV === "production" ? "https://anubis-terminal-v2.vercel.app" : "http://localhost:3000";

export function getShareLink(chain: string, address: string): string {
    return `${baseURL}/terminal/trade/${chain}/${address}`;
}