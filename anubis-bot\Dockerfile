# ---- Base image ----
FROM oven/bun:1.0.25 as base

WORKDIR /app

# Install dependencies
COPY package.json bun.lockb ./
RUN bun install --production

# Copy source code
COPY . .

# Expose the port the API listens on
EXPOSE 1340

# Set default environment variables
ENV NODE_ENV=production
ENV API_PORT=1340

# Declare environment variables that will be provided at runtime
# These will be set via docker-compose.yml or .env file
ENV BOT_TOKEN=""
ENV TAX_PERCENT=""
ENV TAX_SNIPE_PERCENT=""
ENV TAX_ADDRESS=""
ENV ADMIN_ID=""
ENV JWT_SECRET=""
ENV TERMINAL_URL=""

# Start the API server using the package.json script
CMD ["bun", "run", "deploy:api"]
