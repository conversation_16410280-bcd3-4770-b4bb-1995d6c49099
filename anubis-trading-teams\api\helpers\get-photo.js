const axios = require("axios");

module.exports = {
  friendlyName: "Get photo",

  description: "Use Telegram API to get photo URL",

  inputs: {
    file_id: {
      type: "string",
      required: true,
    },
  },

  exits: {
    success: {
      outputFriendlyName: "Photo",
    },
  },

  fn: async function ({ file_id }) {
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;

    const getFileApiUrl = `https://api.telegram.org/bot${TOKEN}/getFile?file_id=${file_id}`;

    try {
      // Step 1: Call the Telegram Bot API's getFile method
      const response = await axios.get(getFileApiUrl);

      // Check if the API call was successful
      if (response.data.ok) {
        const fileInfo = response.data.result;

        // Ensure file_path exists in the response
        if (fileInfo && fileInfo.file_path) {
          const filePath = fileInfo.file_path;

          // Step 2: Construct the full download URL
          const downloadUrl = `https://api.telegram.org/file/bot${TOKEN}/${filePath}`;
          sails.log.debug(downloadUrl);
          return downloadUrl;
        } else {
          return "";
        }
      } else {
        return "";
      }
    } catch (error) {
      console.error("Error fetching Telegram file URL:", error.message);
      return "";
    }
  },
};
