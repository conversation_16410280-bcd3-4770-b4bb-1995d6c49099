# Native Token Transfer Fix

## Problem
The bot was encountering an error when trying to transfer native tokens:
```
gas required exceeds allowance (86)
```

This error occurred because the transaction was not being properly configured with gas parameters, and in the "withdraw all" scenario, there wasn't enough balance left to cover gas fees.

## Root Cause Analysis
1. The `estimateGas` call was failing because it was trying to transfer the entire balance without leaving enough for gas fees
2. The transaction was not being sent with explicit gas parameters
3. The "withdraw all" functionality was not properly accounting for gas fees

## Solution

### 1. Fixed the `transferNativeToken` Function
- Used a fixed gas limit (21000) for native token transfers instead of estimating gas
- Added explicit gas parameters to the transaction
- Implemented proper handling for "withdraw-all" scenario by leaving enough for gas fees
- Reduced the gas price buffer from 50% to 20% to avoid excessive fees

```typescript
// For "withdraw-all" scenario, adjust the amount to leave enough for gas
let finalAmount = amountBigInt;
if (amount === "withdraw-all" || amountBigInt >= senderBalance - gasFee) {
  finalAmount = senderBalance - gasFee;
  console.log(`Adjusted "withdraw-all" amount to leave gas fee: ${formatUnits(finalAmount, 18)} ${userConfig.chain.nativeCurrency.symbol}`);
}

// Execute the transfer with explicit gas parameters
const txHash = await wallet.sendTransaction({
  to: toAddress,
  value: finalAmount,
  account: wallet.account!,
  chain: userConfig.chain,
  gas: gasLimit,
  gasPrice: gasPriceWithBuffer
});
```

### 2. Improved the `handleTransfer` Function
- Passed the special "withdraw-all" string to the transfer function instead of the balance
- Added better user feedback for the "withdraw-all" scenario
- Improved error handling with proper Markdown escaping for error messages

```typescript
// Determine amount to transfer
const amount = ctx.session.withdrawalAmount === "withdraw-all"
  ? "withdraw-all" // Pass the special string to the transfer function
  : ctx.session.withdrawalAmount;

// Show processing message
const displayAmount = amount === "withdraw-all" 
  ? `maximum available (leaving gas fee)` 
  : `${amount} ${userConfig.chain.nativeCurrency.symbol}`;
```

## Benefits
1. **Reliable Transfers**: Native token transfers now work reliably, even for "withdraw all" scenarios
2. **Better User Experience**: Users get clearer feedback about what's happening with their transfer
3. **Improved Error Handling**: Error messages are properly formatted and displayed to users
4. **Gas Efficiency**: Fixed gas limit ensures predictable gas usage for simple transfers

## Technical Details
The key insight was that native token transfers always use a fixed gas amount (21000 gas units), so there's no need to estimate gas. By using this fixed value and ensuring we leave enough balance to cover gas fees, we can make transfers reliable.

For the "withdraw all" scenario, we now calculate the maximum amount that can be transferred by subtracting the gas fee from the total balance, ensuring there's always enough left to cover the transaction costs.
