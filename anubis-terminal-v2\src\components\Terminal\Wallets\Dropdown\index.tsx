"use client";
import { useState } from "react";
import { motion } from "framer-motion";
import { FaCaretDown, FaCaretUp } from "react-icons/fa";
import { TiTick } from "react-icons/ti";

interface Wallet {
    chain: string;
    address: `0x${string}`;
    usdBalance: number;
    icon: React.ReactNode;
}

export default function WalletSelector({ wallets }: { wallets: Wallet[] }) {
    const [selectedWallet, setSelectedWallet] = useState<Wallet>(wallets[0]);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    return (
        <motion.div className="rounded-md py-3 flex justify-between w-fit border border-white/10 hover:border-white/20 transition-colors px-5 min-w-[200px] hover:cursor-pointer relative">
            <div className="flex items-center justify-between gap-x-1 w-full" onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
                <div className="flex gap-x-1 items-center">
                    <span className="text-white">{selectedWallet.icon}</span>
                    <span className="text-sm font-medium">{selectedWallet.chain}</span>
                </div>
                <div className="flex items-center gap-x-3">
                    <span className="text-xs text-white/70 border-r border-white pr-2">${Number(selectedWallet.usdBalance).toFixed(2)}</span>
                    {isDropdownOpen ? <FaCaretUp className="text-white" /> : <FaCaretDown className="text-white" />}
                </div>
            </div>
            {isDropdownOpen && (
                <motion.div
                    className="absolute top-full left-0 w-full bg-black/50 border border-white/10 rounded-md mt-4 z-20"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                >
                    {wallets.map((wallet, index) => (
                        <div
                            key={index}
                            className="flex items-center justify-between gap-x-1 w-full px-5 py-3 hover:bg-white/10 transition-colors cursor-pointer"
                            onClick={() => {
                                setSelectedWallet(wallet);
                                setIsDropdownOpen(false);
                            }}
                        >
                            <div className="flex gap-x-1 items-center">
                                <span className="text-white">{wallet.icon}</span>
                                <span className="text-sm font-medium">{wallet.chain}</span>
                            </div>
                            <div className="flex items-center gap-x-3">
                                <span className="text-xs text-white/70">${Number(wallet.usdBalance).toFixed(2)}</span>
                                <span>{selectedWallet.chain === wallet.chain ? <TiTick className="text-green" /> : null}</span>
                            </div>
                        </div>
                    ))}
                </motion.div>
            )
            }
        </motion.div >
    )
}