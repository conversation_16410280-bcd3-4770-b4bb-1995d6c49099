import axios, { type AxiosError } from 'axios';

interface Token {
    address: string;
    name: string;
    symbol: string;
}

interface PairInfo {
    imageUrl: string;
    websites: { url: string }[];
    socials: { platform: string; handle: string }[];
}

interface Pair {
    chainId: string;
    dexId: string;
    url: string;
    pairAddress: string;
    labels: string[];
    baseToken: Token;
    quoteToken: Token;
    priceNative: string;
    priceUsd: string;
    txns: Record<string, { buys: number; sells: number }>;
    volume: Record<string, number>;
    priceChange: Record<string, number>;
    liquidity: { usd: number; base: number; quote: number };
    fdv: number;
    marketCap: number;
    pairCreatedAt: number;
    info: PairInfo;
    boosts: { active: number };
}

interface DexScreenerResponse {
    pairs: Pair[];
}

const MAX_RETRIES = 3;
const INITIAL_DELAY = 1000;

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const getTokenPairs = async (
    tokenAddress: `0x${string}`,
    network: string,
    retries = MAX_RETRIES
): Promise<DexScreenerResponse> => {
    // Get Pools First
    const poolsEndpoint = `https://api.dexscreener.com/token-pairs/v1/${network}/${tokenAddress}`

    try {
        const response = await axios.get(poolsEndpoint);
        if (!response) {
            throw new Error("Failed to fetch pools");
        }

        const url = `https://api.dexscreener.com/latest/dex/pairs/${network}/${response.data[0].pairAddress}`;

        try {
            const response = await axios.get<DexScreenerResponse>(url);
            return response.data;
        } catch (error) {
            const axiosError = error as AxiosError;

            if (axiosError.response?.status === 429 && retries > 0) {
                const delayTime = INITIAL_DELAY * (MAX_RETRIES - retries + 1);
                await delay(delayTime);
                return getTokenPairs(tokenAddress, network, retries - 1);
            }

            console.error("Error fetching token pairs:", error);
            throw error;
        }
    } catch (error) {
        console.error("Error fetching pools:", error);
        throw error;
    }

}

export const getTokenData = async (tokenAddress: `0x${string}`, network: string): Promise<Pair | null> => {
    const url = `https://api.dexscreener.com/token-pairs/v1/${network}/${tokenAddress}`;
    try {
        const response = await axios.get(url);
        return response.data[0];
    } catch (error) {
        console.error("Error fetching token data:", error);
        throw error;
    }
}