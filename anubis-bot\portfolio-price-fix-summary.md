# Portfolio Price Display Fix

## Problem
When viewing the portfolio, some tokens were showing `$undefined` for their price. This was happening because the `priceUsd` field was undefined for some tokens, and the code wasn't properly handling this case.

## Solution
We've implemented several fixes to properly handle undefined prices:

### 1. Updated the handlePortfolio function
Modified the token display in the portfolio to handle undefined prices by:
- Checking if `priceUsd` is undefined
- Falling back to `priceNative` when available
- Displaying "Price unavailable" when no price data is available

### 2. Updated the convertGeckoTokenInfoToPair function
Ensured that the `priceUsd` field is properly set when converting from Gecko data:
- Added explicit assignment of `priceUsd` from the Gecko API response

### 3. Updated the sendTokenInfo function
Modified the token info display to handle undefined prices:
- Added checks for undefined values in price, FDV, liquidity, and volume
- Implemented fallbacks to display "Not available" when data is missing

### 4. Improved the convertToUSD function
Enhanced the function to handle undefined values gracefully:
- Added checks for undefined or null inputs
- Returns "Not available" instead of throwing an error for invalid inputs

## Benefits
These changes provide a more user-friendly experience by:
1. Eliminating confusing "$undefined" displays
2. Showing meaningful fallback values when data is missing
3. Using available price data even when the preferred source is unavailable
4. Preventing errors when handling missing data

## Testing
The changes have been implemented and should now properly display prices for all tokens in the portfolio, even when some price data is missing from the primary source.
