<!--
This is your default homepage.
For more information, see:
• https://sailsjs.com/documentation/concepts/views
• https://sailsjs.com/documentation/concepts/routes
-->
<link href="https://fonts.googleapis.com/css?family=Lato:300,400" rel="stylesheet">
<style>
  /* Styles included inline since you'll probably be deleting this page anyway */
  html,body{text-align:left;font-size:1em}html,body,img,form,textarea,input,fieldset,div,p,div,ul,li,ol,dl,dt,dd,h1,h2,h3,h4,h5,h6,pre,code{margin:0;padding:0}ul,li{list-style:none}img{display:block}a img{border:0}a{text-decoration:none;font-weight:normal;font-family:inherit}*:active,*:focus{outline:0;-moz-outline-style:none}h1,h2,h3,h4,h5,h6{font-weight:normal}div.clear{clear:both}.clearfix:after{clear:both;content:".";display:block;font-size:0;height:0;line-height:0;visibility:hidden}body{font-family:"Lato",Arial,sans-serif;font-weight:300;}.top-bar {width: 100%; background-color: #e4f0f1; padding: 15px 0;}.top-bar .container img {float: left;}.top-bar .container ul {float: right; padding-top: 0px;}.top-bar .container li {float: left; width: 125px; text-align: center; font-size: 15px; color:#000; font-weight: 600;}.top-bar .container a li:hover {color: #118798; -webkit-transition:color 200ms; -moz-transition:color 200ms; -o-transition:color 200ms;transition:color 200ms;}.container{width: 80%; max-width: 1200px; margin: auto;}div.header {-webkit-transition: 6s; -moz-transition: 6s; -o-transition: 6s;transition: 6s; background: rgba(4, 36, 41, 0.89) url('data:image/png;base64,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') no-repeat 42% bottom; padding: 100px 0 65px;}.header h1#main-title{color: #fff; font-weight: 300; font-size: 2.5em;}.header h3{color: #b1eef7; font-style: italic; font-weight: 300; padding-top: 5px;}.header h3 code{font-style: normal!important; background-color: rgba(255,255,255,0.5); font-weight: 300; color:#0e6471; margin: 0px 5px;}div.main.container{padding: 50px 0 10px;}h1 {color: #118798; font-weight: 300;}code {font-size: inherit; font-family: 'Consolas', 'Monaco', monospace; padding:4px 5px 1px; background-color: #f3f5f7}a{color: #118798; font-weight: 300; text-decoration: underline;}a:hover {color: #0e6471; -webkit-transition:color 200ms; -moz-transition:color 200ms; -o-transition:color 200ms;transition:color 200ms;}p{line-height: 1.5em;}blockquote{background-color: #e4f0f1; padding: 25px; line-height: 1.5em; margin: 15px 0;}blockquote span{font-weight: 600; padding-right: 5px;}ul.getting-started{padding: 0px 75px 0px 0; width: 70%; float: left; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box;}ul.getting-started li{padding: 15px 0;}ul.getting-started li.first{padding-top: 0px;}ul.getting-started li h3 {padding-bottom: 10px; font-size: 25px; font-weight: 300;}.sprite{background:url('data:image/png;base64,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') no-repeat; position: absolute; left: 0; top:0;}.getting-started .sprite{margin-left:10px;padding-left:60px;height:42px;width:0; float: left;}.getting-started .one{background-position:0 0}.getting-started .two{background-position:0 -42px}.getting-started .three{background-position:0 -83px}div.step p { font-size: 15px; }div.step {position: relative; padding-left: 70px; opacity: 0.9;}div.step:hover{ opacity: 1;}div.links {float: left; width: 30%; max-width: 325px; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; background-color: #f3f5f7; border: 1px solid #ebebeb; padding: 25px 45px 35px;}div.links h3 {color: #727272; text-align: center; font-size: 28px; font-weight: 300;}div.links h4 {color: #727272; font-size: 17px; font-weight: 600; padding: 15px 0 10px;}div.links .link-list a {text-decoration: none; font-weight: 400;}div.links .link-list a li {padding: 0px 0px 5px 10px;}div.default-page{min-width: 1200px;}.pocket{display:none;}
</style>
<script type="text/javascript">
setTimeout(function sunrise () {
  document.getElementsByClassName('header')[0].style.backgroundColor = '#118798';
}, 0);
</script>

<div class="default-page">
  <div class="header">
    <h1 id="main-title" class="container"><%= __('A brand new app.') %></h1>
    <h3 class="container">You're looking at: <code><%= view.pathFromApp + '.' +view.ext %></code></h3>
  </div>
  <div class="main container clearfix">
    <ul class="getting-started">
      <li class="clearfix first">
        <div class="step">
          <div class="sprite one"></div>
          <h3>Get your bearings</h3>
          <p>
            If you're new to Sails, get started <a href="https://sailsjs.com/get-started">here</a>.
            <br/>
            <small>(You'll learn about the framework and explore the files that were generated in this new app.)</small>
          </p>
        </div>
      </li>
      <li class="clearfix">
        <div class="step">
          <div class="sprite two"></div>
          <h3>Lift your app</h3>
          <p>
            Looks like you already ran <code>sails lift</code> to start up your app server. Nice work!
            <br/>
            <small>(Remember to re-lift after making backend code changes.)</small>
          </p>
        </div>
      </li>
      <li class="clearfix">
        <div class="step">
          <div class="sprite three"></div>
          <h3>Dive in</h3>
          <p>
            Blueprints are just the beginning.  You'll probably also want to learn how to customize your app's <a href="https://sailsjs.com/documentation/concepts/routes">routes</a>, build custom <a target="_blank" href="https://sailsjs.com/documentation/concepts/actions-and-controllers">actions</a>, set up <a href="https://sailsjs.com/documentation/concepts/policies">security policies</a>, configure your <a href="https://sailsjs.com/documentation/reference/configuration/sails-config-datastores">data sources</a>, and deploy to <a href="http://sailsjs.com/documentation/concepts/deployment">production</a>.
            <br/>
            <small>(For more help getting started, check out the links on this page.)</small>
          </p>
        </div>
      </li>
    </ul>
    <div class="links">
      <ul class="link-list">

        <h4>Documentation</h4>
        <li><a target="_blank" href="https://sailsjs.com/whats-that">About the framework</a></li>
        <li><a target="_blank" href="https://sailsjs.com/documentation/anatomy">App structure</a></li>
        <li><a target="_blank" href="https://sailsjs.com/documentation/concepts/extending-sails/adapters/available-adapters">Supported databases</a></li>
        <li><a target="_blank" href="https://sailsjs.com/documentation/concepts/upgrading">Version notes</a></li>

        <h4>More resources</h4>
        <li><a target="_blank" href="https://sailsjs.com/enterprise">Professional / enterprise</a></li>
        <li><a target="_blank" href="https://sailsjs.com/support">Community support options</a></li>
        <li><a target="_blank" href="https://sailsjs.com/documentation/contributing">Contribute</a></li>
        <li><a target="_blank" href="https://sailsjs.com/resources">Logos/artwork</a></li>
        <li><a target="_blank" href="https://twitter.com/sailsjs">News</a></li>

      </ul>
    </div>
  </div>
</div>
