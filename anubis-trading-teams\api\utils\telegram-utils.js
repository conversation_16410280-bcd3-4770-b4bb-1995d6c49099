const getPhotoHelper = require('../helpers/get-photo');

/**
 * Convert Telegram file ID to a viewable URL
 * @param {Object} fileData - The file data object from Telegram
 * @returns {Promise<Object>} - Promise that resolves to an object containing the file URL and original file data
 */
module.exports = {
  getFileUrl: async function(fileData) {
    if (!fileData || !fileData.file_id) {
      return null;
    }
    
    try {
      // Use the get-photo helper to get the actual download URL
      const downloadUrl = await getPhotoHelper({ file_id: fileData.file_id });
      
      return {
        url: downloadUrl,
        file_path: downloadUrl, // For backward compatibility
        original: fileData
      };
    } catch (error) {
      console.error('Error getting file URL from Telegram:', error);
      return null;
    }
  },

  /**
   * Process page data to include file URLs
   * @param {Object} pageData - The page data object
   * @returns {Promise<Object>} - Processed page data with file URLs
   */
  processPageData: async function(pageData) {
    if (!pageData) return pageData;
    
    const processedData = { ...pageData };
    
    // Process pageLogo if it exists
    if (processedData.pageLogo) {
      processedData.pageLogo = await this.getFileUrl(processedData.pageLogo);
    }
    
    // Process banner if it exists
    if (processedData.banner) {
      processedData.banner = await this.getFileUrl(processedData.banner);
    }
    
    return processedData;
  }
};
