"use client"
import { motion } from "framer-motion";
import { useState, useRef } from "react";
import Link from "next/link";
import { FaPlay, FaPause, FaChrome, FaShieldAlt, FaBolt, FaChartLine, FaMousePointer, FaExpand, FaDownload } from "react-icons/fa";

export default function BrowserExtension() {
    const [isPlaying, setIsPlaying] = useState(false);
    const [isHovering, setIsHovering] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);

    const features = [
        {
            icon: <FaChrome className="text-white" />,
            title: "Seamless Integration",
            description: "Works directly within your browser, no need to switch between interfaces"
        },
        {
            icon: <FaBolt className="text-white" />,
            title: "Instant Execution",
            description: "Execute trades with one click directly from Dexscreener charts"
        },
        {
            icon: <FaShieldAlt className="text-white" />,
            title: "Secure Trading",
            description: "Advanced security features protect your transactions and wallet"
        },
        {
            icon: <FaChartLine className="text-white" />,
            title: "Real-Time Analysis",
            description: "Access comprehensive market data and analytics instantly"
        },
        {
            icon: <FaMousePointer className="text-white" />,
            title: "One-Click Trading",
            description: "Trade any token directly from Dexscreener with minimal clicks"
        }
    ];

    const handleFullscreen = () => {
        if (videoRef.current) {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                videoRef.current.requestFullscreen();
            }
        }
    };

    return (
        <section className="py-20 px-4 md:px-8 lg:px-12 bg-[#0a0a0a]" id="browser-extension">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: -20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold mb-4 text-white">
                        Anubis Browser Extension
                    </h2>
                    <p className="text-gray-400 font-space-grotesk max-w-3xl mx-auto text-lg">
                        Anubis Interface is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.
                        Fully compatible with Dexscreener, Anubis enables traders to seamlessly analyse market trends and execute trades without ever switching between interfaces.
                    </p>
                </motion.div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    {/* Video Demo Section */}
                    <motion.div
                        className="relative"
                        initial={{ opacity: 0, x: -50 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true, margin: "-100px" }}
                    >
                        <div className="relative bg-[#0f0f0f] rounded-2xl p-4 border border-white/10 overflow-hidden">
                            {/* Video Container */}
                            <div
                                className="relative aspect-video rounded-xl overflow-hidden bg-black cursor-pointer group"
                                onMouseEnter={() => setIsHovering(true)}
                                onMouseLeave={() => setIsHovering(false)}
                            >
                                <video
                                    ref={videoRef}
                                    className="w-full h-full object-cover"
                                    poster="/demo/anubis_extension_demo.mp4"
                                    controls
                                    autoPlay
                                    muted
                                    loop
                                    onPlay={() => setIsPlaying(true)}
                                    onPause={() => setIsPlaying(false)}
                                >
                                    <source src="/demo/anubis_extension_demo.mp4" type="video/mp4" />
                                    Your browser does not support the video tag.
                                </video>

                                {/* Play Button Overlay */}
                                {!isPlaying && (
                                    <motion.div
                                        className="absolute inset-0 flex items-center justify-center bg-black/30"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                    >
                                        <motion.div
                                            className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30"
                                            whileHover={{ scale: 1.1, backgroundColor: "rgba(255, 255, 255, 0.3)" }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <FaPlay className="text-white text-2xl ml-1" />
                                        </motion.div>
                                    </motion.div>
                                )}

                                {/* Fullscreen Overlay */}
                                <motion.div
                                    className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-all duration-200 ${isHovering ? 'pointer-events-auto' : 'pointer-events-none'}`}
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: isHovering ? 1 : 0 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <motion.button
                                        onClick={handleFullscreen}
                                        className="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30 hover:bg-white/30 transition-all duration-200 group"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <div className="text-center">
                                            <FaExpand className="text-white text-3xl mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
                                            <p className="text-white font-orbitron font-semibold text-sm">Fullscreen</p>
                                        </div>
                                    </motion.button>
                                </motion.div>
                            </div>

                            {/* Subtle Glow Effect */}
                            <motion.div
                                className="absolute -inset-1 bg-white/5 rounded-2xl blur-xl"
                                animate={{
                                    opacity: [0.1, 0.2, 0.1],
                                }}
                                transition={{
                                    duration: 4,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            />
                        </div>

                        {/* Floating Elements */}
                        <motion.div
                            className="absolute -top-4 -right-4 w-8 h-8 bg-white/10 rounded-full"
                            animate={{
                                y: [0, -10, 0],
                                scale: [1, 1.1, 1],
                            }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                        />
                        <motion.div
                            className="absolute -bottom-4 -left-4 w-6 h-6 bg-white/10 rounded-full"
                            animate={{
                                y: [0, 10, 0],
                                scale: [1, 0.9, 1],
                            }}
                            transition={{
                                duration: 2.5,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: 0.5
                            }}
                        />
                    </motion.div>

                    {/* Features and CTA Section */}
                    <motion.div
                        className="space-y-8"
                        initial={{ opacity: 0, x: 50 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        viewport={{ once: true, margin: "-100px" }}
                    >
                        {/* Features Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    className="bg-[#0f0f0f] p-4 rounded-lg border border-white/10 hover:border-white/20 transition-all duration-300 group"
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5, delay: index * 0.1 }}
                                    viewport={{ once: true, margin: "-100px" }}
                                    whileHover={{
                                        scale: 1.02,
                                        backgroundColor: "rgba(15, 15, 15, 0.9)"
                                    }}
                                >
                                    <div className="flex items-start space-x-3">
                                        <div className="text-2xl mt-1 group-hover:scale-110 transition-transform duration-200">
                                            {feature.icon}
                                        </div>
                                        <div>
                                            <h3 className="font-orbitron font-semibold text-white mb-1 group-hover:text-white/90 transition-colors duration-200">{feature.title}</h3>
                                            <p className="text-gray-400 text-sm font-space-grotesk group-hover:text-gray-300 transition-colors duration-200">{feature.description}</p>
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </div>

                        {/* CTA Section */}
                        <motion.div
                            className="bg-[#0f0f0f] p-6 rounded-xl border border-white/20"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            viewport={{ once: true, margin: "-100px" }}
                        >
                            <h3 className="font-orbitron font-bold text-xl text-white mb-3">
                                Ready to Transform Your Trading?
                            </h3>
                            <p className="text-gray-300 mb-6 font-space-grotesk">
                                Install the Anubis Browser Extension and start trading directly from Dexscreener with unprecedented speed and efficiency.
                            </p>

                            <Link href="/browser-extension/download">
                                <motion.button
                                    className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-lg hover:bg-gray-100 transition-all duration-300 w-full md:w-auto group"
                                    whileHover={{
                                        scale: 1.05,
                                        boxShadow: "0 0 20px rgba(255, 255, 255, 0.2)"
                                    }}
                                    whileTap={{
                                        scale: 0.95
                                    }}
                                >
                                    <span className="flex items-center justify-center space-x-2">
                                        <span>Download Extension</span>
                                        <motion.span
                                            className="group-hover:translate-x-1 transition-transform duration-200"
                                            initial={{ x: 0 }}
                                        >
                                            →
                                        </motion.span>
                                    </span>
                                </motion.button>
                            </Link>
                        </motion.div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
} 