module.exports = {

"[project]/node_modules/react-loader-spinner/dist/module.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Audio": (()=>$dcdd04c60cd78d69$export$153755f98d9861de),
    "BallTriangle": (()=>$e035d01ad1d05b44$export$68949ad0373623af),
    "Bars": (()=>$7dd1b251b360e95a$export$fbc7d6f7dd821b47),
    "Blocks": (()=>$ffa7e3ac27a21a71$export$2ba1b65b747a57aa),
    "Circles": (()=>$29b6b1f956162f74$export$765808835a2dc0a2),
    "CirclesWithBar": (()=>$12bd062f0f060b07$export$17c11650828d97e),
    "ColorRing": (()=>$7097090906378a5b$export$dc036a5afb9ca26f),
    "Comment": (()=>$81e36fafa9b58989$export$4d299b491347818a),
    "DNA": (()=>$bb8e4335d7ee0654$export$bee07fdc425df572),
    "Discuss": (()=>$50138037f422b463$export$f93420b62a5bdffa),
    "FallingLines": (()=>$b184d2a88a50e3dc$export$1ed1943372cc63a9),
    "FidgetSpinner": (()=>$1d8c9163e13b7bf7$export$8e3fad5cade57efa),
    "Grid": (()=>$b438e21e66fce243$export$ef2184bd89960b14),
    "Hearts": (()=>$88eb2f870dd9f437$export$2da2f0c7403af3ce),
    "Hourglass": (()=>$1e82ee682f5b64b8$export$f3c41beb83007357),
    "InfinitySpin": (()=>$ad60b992c945fdb5$export$8009d4483dfda42),
    "LineWave": (()=>$05da46d92e4baf0c$export$d2101d81f63866ab),
    "MagnifyingGlass": (()=>$db94311ffb982ec6$export$bdf537af43a20db5),
    "MutatingDots": (()=>$05cab5f4cf092036$export$64ea884904791f4),
    "Oval": (()=>$a5fa864d4dd36deb$export$67ad50c48ca3ede4),
    "ProgressBar": (()=>$075a2f0ea0d9df8a$export$c17561cb55d4db30),
    "Puff": (()=>$8a2963a7161a08e2$export$83d2259ec538613b),
    "Radio": (()=>$daf95de783b7b8b1$export$d7b12c4107be0d61),
    "RevolvingDot": (()=>$f6f65ef73d86a35a$export$8e22e563e5362f75),
    "Rings": (()=>$0da8ebf0340870f3$export$fdd9e2f491a77de7),
    "RotatingLines": (()=>$5819da83a926266a$export$d20df8773b6b77b5),
    "RotatingSquare": (()=>$30f4fc5ff137b595$export$bb511942ded86554),
    "RotatingTriangles": (()=>$aa2b177fb9ef5dee$export$f64f16a115ce395d),
    "TailSpin": (()=>$56d89154a59e79d3$export$f8e5ae7506d65b32),
    "ThreeCircles": (()=>$5cff71254109409f$export$e21573137ccb7f5d),
    "ThreeDots": (()=>$f0c3e3bb3e76d210$export$4bf83b24a11cff0b),
    "Triangle": (()=>$afa12dd3e98f740f$export$5a465592bfe74b48),
    "Vortex": (()=>$5ad4f4dbdb85103b$export$d25f4198d7ad6c78),
    "Watch": (()=>$e3e50827b57d879a$export$4c68f1a79f88778c)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-components/dist/styled-components.esm.js [app-ssr] (ecmascript)");
;
;
;
// Such export is called Tree Shaking. It allows to import only the components
// that are needed while webpack will remove the rest of the code from the bundle.
const $84fda1e7e33cfd28$export$37394b0fa44b998c = "#4fa94d";
const $84fda1e7e33cfd28$export$6bfda33bcd6c2d18 = {
    "aria-busy": true,
    role: "progressbar"
};
const $4c3f0b77e8caf06d$export$21d9f1931ef75b56 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).div`
  display: ${(props)=>props.$visible ? "flex" : "none"};
`;
const $eb040f10400edc38$export$98a285aab16ab26c = "http://www.w3.org/2000/svg";
const $dcdd04c60cd78d69$export$153755f98d9861de = ({ height: height = "100", width: width = "100", color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "audio-loading", wrapperStyle: wrapperStyle = {}, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        $visible: visible,
        style: {
            ...wrapperStyle
        },
        className: wrapperClass,
        "data-testid": "audio-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            height: `${height}`,
            width: `${width}`,
            fill: color,
            viewBox: "0 0 55 80",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            "data-testid": "audio-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("title", {
                    children: "Audio Visualization"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("desc", {
                    children: "Animated representation of audio data"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                    transform: "matrix(1 0 0 -1 0 80)",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            width: "10",
                            height: "20",
                            rx: "3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "height",
                                begin: "0s",
                                dur: "4.3s",
                                values: "20;45;57;80;64;32;66;45;64;23;66;13;64;56;34;34;2;23;76;79;20",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "15",
                            width: "10",
                            height: "80",
                            rx: "3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "height",
                                begin: "0s",
                                dur: "2s",
                                values: "80;55;33;5;75;23;73;33;12;14;60;80",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "30",
                            width: "10",
                            height: "50",
                            rx: "3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "height",
                                begin: "0s",
                                dur: "1.4s",
                                values: "50;34;78;23;56;23;34;76;80;54;21;50",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "45",
                            width: "10",
                            height: "30",
                            rx: "3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "height",
                                begin: "0s",
                                dur: "2s",
                                values: "30;45;13;80;56;72;45;76;34;23;67;30",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            })
                        })
                    ]
                })
            ]
        })
    });
const $e035d01ad1d05b44$export$68949ad0373623af = ({ height: height = 100, width: width = 100, radius: radius = 5, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "ball-triangle-loading", wrapperClass: wrapperClass, wrapperStyle: wrapperStyle, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: {
            ...wrapperStyle
        },
        $visible: visible,
        className: wrapperClass,
        "data-testid": "ball-triangle-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            height: height,
            width: width,
            stroke: color,
            viewBox: "0 0 57 57",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            "data-testid": "ball-triangle-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("title", {
                    children: "Ball Triangle"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("desc", {
                    children: "Animated representation of three balls"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                    fill: "none",
                    fillRule: "evenodd",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        transform: "translate(1 1)",
                        strokeWidth: "2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                                cx: "5",
                                cy: "50",
                                r: radius,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                        attributeName: "cy",
                                        begin: "0s",
                                        dur: "2.2s",
                                        values: "50;5;50;50",
                                        calcMode: "linear",
                                        repeatCount: "indefinite"
                                    }),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                        attributeName: "cx",
                                        begin: "0s",
                                        dur: "2.2s",
                                        values: "5;27;49;5",
                                        calcMode: "linear",
                                        repeatCount: "indefinite"
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                                cx: "27",
                                cy: "5",
                                r: radius,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                        attributeName: "cy",
                                        begin: "0s",
                                        dur: "2.2s",
                                        from: "5",
                                        to: "5",
                                        values: "5;50;50;5",
                                        calcMode: "linear",
                                        repeatCount: "indefinite"
                                    }),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                        attributeName: "cx",
                                        begin: "0s",
                                        dur: "2.2s",
                                        from: "27",
                                        to: "27",
                                        values: "27;49;5;27",
                                        calcMode: "linear",
                                        repeatCount: "indefinite"
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                                cx: "49",
                                cy: "50",
                                r: radius,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                        attributeName: "cy",
                                        begin: "0s",
                                        dur: "2.2s",
                                        values: "50;50;5;50",
                                        calcMode: "linear",
                                        repeatCount: "indefinite"
                                    }),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                        attributeName: "cx",
                                        from: "49",
                                        to: "49",
                                        begin: "0s",
                                        dur: "2.2s",
                                        values: "49;5;27;49",
                                        calcMode: "linear",
                                        repeatCount: "indefinite"
                                    })
                                ]
                            })
                        ]
                    })
                })
            ]
        })
    });
const $7dd1b251b360e95a$export$fbc7d6f7dd821b47 = ({ height: height = 80, width: width = 80, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "bars-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        $visible: visible,
        style: {
            ...wrapperStyle
        },
        className: wrapperClass,
        "data-testid": "bars-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            fill: color,
            viewBox: "0 0 135 140",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            "data-testid": "bars-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                    y: "10",
                    width: "15",
                    height: "120",
                    rx: "6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "height",
                            begin: "0.5s",
                            dur: "1s",
                            values: "120;110;100;90;80;70;60;50;40;140;120",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "y",
                            begin: "0.5s",
                            dur: "1s",
                            values: "10;15;20;25;30;35;40;45;50;0;10",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                    x: "30",
                    y: "10",
                    width: "15",
                    height: "120",
                    rx: "6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "height",
                            begin: "0.25s",
                            dur: "1s",
                            values: "120;110;100;90;80;70;60;50;40;140;120",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "y",
                            begin: "0.25s",
                            dur: "1s",
                            values: "10;15;20;25;30;35;40;45;50;0;10",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                    x: "60",
                    width: "15",
                    height: "140",
                    rx: "6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "height",
                            begin: "0s",
                            dur: "1s",
                            values: "120;110;100;90;80;70;60;50;40;140;120",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "y",
                            begin: "0s",
                            dur: "1s",
                            values: "10;15;20;25;30;35;40;45;50;0;10",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                    x: "90",
                    y: "10",
                    width: "15",
                    height: "120",
                    rx: "6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "height",
                            begin: "0.25s",
                            dur: "1s",
                            values: "120;110;100;90;80;70;60;50;40;140;120",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "y",
                            begin: "0.25s",
                            dur: "1s",
                            values: "10;15;20;25;30;35;40;45;50;0;10",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                    x: "120",
                    y: "10",
                    width: "15",
                    height: "120",
                    rx: "6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "height",
                            begin: "0.5s",
                            dur: "1s",
                            values: "120;110;100;90;80;70;60;50;40;140;120",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "y",
                            begin: "0.5s",
                            dur: "1s",
                            values: "10;15;20;25;30;35;40;45;50;0;10",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                })
            ]
        })
    });
const $29b6b1f956162f74$export$765808835a2dc0a2 = ({ height: height = 80, width: width = 80, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "circles-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "aria-label": ariaLabel,
        "data-testid": "circles-loading",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            viewBox: "0 0 135 135",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            fill: color,
            "data-testid": "circles-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("title", {
                    children: "circles-loading"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("desc", {
                    children: "Animated representation of circles"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    d: "M67.447 58c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10zm9.448 9.447c0 5.523 4.477 10 10 10 5.522 0 10-4.477 10-10s-4.478-10-10-10c-5.523 0-10 4.477-10 10zm-9.448 9.448c-5.523 0-10 4.477-10 10 0 5.522 4.477 10 10 10s10-4.478 10-10c0-5.523-4.477-10-10-10zM58 67.447c0-5.523-4.477-10-10-10s-10 4.477-10 10 4.477 10 10 10 10-4.477 10-10z",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        type: "rotate",
                        from: "0 67 67",
                        to: "-360 67 67",
                        dur: "2.5s",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    d: "M28.19 40.31c6.627 0 12-5.374 12-12 0-6.628-5.373-12-12-12-6.628 0-12 5.372-12 12 0 6.626 5.372 12 12 12zm30.72-19.825c4.686 4.687 12.284 4.687 16.97 0 4.686-4.686 4.686-12.284 0-16.97-4.686-4.687-12.284-4.687-16.97 0-4.687 4.686-4.687 12.284 0 16.97zm35.74 7.705c0 6.627 5.37 12 12 12 6.626 0 12-5.373 12-12 0-6.628-5.374-12-12-12-6.63 0-12 5.372-12 12zm19.822 30.72c-4.686 4.686-4.686 12.284 0 16.97 4.687 4.686 12.285 4.686 16.97 0 4.687-4.686 4.687-12.284 0-16.97-4.685-4.687-12.283-4.687-16.97 0zm-7.704 35.74c-6.627 0-12 5.37-12 12 0 6.626 5.373 12 12 12s12-5.374 12-12c0-6.63-5.373-12-12-12zm-30.72 19.822c-4.686-4.686-12.284-4.686-16.97 0-4.686 4.687-4.686 12.285 0 16.97 4.686 4.687 12.284 4.687 16.97 0 4.687-4.685 4.687-12.283 0-16.97zm-35.74-7.704c0-6.627-5.372-12-12-12-6.626 0-12 5.373-12 12s5.374 12 12 12c6.628 0 12-5.373 12-12zm-19.823-30.72c4.687-4.686 4.687-12.284 0-16.97-4.686-4.686-12.284-4.686-16.97 0-4.687 4.686-4.687 12.284 0 16.97 4.686 4.687 12.284 4.687 16.97 0z",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        type: "rotate",
                        from: "0 67 67",
                        to: "360 67 67",
                        dur: "8s",
                        repeatCount: "indefinite"
                    })
                })
            ]
        })
    });
const $12bd062f0f060b07$export$17c11650828d97e = ({ wrapperStyle: wrapperStyle = {}, visible: visible = true, wrapperClass: wrapperClass = "", height: height = 100, width: width = 100, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), outerCircleColor: outerCircleColor, innerCircleColor: innerCircleColor, barColor: barColor, ariaLabel: ariaLabel = "circles-with-bar-loading" })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        "data-testid": "circles-with-bar-wrapper",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            version: "1.1",
            id: "L1",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            x: "0px",
            y: "0px",
            height: `${height}`,
            width: `${width}`,
            viewBox: "0 0 100 100",
            enableBackground: "new 0 0 100 100",
            xmlSpace: "preserve",
            "data-testid": "circles-with-bar-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("title", {
                    children: "circles-with-bar-loading"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("desc", {
                    children: "Animated representation of circles with bar"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    fill: "none",
                    stroke: `${outerCircleColor || color}`,
                    strokeWidth: "6",
                    strokeMiterlimit: "15",
                    strokeDasharray: "14.2472,14.2472",
                    cx: "50",
                    cy: "50",
                    r: "47",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        attributeType: "XML",
                        type: "rotate",
                        dur: "5s",
                        from: "0 50 50",
                        to: "360 50 50",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    fill: "none",
                    stroke: `${innerCircleColor || color}`,
                    strokeWidth: "1",
                    strokeMiterlimit: "10",
                    strokeDasharray: "10,10",
                    cx: "50",
                    cy: "50",
                    r: "39",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        attributeType: "XML",
                        type: "rotate",
                        dur: "5s",
                        from: "0 50 50",
                        to: "-360 50 50",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                    fill: `${barColor || color}`,
                    "data-testid": "circles-with-bar-svg-bar",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "30",
                            y: "35",
                            width: "5",
                            height: "30",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                dur: "1s",
                                type: "translate",
                                values: "0 5 ; 0 -5; 0 5",
                                repeatCount: "indefinite",
                                begin: "0.1"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "40",
                            y: "35",
                            width: "5",
                            height: "30",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                dur: "1s",
                                type: "translate",
                                values: "0 5 ; 0 -5; 0 5",
                                repeatCount: "indefinite",
                                begin: "0.2"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "50",
                            y: "35",
                            width: "5",
                            height: "30",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                dur: "1s",
                                type: "translate",
                                values: "0 5 ; 0 -5; 0 5",
                                repeatCount: "indefinite",
                                begin: "0.3"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "60",
                            y: "35",
                            width: "5",
                            height: "30",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                dur: "1s",
                                type: "translate",
                                values: "0 5 ; 0 -5; 0 5",
                                repeatCount: "indefinite",
                                begin: "0.4"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                            x: "70",
                            y: "35",
                            width: "5",
                            height: "30",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                dur: "1s",
                                type: "translate",
                                values: "0 5 ; 0 -5; 0 5",
                                repeatCount: "indefinite",
                                begin: "0.5"
                            })
                        })
                    ]
                })
            ]
        })
    });
};
const $b438e21e66fce243$export$ef2184bd89960b14 = ({ height: height = 80, width: width = 80, radius: radius = 12.5, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "grid-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "grid-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            viewBox: "0 0 105 105",
            fill: color,
            "data-testid": "grid-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "12.5",
                    cy: "12.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "0s",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "12.5",
                    cy: "52.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "100ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "52.5",
                    cy: "12.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "300ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "52.5",
                    cy: "52.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "600ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "92.5",
                    cy: "12.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "800ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "92.5",
                    cy: "52.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "400ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "12.5",
                    cy: "92.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "700ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "52.5",
                    cy: "92.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "500ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    cx: "92.5",
                    cy: "92.5",
                    r: `${radius}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "200ms",
                        dur: "1s",
                        values: "1;.2;1",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                })
            ]
        })
    });
const $88eb2f870dd9f437$export$2da2f0c7403af3ce = ({ height: height = 80, width: width = 80, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "hearts-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "hearts-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            viewBox: "0 0 140 64",
            xmlns: "http://www.w3.org/2000/svg",
            fill: color,
            "data-testid": "hearts-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    d: "M30.262 57.02L7.195 40.723c-5.84-3.976-7.56-12.06-3.842-18.063 3.715-6 11.467-7.65 17.306-3.68l4.52 3.76 2.6-5.274c3.717-6.002 11.47-7.65 17.305-3.68 5.84 3.97 7.56 12.054 3.842 18.062L34.49 56.118c-.897 1.512-2.793 1.915-4.228.9z",
                    attributeName: "fill-opacity",
                    from: "0",
                    to: ".5",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "0s",
                        dur: "1.4s",
                        values: "0.5;1;0.5",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    d: "M105.512 56.12l-14.44-24.272c-3.716-6.008-1.996-14.093 3.843-18.062 5.835-3.97 13.588-2.322 17.306 3.68l2.6 5.274 4.52-3.76c5.84-3.97 13.592-2.32 17.307 3.68 3.718 6.003 1.998 14.088-3.842 18.064L109.74 57.02c-1.434 1.014-3.33.61-4.228-.9z",
                    attributeName: "fill-opacity",
                    from: "0",
                    to: ".5",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill-opacity",
                        begin: "0.7s",
                        dur: "1.4s",
                        values: "0.5;1;0.5",
                        calcMode: "linear",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    d: "M67.408 57.834l-23.01-24.98c-5.864-6.15-5.864-16.108 0-22.248 5.86-6.14 15.37-6.14 21.234 0L70 16.168l4.368-5.562c5.863-6.14 15.375-6.14 21.235 0 5.863 6.14 5.863 16.098 0 22.247l-23.007 24.98c-1.43 1.556-3.757 1.556-5.188 0z"
                })
            ]
        })
    });
const $ad60b992c945fdb5$var$len = 242.776657104492;
const $ad60b992c945fdb5$var$time = 1.6;
const $ad60b992c945fdb5$var$anim = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"])`
12.5% {
  stroke-dasharray: ${$ad60b992c945fdb5$var$len * 0.14}px, ${$ad60b992c945fdb5$var$len}px;
  stroke-dashoffset: -${$ad60b992c945fdb5$var$len * 0.11}px;
}
43.75% {
  stroke-dasharray: ${$ad60b992c945fdb5$var$len * 0.35}px, ${$ad60b992c945fdb5$var$len}px;
  stroke-dashoffset: -${$ad60b992c945fdb5$var$len * 0.35}px;
}
100% {
  stroke-dasharray: ${$ad60b992c945fdb5$var$len * 0.01}px, ${$ad60b992c945fdb5$var$len}px;
  stroke-dashoffset: -${$ad60b992c945fdb5$var$len * 0.99}px;
}
`;
const $ad60b992c945fdb5$var$Path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).path`
  stroke-dasharray: ${$ad60b992c945fdb5$var$len * 0.01}px, ${$ad60b992c945fdb5$var$len};
  stroke-dashoffset: 0;
  animation: ${$ad60b992c945fdb5$var$anim} ${$ad60b992c945fdb5$var$time}s linear infinite;
`;
const $ad60b992c945fdb5$export$8009d4483dfda42 = ({ color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), width: width = "200" })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        width: `${width}`,
        height: `${Number(width) * 0.5}`,
        viewBox: `0 0 ${width} ${Number(100)}`,
        "data-testid": "infinity-spin",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])($ad60b992c945fdb5$var$Path, {
                "data-testid": "infinity-spin-path-1",
                stroke: color,
                fill: "none",
                strokeWidth: "4",
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeMiterlimit: "10",
                d: "M93.9,46.4c9.3,9.5,13.8,17.9,23.5,17.9s17.5-7.8,17.5-17.5s-7.8-17.6-17.5-17.5c-9.7,0.1-13.3,7.2-22.1,17.1 c-8.9,8.8-15.7,17.9-25.4,17.9s-17.5-7.8-17.5-17.5s7.8-17.5,17.5-17.5S86.2,38.6,93.9,46.4z"
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                "data-testid": "infinity-spin-path-2",
                opacity: "0.07",
                fill: "none",
                stroke: color,
                strokeWidth: "4",
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeMiterlimit: "10",
                d: "M93.9,46.4c9.3,9.5,13.8,17.9,23.5,17.9s17.5-7.8,17.5-17.5s-7.8-17.6-17.5-17.5c-9.7,0.1-13.3,7.2-22.1,17.1 c-8.9,8.8-15.7,17.9-25.4,17.9s-17.5-7.8-17.5-17.5s7.8-17.5,17.5-17.5S86.2,38.6,93.9,46.4z"
            })
        ]
    });
};
const $05da46d92e4baf0c$export$d2101d81f63866ab = ({ wrapperStyle: wrapperStyle = {}, visible: visible = true, wrapperClass: wrapperClass = "", height: height = 100, width: width = 100, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "line-wave-loading", firstLineColor: firstLineColor, middleLineColor: middleLineColor, lastLineColor: lastLineColor })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "line-wave-wrapper",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            version: "1.1",
            height: `${height}`,
            width: `${width}`,
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            x: "0px",
            y: "0px",
            viewBox: "0 0 100 100",
            enableBackground: "new 0 0 0 0",
            xmlSpace: "preserve",
            "data-testid": "line-wave-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                    x: "20",
                    y: "50",
                    width: "4",
                    height: "10",
                    fill: firstLineColor || color,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeType: "xml",
                        attributeName: "transform",
                        type: "translate",
                        values: "0 0; 0 20; 0 0",
                        begin: "0",
                        dur: "0.6s",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                    x: "30",
                    y: "50",
                    width: "4",
                    height: "10",
                    fill: middleLineColor || color,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeType: "xml",
                        attributeName: "transform",
                        type: "translate",
                        values: "0 0; 0 20; 0 0",
                        begin: "0.2s",
                        dur: "0.6s",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                    x: "40",
                    y: "50",
                    width: "4",
                    height: "10",
                    fill: lastLineColor || color,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeType: "xml",
                        attributeName: "transform",
                        type: "translate",
                        values: "0 0; 0 20; 0 0",
                        begin: "0.4s",
                        dur: "0.6s",
                        repeatCount: "indefinite"
                    })
                })
            ]
        })
    });
};
const $05cab5f4cf092036$export$64ea884904791f4 = ({ height: height = 90, width: width = 80, radius: radius = 12.5, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), secondaryColor: secondaryColor = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "mutating-dots-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "mutating-dots-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            id: "goo-loader",
            width: width,
            height: height,
            "data-testid": "mutating-dots-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("filter", {
                    id: "fancy-goo",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("feGaussianBlur", {
                            in: "SourceGraphic",
                            stdDeviation: "6",
                            result: "blur"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("feColorMatrix", {
                            in: "blur",
                            mode: "matrix",
                            values: "1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -9",
                            result: "goo"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("feComposite", {
                            in: "SourceGraphic",
                            in2: "goo",
                            operator: "atop"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                    filter: "url(#fancy-goo)",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                            id: "mainAnim",
                            attributeName: "transform",
                            attributeType: "XML",
                            type: "rotate",
                            from: "0 50 50",
                            to: "359 50 50",
                            dur: "1.2s",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                            cx: "50%",
                            cy: "40",
                            r: radius,
                            fill: color,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                id: "cAnim1",
                                attributeType: "XML",
                                attributeName: "cy",
                                dur: "0.6s",
                                begin: "0;cAnim1.end+0.2s",
                                calcMode: "spline",
                                values: "40;20;40",
                                keyTimes: "0;0.3;1",
                                keySplines: "0.09, 0.45, 0.16, 1;0.09, 0.45, 0.16, 1"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                            cx: "50%",
                            cy: "60",
                            r: radius,
                            fill: secondaryColor,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                id: "cAnim2",
                                attributeType: "XML",
                                attributeName: "cy",
                                dur: "0.6s",
                                begin: "0.4s;cAnim2.end+0.2s",
                                calcMode: "spline",
                                values: "60;80;60",
                                keyTimes: "0;0.3;1",
                                keySplines: "0.09, 0.45, 0.16, 1;0.09, 0.45, 0.16, 1"
                            })
                        })
                    ]
                })
            ]
        })
    });
/**
 * The radius of the circle
 * The Loader size is set with the width and height of the SVG
 * @type {number}
 */ const $a5fa864d4dd36deb$var$RADIUS = 20;
/**
 * Compute Path depending on circle radius
 * The structure with radius 20 is "M20 0c0-9.94-8.06-20-20-20"
 * @param radius of the circle radius default 20
 * @returns {string}
 */ const $a5fa864d4dd36deb$var$getPath = (radius)=>{
    return [
        "M" + radius + " 0c0-9.94-8.06",
        radius,
        radius,
        radius
    ].join("-");
};
/**
 * Compute the size of the view box depending on the radius and Stroke-Width
 * @param strokeWidth Stroke-Width of the full circle
 * @param secondaryStrokeWidth Stroke-Width of the 1/4 circle
 * @param radius radius of the circle
 * @returns {string}
 */ const $a5fa864d4dd36deb$var$getViewBoxSize = (strokeWidth, secondaryStrokeWidth, radius)=>{
    const maxStrokeWidth = Math.max(strokeWidth, secondaryStrokeWidth);
    const startingPoint = -radius - maxStrokeWidth / 2 + 1;
    const endpoint = radius * 2 + maxStrokeWidth;
    return [
        startingPoint,
        startingPoint,
        endpoint,
        endpoint
    ].join(" ");
};
const $a5fa864d4dd36deb$export$67ad50c48ca3ede4 = ({ height: height = 80, width: width = 80, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), secondaryColor: secondaryColor = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "oval-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true, strokeWidth: strokeWidth = 2, strokeWidthSecondary: strokeWidthSecondary })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "oval-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
            width: width,
            height: height,
            viewBox: $a5fa864d4dd36deb$var$getViewBoxSize(Number(strokeWidth), Number(strokeWidthSecondary || strokeWidth), $a5fa864d4dd36deb$var$RADIUS),
            xmlns: "http://www.w3.org/2000/svg",
            stroke: color,
            "data-testid": "oval-svg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                fill: "none",
                fillRule: "evenodd",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                    transform: "translate(1 1)",
                    strokeWidth: Number(strokeWidthSecondary || strokeWidth),
                    "data-testid": "oval-secondary-group",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                            strokeOpacity: ".5",
                            cx: "0",
                            cy: "0",
                            r: $a5fa864d4dd36deb$var$RADIUS,
                            stroke: secondaryColor,
                            strokeWidth: strokeWidth
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: $a5fa864d4dd36deb$var$getPath($a5fa864d4dd36deb$var$RADIUS),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                type: "rotate",
                                from: "0 0 0",
                                to: "360 0 0",
                                dur: "1s",
                                repeatCount: "indefinite"
                            })
                        })
                    ]
                })
            })
        })
    });
const $8a2963a7161a08e2$export$83d2259ec538613b = ({ height: height = 80, width: width = 80, radius: radius = 1, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "puff-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "puff-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
            width: width,
            height: height,
            viewBox: "0 0 44 44",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            stroke: color,
            "data-testid": "puff-svg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                fill: "none",
                fillRule: "evenodd",
                strokeWidth: "2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                        cx: "22",
                        cy: "22",
                        r: radius,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "r",
                                begin: "0s",
                                dur: "1.8s",
                                values: "1; 20",
                                calcMode: "spline",
                                keyTimes: "0; 1",
                                keySplines: "0.165, 0.84, 0.44, 1",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "strokeOpacity",
                                begin: "0s",
                                dur: "1.8s",
                                values: "1; 0",
                                calcMode: "spline",
                                keyTimes: "0; 1",
                                keySplines: "0.3, 0.61, 0.355, 1",
                                repeatCount: "indefinite"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                        cx: "22",
                        cy: "22",
                        r: radius,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "r",
                                begin: "-0.9s",
                                dur: "1.8s",
                                values: "1; 20",
                                calcMode: "spline",
                                keyTimes: "0; 1",
                                keySplines: "0.165, 0.84, 0.44, 1",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "strokeOpacity",
                                begin: "-0.9s",
                                dur: "1.8s",
                                values: "1; 0",
                                calcMode: "spline",
                                keyTimes: "0; 1",
                                keySplines: "0.3, 0.61, 0.355, 1",
                                repeatCount: "indefinite"
                            })
                        ]
                    })
                ]
            })
        })
    });
const $f6f65ef73d86a35a$export$8e22e563e5362f75 = ({ radius: radius = 45, strokeWidth: strokeWidth = 5, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), secondaryColor: secondaryColor, ariaLabel: ariaLabel = "revolving-dot-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "aria-label": ariaLabel,
        "data-testid": "revolving-dot-loading",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            version: "1.1",
            width: `calc(${radius} * 2.5)`,
            height: `calc(${radius} * 2.5)`,
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            x: "0px",
            y: "0px",
            "data-testid": "revolving-dot-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    fill: "none",
                    stroke: secondaryColor || color,
                    strokeWidth: strokeWidth,
                    cx: `calc(${radius} * 1.28)`,
                    cy: `calc(${radius} * 1.28)`,
                    r: radius,
                    style: {
                        opacity: 0.5
                    }
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    fill: color,
                    stroke: color,
                    strokeWidth: "3",
                    cx: `calc(${radius} * 1.28)`,
                    cy: `calc(${radius} / 3.5)`,
                    r: `calc(${radius} / 5)`,
                    style: {
                        transformOrigin: "50% 50%"
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        dur: "2s",
                        type: "rotate",
                        from: "0",
                        to: "360",
                        repeatCount: "indefinite"
                    })
                })
            ]
        })
    });
const $0da8ebf0340870f3$export$fdd9e2f491a77de7 = ({ height: height = 80, width: width = 80, radius: radius = 6, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "rings-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "rings-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
            width: width,
            height: height,
            viewBox: "0 0 45 45",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            stroke: color,
            "data-testid": "rings-svg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                fill: "none",
                fillRule: "evenodd",
                transform: "translate(1 1)",
                strokeWidth: "2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                        cx: "22",
                        cy: "22",
                        r: radius,
                        strokeOpacity: "0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "r",
                                begin: "1.5s",
                                dur: "3s",
                                values: "6;22",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "stroke-opacity",
                                begin: "1.5s",
                                dur: "3s",
                                values: "1;0",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "stroke-width",
                                begin: "1.5s",
                                dur: "3s",
                                values: "2;0",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                        cx: "22",
                        cy: "22",
                        r: radius,
                        strokeOpacity: "0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "r",
                                begin: "3s",
                                dur: "3s",
                                values: "6;22",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "strokeOpacity",
                                begin: "3s",
                                dur: "3s",
                                values: "1;0",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "strokeWidth",
                                begin: "3s",
                                dur: "3s",
                                values: "2;0",
                                calcMode: "linear",
                                repeatCount: "indefinite"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                        cx: "22",
                        cy: "22",
                        r: Number(radius) + 2,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "r",
                            begin: "0s",
                            dur: "1.5s",
                            values: "6;1;2;3;4;5;6",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    })
                ]
            })
        })
    });
const $30f4fc5ff137b595$export$bb511942ded86554 = ({ wrapperClass: wrapperClass = "", color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), height: height = 100, width: width = 100, strokeWidth: strokeWidth = 4, ariaLabel: ariaLabel = "rotating-square-loading", wrapperStyle: wrapperStyle = {}, visible: visible = true })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "rotating-square-wrapper",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            version: "1.1",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            x: "0px",
            y: "0px",
            viewBox: "0 0 100 100",
            enableBackground: "new 0 0 100 100",
            height: `${height}`,
            width: `${width}`,
            "data-testid": "rotating-square-svg",
            xmlSpace: "preserve",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                    fill: "none",
                    stroke: color,
                    strokeWidth: strokeWidth,
                    x: "25",
                    y: "25",
                    width: "50",
                    height: "50",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        dur: "0.5s",
                        from: "0 50 50",
                        to: "180 50 50",
                        type: "rotate",
                        id: "strokeBox",
                        attributeType: "XML",
                        begin: "rectBox.end"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                    x: "27",
                    y: "27",
                    fill: color,
                    width: "46",
                    height: "50",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "height",
                        dur: "1.3s",
                        attributeType: "XML",
                        from: "50",
                        to: "0",
                        id: "rectBox",
                        fill: "freeze",
                        begin: "0s;strokeBox.end"
                    })
                })
            ]
        })
    });
};
const $5819da83a926266a$var$POINTS = [
    0,
    30,
    60,
    90,
    120,
    150,
    180,
    210,
    240,
    270,
    300,
    330
];
const $5819da83a926266a$var$spin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"])`
to {
   transform: rotate(360deg);
 }
`;
const $5819da83a926266a$var$Svg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).svg`
  animation: ${$5819da83a926266a$var$spin} 0.75s steps(12, end) infinite;
  animation-duration: 0.75s;
`;
const $5819da83a926266a$var$Polyline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).polyline`
  stroke-width: ${(props)=>props.width}px;
  stroke-linecap: round;

  &:nth-child(12n + 0) {
    stroke-opacity: 0.08;
  }

  &:nth-child(12n + 1) {
    stroke-opacity: 0.17;
  }

  &:nth-child(12n + 2) {
    stroke-opacity: 0.25;
  }

  &:nth-child(12n + 3) {
    stroke-opacity: 0.33;
  }

  &:nth-child(12n + 4) {
    stroke-opacity: 0.42;
  }

  &:nth-child(12n + 5) {
    stroke-opacity: 0.5;
  }

  &:nth-child(12n + 6) {
    stroke-opacity: 0.58;
  }

  &:nth-child(12n + 7) {
    stroke-opacity: 0.66;
  }

  &:nth-child(12n + 8) {
    stroke-opacity: 0.75;
  }

  &:nth-child(12n + 9) {
    stroke-opacity: 0.83;
  }

  &:nth-child(12n + 11) {
    stroke-opacity: 0.92;
  }
`;
const $5819da83a926266a$export$d20df8773b6b77b5 = ({ strokeColor: strokeColor = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), strokeWidth: strokeWidth = "5", animationDuration: animationDuration = "0.75", width: width = "96", visible: visible = true, ariaLabel: ariaLabel = "rotating-lines-loading" })=>{
    const lines = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>$5819da83a926266a$var$POINTS.map((point)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])($5819da83a926266a$var$Polyline, {
                points: "24,12 24,4",
                width: strokeWidth,
                transform: `rotate(${point}, 24, 24)`
            }, point)), [
        strokeWidth
    ]);
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])($5819da83a926266a$var$Svg, {
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 48 48",
        width: width,
        stroke: strokeColor,
        speed: animationDuration,
        "data-testid": "rotating-lines-svg",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: lines()
    });
};
const $56d89154a59e79d3$export$f8e5ae7506d65b32 = ({ height: height = 80, width: width = 80, strokeWidth: strokeWidth = 2, radius: radius = 1, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "tail-spin-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>{
    const strokeWidthNum = parseInt(String(strokeWidth));
    const viewBoxValue = strokeWidthNum + 36;
    const halfStrokeWidth = strokeWidthNum / 2;
    const processedRadius = halfStrokeWidth + parseInt(String(radius)) - 1;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "tail-spin-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            viewBox: `0 0 ${viewBoxValue} ${viewBoxValue}`,
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            "data-testid": "tail-spin-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("defs", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("linearGradient", {
                        x1: "8.042%",
                        y1: "0%",
                        x2: "65.682%",
                        y2: "23.865%",
                        id: "a",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                                stopColor: color,
                                stopOpacity: "0",
                                offset: "0%"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                                stopColor: color,
                                stopOpacity: ".631",
                                offset: "63.146%"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                                stopColor: color,
                                offset: "100%"
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                    fill: "none",
                    fillRule: "evenodd",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        transform: `translate(${halfStrokeWidth} ${halfStrokeWidth})`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M36 18c0-9.94-8.06-18-18-18",
                                id: "Oval-2",
                                stroke: color,
                                strokeWidth: strokeWidth,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                    attributeName: "transform",
                                    type: "rotate",
                                    from: "0 18 18",
                                    to: "360 18 18",
                                    dur: "0.9s",
                                    repeatCount: "indefinite"
                                })
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                                fill: "#fff",
                                cx: "36",
                                cy: "18",
                                r: processedRadius,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                    attributeName: "transform",
                                    type: "rotate",
                                    from: "0 18 18",
                                    to: "360 18 18",
                                    dur: "0.9s",
                                    repeatCount: "indefinite"
                                })
                            })
                        ]
                    })
                })
            ]
        })
    });
};
const $5cff71254109409f$export$e21573137ccb7f5d = ({ wrapperStyle: wrapperStyle = {}, visible: visible = true, wrapperClass: wrapperClass = "", height: height = 100, width: width = 100, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "three-circles-loading", outerCircleColor: outerCircleColor, innerCircleColor: innerCircleColor, middleCircleColor: middleCircleColor })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "three-circles-wrapper",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            version: "1.1",
            height: `${height}`,
            width: `${width}`,
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            viewBox: "0 0 100 100",
            enableBackground: "new 0 0 100 100",
            xmlSpace: "preserve",
            "data-testid": "three-circles-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    fill: outerCircleColor || color,
                    d: "M31.6,3.5C5.9,13.6-6.6,42.7,3.5,68.4c10.1,25.7,39.2,38.3,64.9,28.1l-3.1-7.9c-21.3,8.4-45.4-2-53.8-23.3 c-8.4-21.3,2-45.4,23.3-53.8L31.6,3.5z",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        attributeType: "XML",
                        type: "rotate",
                        dur: "2s",
                        from: "0 50 50",
                        to: "360 50 50",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    fill: middleCircleColor || color,
                    d: "M42.3,39.6c5.7-4.3,13.9-3.1,18.1,2.7c4.3,5.7,3.1,13.9-2.7,18.1l4.1,5.5c8.8-6.5,10.6-19,4.1-27.7 c-6.5-8.8-19-10.6-27.7-4.1L42.3,39.6z",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        attributeType: "XML",
                        type: "rotate",
                        dur: "1s",
                        from: "0 50 50",
                        to: "-360 50 50",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                    fill: innerCircleColor || color,
                    d: "M82,35.7C74.1,18,53.4,10.1,35.7,18S10.1,46.6,18,64.3l7.6-3.4c-6-13.5,0-29.3,13.5-35.3s29.3,0,35.3,13.5 L82,35.7z",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        attributeType: "XML",
                        type: "rotate",
                        dur: "2s",
                        from: "0 50 50",
                        to: "360 50 50",
                        repeatCount: "indefinite"
                    })
                })
            ]
        })
    });
};
const $f0c3e3bb3e76d210$export$4bf83b24a11cff0b = ({ height: height = 80, width: width = 80, radius: radius = 9, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "three-dots-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "three-dots-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            viewBox: "0 0 120 30",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            fill: color,
            "data-testid": "three-dots-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                    cx: "15",
                    cy: "15",
                    r: Number(radius) + 6,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "r",
                            from: "15",
                            to: "15",
                            begin: "0s",
                            dur: "0.8s",
                            values: "15;9;15",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill-opacity",
                            from: "1",
                            to: "1",
                            begin: "0s",
                            dur: "0.8s",
                            values: "1;.5;1",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                    cx: "60",
                    cy: "15",
                    r: radius,
                    attributeName: "fill-opacity",
                    from: "1",
                    to: "0.3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "r",
                            from: "9",
                            to: "9",
                            begin: "0s",
                            dur: "0.8s",
                            values: "9;15;9",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill-opacity",
                            from: "0.5",
                            to: "0.5",
                            begin: "0s",
                            dur: "0.8s",
                            values: ".5;1;.5",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                    cx: "105",
                    cy: "15",
                    r: Number(radius) + 6,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "r",
                            from: "15",
                            to: "15",
                            begin: "0s",
                            dur: "0.8s",
                            values: "15;9;15",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill-opacity",
                            from: "1",
                            to: "1",
                            begin: "0s",
                            dur: "0.8s",
                            values: "1;.5;1",
                            calcMode: "linear",
                            repeatCount: "indefinite"
                        })
                    ]
                })
            ]
        })
    });
const $afa12dd3e98f740f$var$VIEW_BOX_VALUES = "-3 -4 39 39";
const $afa12dd3e98f740f$var$POLYGON_POINTS = "16,0 32,32 0,32";
/** Styles */ const $afa12dd3e98f740f$var$dash = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"])`
to {
   stroke-dashoffset: 136;
 }
`;
const $afa12dd3e98f740f$var$Polygon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).polygon`
  stroke-dasharray: 17;
  animation: ${$afa12dd3e98f740f$var$dash} 2.5s cubic-bezier(0.35, 0.04, 0.63, 0.95) infinite;
`;
const $afa12dd3e98f740f$var$SVG = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$components$2f$dist$2f$styled$2d$components$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).svg`
  transform-origin: 50% 65%;
`;
const $afa12dd3e98f740f$export$5a465592bfe74b48 = ({ height: height = 80, width: width = 80, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "triangle-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: `${wrapperClass}`,
        "data-testid": "triangle-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])($afa12dd3e98f740f$var$SVG, {
            id: "triangle",
            width: width,
            height: height,
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            viewBox: $afa12dd3e98f740f$var$VIEW_BOX_VALUES,
            "data-testid": "triangle-svg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])($afa12dd3e98f740f$var$Polygon, {
                fill: "transparent",
                stroke: color,
                strokeWidth: "1",
                points: $afa12dd3e98f740f$var$POLYGON_POINTS
            })
        })
    });
};
const $e3e50827b57d879a$export$4c68f1a79f88778c = ({ height: height = 80, width: width = 80, radius: radius = 48, color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ariaLabel: ariaLabel = "watch-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, visible: visible = true })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])((0, $4c3f0b77e8caf06d$export$21d9f1931ef75b56), {
        style: wrapperStyle,
        $visible: visible,
        className: wrapperClass,
        "data-testid": "watch-loading",
        "aria-label": ariaLabel,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
            width: width,
            height: height,
            version: "1.1",
            id: "L2",
            xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
            x: "0px",
            y: "0px",
            viewBox: "0 0 100 100",
            enableBackground: "new 0 0 100 100",
            xmlSpace: "preserve",
            "data-testid": "watch-svg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                    fill: "none",
                    stroke: color,
                    strokeWidth: "4",
                    strokeMiterlimit: "10",
                    cx: "50",
                    cy: "50",
                    r: radius
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("line", {
                    fill: "none",
                    strokeLinecap: "round",
                    stroke: color,
                    strokeWidth: "4",
                    strokeMiterlimit: "10",
                    x1: "50",
                    y1: "50",
                    x2: "85",
                    y2: "50.5",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        dur: "2s",
                        type: "rotate",
                        from: "0 50 50",
                        to: "360 50 50",
                        repeatCount: "indefinite"
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("line", {
                    fill: "none",
                    strokeLinecap: "round",
                    stroke: color,
                    strokeWidth: "4",
                    strokeMiterlimit: "10",
                    x1: "50",
                    y1: "50",
                    x2: "49.5",
                    y2: "74",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        dur: "15s",
                        type: "rotate",
                        from: "0 50 50",
                        to: "360 50 50",
                        repeatCount: "indefinite"
                    })
                })
            ]
        })
    });
const $b184d2a88a50e3dc$export$1ed1943372cc63a9 = ({ color: color = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), width: width = "100", visible: visible = true })=>{
    return visible ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        width: width,
        height: width,
        viewBox: "0 0 100 100",
        "data-testid": "falling-lines",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                y: "25",
                width: "10",
                height: "50",
                rx: "4",
                ry: "4",
                fill: color,
                "data-testid": "falling-lines-rect-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "x",
                        values: "10;100",
                        dur: "1.2s",
                        repeatCount: "indefinite"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        type: "rotate",
                        from: "0 10 70",
                        to: "-60 100 70",
                        dur: "1.2s",
                        repeatCount: "indefinite"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "opacity",
                        values: "0;1;0",
                        dur: "1.2s",
                        repeatCount: "indefinite"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                y: "25",
                width: "10",
                height: "50",
                rx: "4",
                ry: "4",
                fill: color,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "x",
                        values: "10;100",
                        dur: "1.2s",
                        begin: "0.4s",
                        repeatCount: "indefinite"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        type: "rotate",
                        from: "0 10 70",
                        to: "-60 100 70",
                        dur: "1.2s",
                        begin: "0.4s",
                        repeatCount: "indefinite"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "opacity",
                        values: "0;1;0",
                        dur: "1.2s",
                        begin: "0.4s",
                        repeatCount: "indefinite"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                y: "25",
                width: "10",
                height: "50",
                rx: "4",
                ry: "4",
                fill: color,
                "data-testid": "falling-lines-rect-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "x",
                        values: "10;100",
                        dur: "1.2s",
                        begin: "0.8s",
                        repeatCount: "indefinite"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                        attributeName: "transform",
                        type: "rotate",
                        from: "0 10 70",
                        to: "-60 100 70",
                        dur: "1.2s",
                        begin: "0.8s",
                        repeatCount: "indefinite"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "opacity",
                        values: "0;1;0",
                        dur: "1.2s",
                        begin: "0.8s",
                        repeatCount: "indefinite"
                    })
                ]
            })
        ]
    }) : null;
};
const $5ad4f4dbdb85103b$export$d25f4198d7ad6c78 = ({ visible: visible = true, height: height = "80", width: width = "80", ariaLabel: ariaLabel = "vortex-loading", wrapperStyle: wrapperStyle, wrapperClass: wrapperClass, colors: colors = [
    "#1B5299",
    "#EF8354",
    "#DB5461",
    "#1B5299",
    "#EF8354",
    "#DB5461"
] })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        height: height,
        width: width,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        "data-testid": "vortex-svg",
        "aria-label": ariaLabel,
        style: wrapperStyle,
        className: wrapperClass,
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
            transform: "translate(50,50)",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                transform: "scale(0.7)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                    transform: "translate(-50,-50)",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        transform: "rotate(137.831 50 50)",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                type: "rotate",
                                repeatCount: "indefinite",
                                values: "360 50 50;0 50 50",
                                keyTimes: "0;1",
                                dur: "1",
                                keySplines: "0.5 0.5 0.5 0.5",
                                calcMode: "spline"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[0],
                                d: "M30.4,9.7c-7.4,10.9-11.8,23.8-12.3,37.9c0.2,1,0.5,1.9,0.7,2.8c1.4-5.2,3.4-10.3,6.2-15.1 c2.6-4.4,5.6-8.4,9-12c0.7-0.7,1.4-1.4,2.1-2.1c7.4-7,16.4-12,26-14.6C51.5,3.6,40.2,4.9,30.4,9.7z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[1],
                                d: "M24.8,64.2c-2.6-4.4-4.5-9.1-5.9-13.8c-0.3-0.9-0.5-1.9-0.7-2.8c-2.4-9.9-2.2-20.2,0.4-29.8 C10.6,25.5,6,36,5.3,46.8C11,58.6,20,68.9,31.9,76.3c0.9,0.3,1.9,0.5,2.8,0.8C31,73.3,27.6,69,24.8,64.2z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[2],
                                d: "M49.6,78.9c-5.1,0-10.1-0.6-14.9-1.8c-1-0.2-1.9-0.5-2.8-0.8c-9.8-2.9-18.5-8.2-25.6-15.2 c2.8,10.8,9.5,20,18.5,26c13.1,0.9,26.6-1.7,38.9-8.3c0.7-0.7,1.4-1.4,2.1-2.1C60.7,78.2,55.3,78.9,49.6,78.9z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[3],
                                d: "M81.1,49.6c-1.4,5.2-3.4,10.3-6.2,15.1c-2.6,4.4-5.6,8.4-9,12c-0.7,0.7-1.4,1.4-2.1,2.1 c-7.4,7-16.4,12-26,14.6c10.7,3,22.1,1.7,31.8-3.1c7.4-10.9,11.8-23.8,12.3-37.9C81.6,51.5,81.4,50.6,81.1,49.6z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[4],
                                d: "M75.2,12.9c-13.1-0.9-26.6,1.7-38.9,8.3c-0.7,0.7-1.4,1.4-2.1,2.1c5.2-1.4,10.6-2.2,16.2-2.2 c5.1,0,10.1,0.6,14.9,1.8c1,0.2,1.9,0.5,2.8,0.8c9.8,2.9,18.5,8.2,25.6,15.2C90.9,28.1,84.2,18.9,75.2,12.9z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[5],
                                d: "M94.7,53.2C89,41.4,80,31.1,68.1,23.7c-0.9-0.3-1.9-0.5-2.8-0.8c3.8,3.8,7.2,8.1,10,13 c2.6,4.4,4.5,9.1,5.9,13.8c0.3,0.9,0.5,1.9,0.7,2.8c2.4,9.9,2.2,20.2-0.4,29.8C89.4,74.5,94,64,94.7,53.2z"
                            })
                        ]
                    })
                })
            })
        })
    });
};
const $aa2b177fb9ef5dee$export$f64f16a115ce395d = ({ visible: visible = true, height: height = "80", width: width = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "rotating-triangle-loading", colors: colors = [
    "#1B5299",
    "#EF8354",
    "#DB5461"
] })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "rotating-triangle-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
            transform: "translate(50,42)",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                transform: "scale(0.8)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                    transform: "translate(-50,-50)",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("polygon", {
                            points: "72.5,50 50,11 27.5,50 50,50",
                            fill: colors[0],
                            transform: "rotate(186 50 38.5)",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                type: "rotate",
                                calcMode: "linear",
                                values: "0 50 38.5;360 50 38.5",
                                keyTimes: "0;1",
                                dur: "1s",
                                begin: "0s",
                                repeatCount: "indefinite"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("polygon", {
                            points: "5,89 50,89 27.5,50",
                            fill: colors[1],
                            transform: "rotate(186 27.5 77.5)",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                type: "rotate",
                                calcMode: "linear",
                                values: "0 27.5 77.5;360 27.5 77.5",
                                keyTimes: "0;1",
                                dur: "1s",
                                begin: "0s",
                                repeatCount: "indefinite"
                            })
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("polygon", {
                            points: "72.5,50 50,89 95,89",
                            fill: colors[2],
                            transform: "rotate(186 72.2417 77.5)",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                type: "rotate",
                                calcMode: "linear",
                                values: "0 72.5 77.5;360 72 77.5",
                                keyTimes: "0;1",
                                dur: "1s",
                                begin: "0s",
                                repeatCount: "indefinite"
                            })
                        })
                    ]
                })
            })
        })
    });
};
const $daf95de783b7b8b1$export$d7b12c4107be0d61 = ({ visible: visible = true, height: height = "80", width: width = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "radio-loading", colors: colors = [
    (0, $84fda1e7e33cfd28$export$37394b0fa44b998c),
    (0, $84fda1e7e33cfd28$export$37394b0fa44b998c),
    (0, $84fda1e7e33cfd28$export$37394b0fa44b998c)
] })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "radio-bar-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                cx: "28",
                cy: "75",
                r: "11",
                fill: colors[0],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill-opacity",
                    calcMode: "linear",
                    values: "0;1;1",
                    keyTimes: "0;0.2;1",
                    dur: "1",
                    begin: "0s",
                    repeatCount: "indefinite"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M28 47A28 28 0 0 1 56 75",
                fill: "none",
                strokeWidth: "10",
                stroke: colors[1],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "stroke-opacity",
                    calcMode: "linear",
                    values: "0;1;1",
                    keyTimes: "0;0.2;1",
                    dur: "1",
                    begin: "0.1s",
                    repeatCount: "indefinite"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M28 25A50 50 0 0 1 78 75",
                fill: "none",
                strokeWidth: "10",
                stroke: colors[2],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "stroke-opacity",
                    calcMode: "linear",
                    values: "0;1;1",
                    keyTimes: "0;0.2;1",
                    dur: "1",
                    begin: "0.2s",
                    repeatCount: "indefinite"
                })
            })
        ]
    });
};
const $075a2f0ea0d9df8a$export$c17561cb55d4db30 = ({ visible: visible = true, height: height = "80", width: width = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "progress-bar-loading", borderColor: borderColor = "#F4442E", barColor: barColor = "#51E5FF" })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "progress-bar-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("clipPath", {
                    x: "0",
                    y: "0",
                    width: "100",
                    height: "100",
                    id: "lds-progress-cpid-5009611b8a418",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("rect", {
                        x: "0",
                        y: "0",
                        width: "66.6667",
                        height: "100",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "width",
                                calcMode: "linear",
                                values: "0;100;100",
                                keyTimes: "0;0.5;1",
                                dur: "1",
                                begin: "0s",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "x",
                                calcMode: "linear",
                                values: "0;0;100",
                                keyTimes: "0;0.5;1",
                                dur: "1",
                                begin: "0s",
                                repeatCount: "indefinite"
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                fill: "none",
                strokeWidth: "2.7928",
                d: "M82,63H18c-7.2,0-13-5.8-13-13v0c0-7.2,5.8-13,13-13h64c7.2,0,13,5.8,13,13v0C95,57.2,89.2,63,82,63z",
                stroke: borderColor
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M81.3,58.7H18.7c-4.8,0-8.7-3.9-8.7-8.7v0c0-4.8,3.9-8.7,8.7-8.7h62.7c4.8,0,8.7,3.9,8.7,8.7v0C90,54.8,86.1,58.7,81.3,58.7z",
                fill: barColor,
                clipPath: "url(#lds-progress-cpid-5009611b8a418)"
            })
        ]
    });
};
const $db94311ffb982ec6$export$bdf537af43a20db5 = ({ visible: visible = true, height: height = "80", width: width = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "magnifying-glass-loading", glassColor: glassColor = "#c0efff", color: color = "#e15b64" })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "magnifying-glass-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
            transform: "translate(50,50)",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                transform: "scale(0.82)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                    transform: "translate(-50,-50)",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        transform: "translate(16.3636 -20)",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                                attributeName: "transform",
                                type: "translate",
                                calcMode: "linear",
                                values: "-20 -20;20 -20;0 20;-20 -20",
                                keyTimes: "0;0.33;0.66;1",
                                dur: "1s",
                                begin: "0s",
                                repeatCount: "indefinite"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M44.19,26.158c-4.817,0-9.345,1.876-12.751,5.282c-3.406,3.406-5.282,7.934-5.282,12.751 c0,4.817,1.876,9.345,5.282,12.751c3.406,3.406,7.934,5.282,12.751,5.282s9.345-1.876,12.751-5.282 c3.406-3.406,5.282-7.934,5.282-12.751c0-4.817-1.876-9.345-5.282-12.751C53.536,28.033,49.007,26.158,44.19,26.158z",
                                fill: glassColor
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M78.712,72.492L67.593,61.373l-3.475-3.475c1.621-2.352,2.779-4.926,3.475-7.596c1.044-4.008,1.044-8.23,0-12.238 c-1.048-4.022-3.146-7.827-6.297-10.979C56.572,22.362,50.381,20,44.19,20C38,20,31.809,22.362,27.085,27.085 c-9.447,9.447-9.447,24.763,0,34.21C31.809,66.019,38,68.381,44.19,68.381c4.798,0,9.593-1.425,13.708-4.262l9.695,9.695 l4.899,4.899C73.351,79.571,74.476,80,75.602,80s2.251-0.429,3.11-1.288C80.429,76.994,80.429,74.209,78.712,72.492z M56.942,56.942 c-3.406,3.406-7.934,5.282-12.751,5.282s-9.345-1.876-12.751-5.282c-3.406-3.406-5.282-7.934-5.282-12.751 c0-4.817,1.876-9.345,5.282-12.751c3.406-3.406,7.934-5.282,12.751-5.282c4.817,0,9.345,1.876,12.751,5.282 c3.406,3.406,5.282,7.934,5.282,12.751C62.223,49.007,60.347,53.536,56.942,56.942z",
                                fill: color
                            })
                        ]
                    })
                })
            })
        })
    });
};
const $1d8c9163e13b7bf7$export$8e3fad5cade57efa = ({ width: width = "80", height: height = "80", backgroundColor: backgroundColor = (0, $84fda1e7e33cfd28$export$37394b0fa44b998c), ballColors: ballColors = [
    "#fc636b",
    "#6a67ce",
    "#ffb900"
], wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "fidget-spinner-loader", visible: visible = true })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "fidget-spinner-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
            transform: "rotate(6 50 50)",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                    transform: "translate(50 50)",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("g", {
                        transform: "scale(0.9)",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                            transform: "translate(-50 -58)",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                    d: "M27.1,79.4c-1.1,0.6-2.4,1-3.7,1c-2.6,0-5.1-1.4-6.4-3.7c-2-3.5-0.8-8,2.7-10.1c1.1-0.6,2.4-1,3.7-1c2.6,0,5.1,1.4,6.4,3.7 C31.8,72.9,30.6,77.4,27.1,79.4z",
                                    fill: ballColors[0]
                                }),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                    d: "M72.9,79.4c1.1,0.6,2.4,1,3.7,1c2.6,0,5.1-1.4,6.4-3.7c2-3.5,0.8-8-2.7-10.1c-1.1-0.6-2.4-1-3.7-1c-2.6,0-5.1,1.4-6.4,3.7 C68.2,72.9,69.4,77.4,72.9,79.4z",
                                    fill: ballColors[1]
                                }),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                                    cx: "50",
                                    cy: "27",
                                    r: "7.4",
                                    fill: ballColors[2]
                                }),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                    d: "M86.5,57.5c-3.1-1.9-6.4-2.8-9.8-2.8c-0.5,0-0.9,0-1.4,0c-0.4,0-0.8,0-1.1,0c-2.1,0-4.2-0.4-6.2-1.2 c-0.8-3.6-2.8-6.9-5.4-9.3c0.4-2.5,1.3-4.8,2.7-6.9c2-2.9,3.2-6.5,3.2-10.4c0-10.2-8.2-18.4-18.4-18.4c-0.3,0-0.6,0-0.9,0 C39.7,9,32,16.8,31.6,26.2c-0.2,4.1,1,7.9,3.2,11c1.4,2.1,2.3,4.5,2.7,6.9c-2.6,2.5-4.6,5.7-5.4,9.3c-1.9,0.7-4,1.1-6.1,1.1 c-0.4,0-0.8,0-1.2,0c-0.5,0-0.9-0.1-1.4-0.1c-3.1,0-6.3,0.8-9.2,2.5c-9.1,5.2-12,17-6.3,25.9c3.5,5.4,9.5,8.4,15.6,8.4 c2.9,0,5.8-0.7,8.5-2.1c3.6-1.9,6.3-4.9,8-8.3c1.1-2.3,2.7-4.2,4.6-5.8c1.7,0.5,3.5,0.8,5.4,0.8c1.9,0,3.7-0.3,5.4-0.8 c1.9,1.6,3.5,3.5,4.6,5.7c1.5,3.2,4,6,7.4,8c2.9,1.7,6.1,2.5,9.2,2.5c6.6,0,13.1-3.6,16.4-10C97.3,73.1,94.4,62.5,86.5,57.5z M29.6,83.7c-1.9,1.1-4,1.6-6.1,1.6c-4.2,0-8.4-2.2-10.6-6.1c-3.4-5.9-1.4-13.4,4.5-16.8c1.9-1.1,4-1.6,6.1-1.6 c4.2,0,8.4,2.2,10.6,6.1C37.5,72.8,35.4,80.3,29.6,83.7z M50,39.3c-6.8,0-12.3-5.5-12.3-12.3S43.2,14.7,50,14.7 c6.8,0,12.3,5.5,12.3,12.3S56.8,39.3,50,39.3z M87.2,79.2c-2.3,3.9-6.4,6.1-10.6,6.1c-2.1,0-4.2-0.5-6.1-1.6 c-5.9-3.4-7.9-10.9-4.5-16.8c2.3-3.9,6.4-6.1,10.6-6.1c2.1,0,4.2,0.5,6.1,1.6C88.6,65.8,90.6,73.3,87.2,79.2z",
                                    fill: backgroundColor
                                })
                            ]
                        })
                    })
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                    attributeName: "transform",
                    type: "rotate",
                    calcMode: "linear",
                    values: "0 50 50;360 50 50",
                    keyTimes: "0;1",
                    dur: "1s",
                    begin: "0s",
                    repeatCount: "indefinite"
                })
            ]
        })
    });
};
const $bb8e4335d7ee0654$export$bee07fdc425df572 = ({ visible: visible = true, width: width = "80", height: height = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "dna-loading" })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        width: width,
        height: height,
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "dna-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "6.451612903225806",
                cy: "60.6229",
                r: "3.41988",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.5s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "0s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.5s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "6.451612903225806",
                cy: "39.3771",
                r: "2.58012",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.5s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.5s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "16.129032258064512",
                cy: "68.1552",
                r: "3.17988",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.7s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.2s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.7s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "16.129032258064512",
                cy: "31.8448",
                r: "2.82012",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.7s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.2s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.7s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "25.806451612903224",
                cy: "69.3634",
                r: "2.93988",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.9s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.4s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.9s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "25.806451612903224",
                cy: "30.6366",
                r: "3.06012",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.9s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.4s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.9s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "35.48387096774193",
                cy: "65.3666",
                r: "2.69988",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.1s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.6s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.1s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "35.48387096774193",
                cy: "34.6334",
                r: "3.30012",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.1s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.6s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.1s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "45.16129032258064",
                cy: "53.8474",
                r: "2.45988",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.3s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-0.8s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.3s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "45.16129032258064",
                cy: "46.1526",
                r: "3.54012",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.3s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.8s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.3s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "54.838709677419345",
                cy: "39.3771",
                r: "2.58012",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.5s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.5s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "54.838709677419345",
                cy: "60.6229",
                r: "3.41988",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.5s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.5s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "64.51612903225805",
                cy: "31.8448",
                r: "2.82012",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.7s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.2s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.7s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "64.51612903225805",
                cy: "68.1552",
                r: "3.17988",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.7s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.2s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.7s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "74.19354838709677",
                cy: "30.6366",
                r: "3.06012",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.9s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.4s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.9s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "74.19354838709677",
                cy: "69.3634",
                r: "2.93988",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.9s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.4s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.9s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "83.87096774193547",
                cy: "34.6334",
                r: "3.30012",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.1s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.6s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.1s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "83.87096774193547",
                cy: "65.3666",
                r: "2.69988",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-3.1s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.6s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.1s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "93.54838709677418",
                cy: "46.1526",
                r: "3.54012",
                fill: "rgba(233, 12, 89, 0.5125806451612902)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.3s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-1.8s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "rgba(233, 12, 89, 0.5125806451612902);#ff0033;rgba(233, 12, 89, 0.5125806451612902)",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.3s"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("circle", {
                cx: "93.54838709677418",
                cy: "53.8474",
                r: "2.45988",
                fill: "#46dff0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "r",
                        keyTimes: "0;0.5;1",
                        values: "2.4000000000000004;3.5999999999999996;2.4000000000000004",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-3.3s"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "cy",
                        keyTimes: "0;0.5;1",
                        values: "30.5;69.5;30.5",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.8s",
                        keySplines: "0.5 0 0.5 1;0.5 0 0.5 1",
                        calcMode: "spline"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                        attributeName: "fill",
                        keyTimes: "0;0.5;1",
                        values: "#46dff0;rgba(53, 58, 57, 0.1435483870967742);#46dff0",
                        dur: "2s",
                        repeatCount: "indefinite",
                        begin: "-2.3s"
                    })
                ]
            })
        ]
    });
};
const $50138037f422b463$export$f93420b62a5bdffa = ({ visible: visible = true, width: width = "80", height: height = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "discuss-loading", colors: colors = [
    "#ff727d",
    "#ff727d"
] })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "discuss-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                fill: "none",
                d: "M82 50A32 32 0 1 1 23.533421623214014 32.01333190873183 L21.71572875253809 21.7157287525381 L32.013331908731814 23.53342162321403 A32 32 0 0 1 82 50",
                strokeWidth: "5",
                stroke: colors[0]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                cx: "50",
                cy: "50",
                fill: "none",
                strokeLinecap: "round",
                r: "20",
                strokeWidth: "5",
                stroke: colors[1],
                strokeDasharray: "31.41592653589793 31.41592653589793",
                transform: "rotate(96 50 50)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                    attributeName: "transform",
                    type: "rotate",
                    calcMode: "linear",
                    values: "0 50 50;360 50 50",
                    keyTimes: "0;1",
                    dur: "1s",
                    begin: "0s",
                    repeatCount: "indefinite"
                })
            })
        ]
    });
};
const $7097090906378a5b$export$dc036a5afb9ca26f = ({ visible: visible = true, width: width = "80", height: height = "80", colors: colors = [
    "#e15b64",
    "#f47e60",
    "#f8b26a",
    "#abbd81",
    "#849b87"
], wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "color-ring-loading" })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        xmlnsXlink: "http://www.w3.org/1999/xlink",
        width: width,
        height: height,
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "color-ring-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("mask", {
                    id: "ldio-4offds5dlws-mask",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                        cx: "50",
                        cy: "50",
                        r: "26",
                        stroke: "#fff",
                        strokeLinecap: "round",
                        strokeDasharray: "122.52211349000194 40.840704496667314",
                        strokeWidth: "9",
                        transform: "rotate(198.018 50 50)",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                            attributeName: "transform",
                            type: "rotate",
                            values: "0 50 50;360 50 50",
                            keyTimes: "0;1",
                            dur: "1s",
                            repeatCount: "indefinite"
                        })
                    })
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                mask: "url(#ldio-4offds5dlws-mask)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                        x: "14.5",
                        y: "0",
                        width: "15",
                        height: "100",
                        fill: colors[0],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill",
                            values: colors.join(";").toString(),
                            keyTimes: "0;0.25;0.5;0.75;1",
                            dur: "1s",
                            repeatCount: "indefinite",
                            begin: "-0.8s"
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                        x: "28.5",
                        y: "0",
                        width: "15",
                        height: "100",
                        fill: colors[1],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill",
                            values: colors.join(";").toString(),
                            keyTimes: "0;0.25;0.5;0.75;1",
                            dur: "1s",
                            repeatCount: "indefinite",
                            begin: "-0.6s"
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                        x: "42.5",
                        y: "0",
                        width: "15",
                        height: "100",
                        fill: colors[2],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill",
                            values: colors.join(";").toString(),
                            keyTimes: "0;0.25;0.5;0.75;1",
                            dur: "1s",
                            repeatCount: "indefinite",
                            begin: "-0.4s"
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                        x: "56.5",
                        y: "0",
                        width: "15",
                        height: "100",
                        fill: colors[3],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill",
                            values: colors.join(";").toString(),
                            keyTimes: "0;0.25;0.5;0.75;1",
                            dur: "1s",
                            repeatCount: "indefinite",
                            begin: "-0.2s"
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                        x: "70.5",
                        y: "0",
                        width: "15",
                        height: "100",
                        fill: colors[4],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                            attributeName: "fill",
                            values: colors.join(";").toString(),
                            keyTimes: "0;0.25;0.5;0.75;1",
                            dur: "1s",
                            repeatCount: "indefinite",
                            begin: "0s"
                        })
                    })
                ]
            })
        ]
    });
};
const $81e36fafa9b58989$export$4d299b491347818a = ({ visible: visible = true, width: width = "80", height: height = "80", backgroundColor: backgroundColor = "#ff6d00", color: color = "#fff", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "comment-loading" })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        width: width,
        height: height,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "comment-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M78,19H22c-6.6,0-12,5.4-12,12v31c0,6.6,5.4,12,12,12h37.2c0.4,3,1.8,5.6,3.7,7.6c2.4,2.5,5.1,4.1,9.1,4 c-1.4-2.1-2-7.2-2-10.3c0-0.4,0-0.8,0-1.3h8c6.6,0,12-5.4,12-12V31C90,24.4,84.6,19,78,19z",
                fill: backgroundColor
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                cx: "30",
                cy: "47",
                r: "5",
                fill: color,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "opacity",
                    calcMode: "linear",
                    values: "0;1;1",
                    keyTimes: "0;0.2;1",
                    dur: "1",
                    begin: "0s",
                    repeatCount: "indefinite"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                cx: "50",
                cy: "47",
                r: "5",
                fill: color,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "opacity",
                    calcMode: "linear",
                    values: "0;0;1;1",
                    keyTimes: "0;0.2;0.4;1",
                    dur: "1",
                    begin: "0s",
                    repeatCount: "indefinite"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("circle", {
                cx: "70",
                cy: "47",
                r: "5",
                fill: color,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "opacity",
                    calcMode: "linear",
                    values: "0;0;1;1",
                    keyTimes: "0;0.4;0.6;1",
                    dur: "1",
                    begin: "0s",
                    repeatCount: "indefinite"
                })
            })
        ]
    });
};
const $ffa7e3ac27a21a71$export$2ba1b65b747a57aa = ({ visible: visible = true, width: width = "80", height: height = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "blocks-loading" })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        width: width,
        height: height,
        className: wrapperClass,
        style: wrapperStyle,
        xmlns: (0, $eb040f10400edc38$export$98a285aab16ab26c),
        viewBox: "0 0 100 100",
        preserveAspectRatio: "xMidYMid",
        "aria-label": ariaLabel,
        "data-testid": "blocks-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("title", {
                children: "Blocks"
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("desc", {
                children: "Animated representation of blocks"
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "17",
                y: "17",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "40",
                y: "17",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.125s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "63",
                y: "17",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.25s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "17",
                y: "40",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.875s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "63",
                y: "40",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.375s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "17",
                y: "63",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.75s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "40",
                y: "63",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.625s",
                    calcMode: "discrete"
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("rect", {
                x: "63",
                y: "63",
                width: "20",
                height: "20",
                fill: "#577c9b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                    attributeName: "fill",
                    values: "#0dceff;#577c9b;#577c9b",
                    keyTimes: "0;0.125;1",
                    dur: "1s",
                    repeatCount: "indefinite",
                    begin: "0.5s",
                    calcMode: "discrete"
                })
            })
        ]
    });
};
const $1e82ee682f5b64b8$export$f3c41beb83007357 = ({ visible: visible = true, width: width = "80", height: height = "80", wrapperClass: wrapperClass = "", wrapperStyle: wrapperStyle = {}, ariaLabel: ariaLabel = "hourglass-loading", colors: colors = [
    "#306cce",
    "#72a1ed"
] })=>{
    return !visible ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        width: width,
        height: height,
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 350 350",
        preserveAspectRatio: "xMidYMid",
        className: wrapperClass,
        style: wrapperStyle,
        "aria-label": ariaLabel,
        "data-testid": "hourglass-svg",
        ...(0, $84fda1e7e33cfd28$export$6bfda33bcd6c2d18),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animateTransform", {
                attributeName: "transform",
                type: "rotate",
                values: "0; 0; -30; 360; 360",
                keyTimes: "0; 0.40; 0.55; 0.65; 1",
                dur: "3s",
                begin: "0s",
                calcMode: "linear",
                repeatCount: "indefinite"
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                        fill: colors[0],
                        stroke: colors[0],
                        d: "M324.658,20.572v-2.938C324.658,7.935,316.724,0,307.025,0H40.313c-9.699,0-17.635,7.935-17.635,17.634v2.938     c0,9.699,7.935,17.634,17.635,17.634h6.814c3.5,0,3.223,3.267,3.223,4.937c0,19.588,8.031,42.231,14.186,56.698     c12.344,29.012,40.447,52.813,63.516,69.619c4.211,3.068,3.201,5.916,0.756,7.875c-22.375,17.924-51.793,40.832-64.271,70.16     c-6.059,14.239-13.934,36.4-14.18,55.772c-0.025,1.987,0.771,5.862-3.979,5.862h-6.064c-9.699,0-17.635,7.936-17.635,17.634v2.94     c0,9.698,7.935,17.634,17.635,17.634h266.713c9.699,0,17.633-7.936,17.633-17.634v-2.94c0-9.698-7.934-17.634-17.633-17.634     h-3.816c-7,0-6.326-5.241-6.254-7.958c0.488-18.094-4.832-38.673-12.617-54.135c-17.318-34.389-44.629-56.261-61.449-68.915     c-3.65-2.745-4.018-6.143,0-8.906c17.342-11.929,44.131-34.526,61.449-68.916c8.289-16.464,13.785-38.732,12.447-57.621     c-0.105-1.514-0.211-4.472,3.758-4.472h6.482C316.725,38.206,324.658,30.272,324.658,20.572z M270.271,93.216     c-16.113,31.998-41.967,54.881-64.455,68.67c-1.354,0.831-3.936,2.881-3.936,8.602v6.838c0,6.066,2.752,7.397,4.199,8.286     c22.486,13.806,48.143,36.636,64.191,68.508c7.414,14.727,11.266,32.532,10.885,46.702c-0.078,2.947,1.053,8.308-6.613,8.308     H72.627c-6.75,0-6.475-3.37-6.459-5.213c0.117-12.895,4.563-30.757,12.859-50.255c14.404-33.854,44.629-54.988,64.75-67.577     c0.896-0.561,2.629-1.567,2.629-6.922v-10.236c0-5.534-2.656-7.688-4.057-8.57c-20.098-12.688-49.256-33.618-63.322-66.681     c-8.383-19.702-12.834-37.732-12.861-50.657c-0.002-1.694,0.211-4.812,3.961-4.812h206.582c4.168,0,4.127,3.15,4.264,4.829     C282.156,57.681,278.307,77.257,270.271,93.216z"
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[1],
                                stroke: colors[1],
                                d: "M169.541,196.2l-68.748,86.03c-2.27,2.842-1.152,5.166,2.484,5.166h140.781c3.637,0,4.756-2.324,2.484-5.166     l-68.746-86.03C175.525,193.358,171.811,193.358,169.541,196.2z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "opacity",
                                values: "0; 0; 1; 1; 0; 0",
                                keyTimes: "0; 0.1; 0.4; 0.6; 0.61; 1",
                                dur: "3s",
                                repeatCount: "indefinite"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                fill: colors[1],
                                stroke: colors[1],
                                d: "M168.986,156.219c2.576,2.568,6.789,2.568,9.363,0l34.576-34.489c2.574-2.568,1.707-4.67-1.932-4.67H136.34     c-3.637,0-4.506,2.102-1.932,4.67L168.986,156.219z"
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])("animate", {
                                attributeName: "opacity",
                                values: "1; 1; 0; 0; 1; 1",
                                keyTimes: "0; 0.1; 0.4; 0.65; 0.66; 1",
                                dur: "3s",
                                repeatCount: "indefinite"
                            })
                        ]
                    })
                ]
            })
        ]
    });
};
;
 //# sourceMappingURL=module.js.map
}}),

};

//# sourceMappingURL=node_modules_react-loader-spinner_dist_module_2c1dbed8.js.map