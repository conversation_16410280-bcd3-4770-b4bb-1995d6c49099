module.exports = {
  friendlyName: "Edit message",

  description: "",

  inputs: {
    chat_id: {
      type: "string",
      description: "Chat ID",
      required: true,
    },
    message_id: {
      type: "string",
      description: "Message ID to edit",
      required: true,
    },
  },

  exits: {
    success: {
      description: "All done.",
    },
  },

  fn: async function (inputs) {
    const TOKEN = process.env.TELEGRAM_BOT_TOKEN;
    const TELEGRAM_API = `https://api.telegram.org/bot${TOKEN}`;

    try {
      const res = await axios.post(`${TELEGRAM_API}/editMessageText`, {});

      sails.log.info(res.data);

      return {
        chat_id: res.data.result.chat.id,
        message_id: res.data.result.message_id,
      };
    } catch (error) {
      sails.log.error("Error sending message:", error);
      throw error;
    }
  },
};
