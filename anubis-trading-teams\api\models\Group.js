/**
 * Group.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
  attributes: {
    owner: {
      model: "user",
      required: true,
    },
    groupOwnerId: {
      type: "number",
      description: "group owner ID",
      required: true,
      unique: true,
    },
    groupId: {
      type: "number",
      description: "Group ID",
      required: true,
      unique: true,
    },
    groupTitle: {
      type: "string",
      description: "Telegram Group Title",
      required: true,
    },
    page: {
      collection: "page",
      via: "group",
    },
    buyToken: {
      type: "json",
      defaultsTo: {
        contractAddress: "",
        symbol: "",
        chain: "",
        name: "",
        logo: "",
        websites:[],
        socials:[]
      },
    },
  },
};
