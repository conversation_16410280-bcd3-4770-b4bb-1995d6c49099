/**
 * Creates a new user in the database.
 * @param {string} id - The telegram chat id of the user.
 * @param {string} username - The telegram username of the user.
 * @param {string} firstName - The telegram first name of the user.
 * @returns {Promise<void>}
 */
async function createNewUser(id, username, firstName) {
  try {
    const user = await User.findOne({ chatId: id });
    if (user) return user;

    const newUser = await User.create({
      chatId: id,
      username: username,
      firstName: firstName,
    }).fetch();

    return newUser;
  } catch (error) {
    sails.log.error("There was a problem signing up users:", error.message);
    return null;
  }
}

/**
 * Finds a user in the database by their telegram chat id.
 * @param {string} id - The telegram chat id of the user.
 * @returns {Promise<null|User>}
 */
async function getUser(id) {
  try {
    const user = await User.findOne({ chatId: id });
    if (!user) return null;
    return user;
  } catch (error) {
    sails.log.error(error.message);
    return null;
  }
}

/**
 * Creates a new group in the database.
 * @param {string} owner - The telegram chat id of the owner of the group.
 * @param {string} groupOwnerId - The telegram chat id of the owner of the group.
 * @param {string} groupId - The telegram group id.
 * @param {string} groupTitle - The title of the group.
 * @returns {Promise<void>}
 */
async function createNewGroup(owner, groupOwnerId, groupId, groupTitle) {
  try {
    const groupRecord = await Group.findOne({ groupId });
    if (groupRecord) return groupRecord;

    const group = await Group.create({
      owner,
      groupOwnerId,
      groupId,
      groupTitle: groupTitle,
    }).fetch();

    return group;
  } catch (error) {
    sails.log.error(error.message);
  }
}

/**
 * Finds a group in the database by their telegram group id.
 * @param {string} id - The telegram group id of the group.
 * @returns {Promise<null|Group>}
 */
async function getGroup(id) {
  try {
    const groupRecord = await Group.findOne({ groupId: id });
    if (!groupRecord) return null;

    return groupRecord;
  } catch (error) {
    sails.log.error(error.message);
  }
}

/**
 * Finds all groups in the database by their telegram group owner id.
 * @param {string} id - The telegram group owner id of the groups.
 * @returns {Promise<null|Group[]>}
 */
async function getGroups(id) {
  try {
    const groups = await Group.find({ groupOwnerId: id });
    return groups;
  } catch (error) {
    sails.log.error(error.message);
  }
}

module.exports = { createNewUser, getUser, createNewGroup, getGroup, getGroups };
