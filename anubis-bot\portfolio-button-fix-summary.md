# Portfolio Button Fix

## Problem
The buttons below the portfolio display were not correctly matching the tokens shown in the portfolio. Some buttons were pointing to invalid contract addresses or showing different token names than what appeared in the trade message.

## Root Cause Analysis
1. The token buttons were being created based on the raw index in the `ctx.session.data.tokens` array
2. This approach didn't account for tokens that failed to load or had missing information
3. The pagination was based on the total number of tokens rather than valid tokens with information
4. There was a mismatch between the displayed tokens and the buttons created for them

## Solution
We've implemented a more robust approach to ensure that portfolio buttons correctly match the displayed tokens:

### 1. Improved Token Mapping
- Created a mapping between token addresses and their display information
- Filtered out tokens that don't have valid information
- Ensured that each button corresponds to a valid token with display information

### 2. Enhanced Pagination Logic
- Based pagination on the count of valid tokens rather than all tokens
- Properly sliced the valid tokens array to get tokens for the current page
- Maintained correct indexing to ensure buttons point to the right tokens

### 3. Robust Button Creation
- Used the token's actual index in the session data for the callback data
- Extracted token names directly from the display information
- Added error handling to gracefully handle any issues during button creation

## Implementation Details

### Token Mapping and Filtering
```typescript
// Create a mapping of token addresses to their display info
const tokenInfoMap = new Map();
ctx.session.data.tokens.forEach((token, index) => {
  if (tokensWithInfo[index]) {
    tokenInfoMap.set(token, {
      index,
      info: tokensWithInfo[index]
    });
  }
});

// Get valid tokens (tokens with info)
const validTokens = ctx.session.data.tokens.filter((_, index) => tokensWithInfo[index]);
```

### Pagination Based on Valid Tokens
```typescript
// Get current page from session or default to 0
const currentPage = ctx.session.portfolioPage || 0;
const tokensPerPage = 3; // Number of tokens to show per page
const totalTokens = validTokens.length;
const totalPages = Math.ceil(totalTokens / tokensPerPage);

// Calculate start and end indices for current page
const startIndex = currentPage * tokensPerPage;
const endIndex = Math.min(startIndex + tokensPerPage, totalTokens);

// Get tokens for the current page
const pageTokens = validTokens.slice(startIndex, endIndex);
```

### Button Creation with Proper References
```typescript
// Add buttons for tokens on current page
for (const token of pageTokens) {
  try {
    const tokenData = tokenInfoMap.get(token);
    if (!tokenData) continue;
    
    // Get token name
    let tokenName = `Token ${tokenData.index + 1}`;
    const match = tokenData.info.match(/🔹 ([^:]+):/);
    if (match && match[1]) {
      tokenName = match[1].trim();
    }
    
    // Add a button for this token
    inlineKeyboard.push([{
      text: `Trade ${tokenName}`,
      callback_data: `trade::${tokenData.index}`
    }]);
  } catch (error) {
    console.error(`Error creating button for token ${token}:`, error);
  }
}
```

## Benefits
1. **Improved Reliability**: Buttons now correctly match the tokens displayed in the portfolio
2. **Better User Experience**: Token names in buttons match the names shown in the trade screen
3. **Reduced Errors**: Eliminated cases where buttons pointed to invalid tokens
4. **Enhanced Pagination**: Pagination now works correctly with the actual number of valid tokens
