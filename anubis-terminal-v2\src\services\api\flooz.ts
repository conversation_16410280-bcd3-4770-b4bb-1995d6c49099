import axios from 'axios';

interface GetGainerResponse {
    // Define the response type based on the actual API response structure
    data: any;
}

export interface TokenPriceData {
    time: number;
    price: string;
}

export const getGainerTokenPairs = async (networks: string, time: string) => {
    const url = "https://api.flooz.trade/v2/trend";
    const params = {
        networks: networks,
        trendType: 'gainers',
        volumePeriod: time,
    };

    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };

    try {
        const response = await axios.get(url, {
            params,
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching new trends:', error);
        throw error;
    }
};


export const getNewTokenPairs = async (networks: string, time: string) => {
    const url = "https://api.flooz.trade/v2/trend";
    const params = {
        networks: networks,
        trendType: 'newPairs',
        volumePeriod: time,
    };

    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };

    try {
        const response = await axios.get(url, {
            params,
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching new trends:', error);
        throw error;
    }
};

export const getTrendingTokens = async (networks: string, time: string) => {
    const url = "https://api.flooz.trade/v2/trend";
    const params = {
        networks: networks,
        volumePeriod: time,
    };

    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };

    try {
        const response = await axios.get(url, {
            params,
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching new trends:', error);
        throw error;
    }
};

export const getTokenData = async (networks: string, address: `0x${string}`) => {
    const url = "https://api.flooz.trade/v2/trend";

    const body = {
        filters: {
            networks: [networks]
        },
        tokens: [address]
    };

    const headers = {
        accept: '*/*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };

    try {
        const response = await axios.post(url, body, { headers });
        return response.data;
    } catch (error) {
        console.error('Error fetching token data:', error);
        throw error;
    }
}


export const getTokenPriceHistory = async (
    network: string,
    address: `0x${string}`
): Promise<TokenPriceData[]> => {
    const url = `https://api.flooz.trade/v2/tokens/${address}/price`;

    // Get current date in ISO format
    const till = new Date().toISOString();

    const params = {
        network,
        till,
        countBack: 300,
        resolution: 5
    };

    const headers = {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9',
        authorization: `Bearer ${process.env.NEXT_PUBLIC_FLOOZ_API_KEY}`,
        priority: 'u=1, i',
        'save-data': 'on',
        'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?1',
        'sec-ch-ua-platform': '"Android"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'x-statsig-user': 'b6938133-0d8c-4294-8f14-7e99bc430891',
        'Referer': 'https://flooz.xyz/',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };

    try {
        const response = await axios.get(url, {
            params,
            headers
        });
        return response.data.data;
    } catch (error) {
        console.error('Error fetching token price history:', error);
        throw error;
    }
};