module.exports = {
  friendlyName: "Get Sign In OTP",

  description: "",

  inputs: {
    username: {
      type: "string",
      description: "Username",
      required: true,
    },
  },

  exits: {
    notAdmin: {
      statusCode: 401,
      description: "Wallet Address not an admin account",
    },
    success: {
      statusCode: 200,
      description: "Successfully delivered auth token to user",
    },
    noUserAccountFound: {
      statusCode: 404,
      description: "No user account with chat id found",
    },
  },

  fn: async function (inputs, exits) {
    const { username } = inputs;

    // Find Admin
    const admin = await Admin.findOne({ username });

    if (!admin) {
      return exits.notAdmin({ message: "Access denied" });
    }

    // Make sure Admin has a bot account
    const user = await User.findOne({ chatId: admin.chatId });

    if (!user) {
      return exits.noUserAccountFound({
        message: "Failed to locate Admin Account Data",
      });
    }

    const otp = await sails.helpers.getOtp();

    const message = await sails.helpers.sendMessage(
      admin.chatId,
      `Your OTP to Access RGOAT Bot Management : <code>${otp}</code>\nThis message will delete in 2 minutes...`
    );

    if (message) {
      setTimeout(async () => {
        await sails.helpers.deleteMessage(admin.chatId, message.message_id);
      }, 120000); // 2 minutes
    }

    await Admin.updateOne({ id: admin.id }).set({
      token: otp,
    });

    return exits.success({ message: "Successfully delivered OTP" });
  },
};
