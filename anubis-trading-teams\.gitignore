################################################
#   ┌─┐┬┌┬┐╦╔═╗╔╗╔╔═╗╦═╗╔═╗
#   │ ┬│ │ ║║ ╦║║║║ ║╠╦╝║╣
#  o└─┘┴ ┴ ╩╚═╝╝╚╝╚═╝╩╚═╚═╝
#
# > Files to exclude from your app's repo.
#
# This file (`.gitignore`) is only relevant if
# you are using git.
#
# It exists to signify to git that certain files
# and/or directories should be ignored for the
# purposes of version control.
#
# This keeps tmp files and sensitive credentials
# from being uploaded to  your repository.  And
# it allows you to configure your app for your
# machine without accidentally committing settings
# which will smash the local settings of other
# developers on your team.
#
# Some reasonable defaults are included below,
# but, of course, you should modify/extend/prune
# to fit your needs!
#
################################################


################################################
# Local Configuration
#
# Explicitly ignore files which contain:
#
# 1. Sensitive information you'd rather not push to
#    your git repository.
#    e.g., your personal API keys or passwords.
#
# 2. Developer-specific configuration
#    Basically, anything that would be annoying
#    to have to change every time you do a
#    `git pull` on your laptop.
#    e.g. your local development database, or
#    the S3 bucket you're using for file uploads
#    during development.
#
################################################

config/local.js


################################################
# Dependencies
#
#
# When releasing a production app, you _could_
# hypothetically include your node_modules folder
# in your git repo, but during development, it
# is always best to exclude it, since different
# developers may be working on different kernels,
# where dependencies would need to be recompiled
# anyway.
#
# Most of the time, the node_modules folder can
# be excluded from your code repository, even
# in production, thanks to features like the
# package-lock.json file / NPM shrinkwrap.
#
# But no matter what, since this is a Sails app,
# you should always push up the package-lock.json
# or shrinkwrap file to your repository, to avoid
# accidentally pulling in upgraded dependencies
# and breaking your code.
#
# That said, if you are having trouble with
# dependencies, (particularly when using
# `npm link`) this can be pretty discouraging.
# But rather than just adding the lockfile to
# your .gitignore, try this first:
# ```
#     rm -rf node_modules
#     rm package-lock.json
#     npm install
# ```
#
# [?] For more tips/advice, come by and say hi
#     over at https://sailsjs.com/support
#
################################################

node_modules


################################################
#
# > Do you use bower?
# > re: the bower_components dir, see this:
# > http://addyosmani.com/blog/checking-in-front-end-dependencies/
# > (credit Addy Osmani, @addyosmani)
#
################################################


################################################
# Temporary files generated by Sails/Waterline.
################################################

.tmp


################################################
# Miscellaneous
#
# Common files generated by text editors,
# operating systems, file systems, dbs, etc.
################################################

*~
*#
.DS_STORE
.netbeans
nbproject
.idea
*.iml
.vscode
.node_history
dump.rdb

npm-debug.log
lib-cov
*.seed
*.log
*.out
*.pid

.env