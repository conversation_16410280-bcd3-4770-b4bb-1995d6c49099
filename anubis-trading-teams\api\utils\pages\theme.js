const { infoParseQuery } = require("../lib");
const { page_update_msg } = require("../message-structure");

async function updateTheme(pageId, ctx) {
  const { id: chatId } = infoParseQuery(ctx);

  // Find Page Record
  const page = await Page.findOne({ id: pageId });

  if (!page) {
    await sails.helpers.sendMessage(chatId, `Server Process Error ❌`);

    return;
  }

  const updatedPage = await Page.updateOne({ id: pageId }).set({
    pageTheme: page.pageTheme === "dark" ? "light" : "dark",
  });

  const keyboard = page_update_msg(updatedPage.id, updatedPage.pageTheme);

  await sails.helpers.sendMessage(
    chatId,
    `${updatedPage.displayName}'s theme has been set to ${
      updatedPage.pageTheme
    } : ${updatedPage.pageTheme === "dark" ? "🌑" : "☀"}`,
    keyboard
  );

  return;
}

module.exports = { updateTheme };
