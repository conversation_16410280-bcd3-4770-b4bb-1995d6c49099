/**
 * Route Mappings
 * (sails.config.routes)
 *
 * Your routes tell <PERSON><PERSON> what to do each time it receives a request.
 *
 * For more information on configuring custom routes, check out:
 * https://sailsjs.com/anatomy/config/routes-js
 */

module.exports.routes = {
  /***************************************************************************
   *                                                                          *
   * Make the view located at `views/homepage.ejs` your home page.            *
   *                                                                          *
   * (Alternatively, remove this and add an `index.html` file in your         *
   * `assets` directory)                                                      *
   *                                                                          *
   ***************************************************************************/

  "/": { action: "index" },
  "POST /api/v1/webhook/telegram": { action: "bot/process-webhook" },

  // Auth
  "POST /api/v1/auth/login": { action: "auth/get-otp" },

  //Twitter Verification
  "GET /auth/twitter": { action: "auth/twitter" },

  // GET Page
  "GET /page/:url": { action: "page/get-page" },



  /***************************************************************************
   *                                                                          *
   * More custom routes here...                                               *
   * (See https://sailsjs.com/config/routes for examples.)                    *
   *                                                                          *
   * If a request to a URL doesn't match any of the routes in this file, it   *
   * is matched against "shadow routes" (e.g. blueprint routes).  If it does  *
   * not match any of those, it is matched against static assets.             *
   *                                                                          *
   ***************************************************************************/
};
