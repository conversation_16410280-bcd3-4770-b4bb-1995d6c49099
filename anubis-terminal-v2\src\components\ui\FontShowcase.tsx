export default function FontShowcase() {
  return (
    <div className="max-w-4xl mx-auto p-8 space-y-12 bg-[#0a0a0a] rounded-lg border border-white/10">
      <div>
        <h2 className="text-h2 heading-primary mb-6">Orbitron Font</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-h3 text-white/70">Font Weights</h3>
            <div className="space-y-2">
              <p className="font-orbitron font-light">Orbitron Light (300)</p>
              <p className="font-orbitron font-regular">Orbitron Regular (400)</p>
              <p className="font-orbitron font-medium">Orbitron Medium (500)</p>
              <p className="font-orbitron font-semibold">Orbitron Semibold (600)</p>
              <p className="font-orbitron font-bold">Orbitron Bold (700)</p>
              <p className="font-orbitron font-extrabold">Orbitron Extrabold (800)</p>
              <p className="font-orbitron font-black">Orbitron Black (900)</p>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-h3 text-white/70">Heading Styles</h3>
            <div className="space-y-2">
              <p className="heading-primary text-display">Display</p>
              <p className="heading-primary text-h1">Heading 1</p>
              <p className="heading-primary text-h2">Heading 2</p>
              <p className="heading-secondary text-h3">Heading 3</p>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-h2 heading-primary mb-6">Space Grotesk Font</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-h3 text-white/70">Font Weights</h3>
            <div className="space-y-2">
              <p className="font-space-grotesk font-light">Space Grotesk Light (300)</p>
              <p className="font-space-grotesk font-regular">Space Grotesk Regular (400)</p>
              <p className="font-space-grotesk font-medium">Space Grotesk Medium (500)</p>
              <p className="font-space-grotesk font-semibold">Space Grotesk Semibold (600)</p>
              <p className="font-space-grotesk font-bold">Space Grotesk Bold (700)</p>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-h3 text-white/70">Body Text Styles</h3>
            <div className="space-y-2">
              <p className="body-text text-body-large">Body Large</p>
              <p className="body-text text-body">Body Regular</p>
              <p className="body-text text-body-small">Body Small</p>
              <p className="body-text-bold text-body">Body Bold</p>
              <p className="body-text text-caption">Caption</p>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-h2 heading-primary mb-6">Usage Examples</h2>
        <div className="space-y-6">
          <div className="p-6 border border-white/10 rounded-lg">
            <h3 className="heading-primary text-h2 mb-4">Anubis Terminal</h3>
            <p className="body-text text-body-large mb-2">
              Snipe and Sell tokens on-chain instantly
            </p>
            <p className="body-text text-body mb-4">
              Experience lightning-fast trading with Anubis, the most advanced on-chain trading terminal.
            </p>
            <button className="font-orbitron font-semibold bg-white text-black px-6 py-3 rounded-md">
              Trade Now
            </button>
          </div>

          <div className="p-6 border border-white/10 rounded-lg">
            <h3 className="heading-secondary text-h3 mb-4">Error Message</h3>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span className="font-space-grotesk font-medium">Connection Error</span>
            </div>
            <p className="body-text text-body-small text-white/70">
              Unable to connect to the server. Please check your internet connection and try again.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
