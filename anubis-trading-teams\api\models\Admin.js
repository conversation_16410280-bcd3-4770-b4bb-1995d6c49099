/**
 * Admin.js
 *
 * @description :: A model definition represents a database table/collection.
 * @docs        :: https://sailsjs.com/docs/concepts/models-and-orm/models
 */

module.exports = {
  attributes: {
    token: {
      type: "string",
      description: "Token For Action Validation",
    },
    chatId: {
      type: "string",
      description: "Telegram Chat ID",
      required: true,
    },
    username: {
      type: "string",
      description: "Telegram Username",
      required: true,
    },
  },
};
