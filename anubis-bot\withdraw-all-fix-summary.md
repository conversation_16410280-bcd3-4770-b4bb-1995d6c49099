# "Withdraw All" Token Transfer Fix

## Problem
The bot was encountering a parsing error when trying to transfer all native tokens:
```
SyntaxError: Failed to parse String to BigInt
```

This error occurred because the code was trying to parse the string "withdraw-all" as a BigInt, which is not a valid number.

## Root Cause Analysis
1. The `transferNativeToken` function was trying to parse the string "withdraw-all" directly to a BigInt
2. The function was not properly handling the special "withdraw-all" case before attempting to parse the amount

## Solution

### 1. Restructured the `transferNativeToken` Function
- Moved the balance retrieval to the beginning of the function
- Added special handling for the "withdraw-all" string
- Separated the logic for regular transfers and "withdraw-all" transfers

```typescript
// Get sender's balance first
const senderBalance = await client.getBalance({
  address: wallet.account!.address,
});

// Handle the special "withdraw-all" case
let amountBigInt: bigint;
if (amount === "withdraw-all") {
  // We'll calculate the actual amount later after determining gas fees
  amountBigInt = senderBalance; // Temporary value
} else {
  // Normal case - parse the amount
  amountBigInt = parseUnits(amount.toString(), 18);
}
```

### 2. Improved the "Withdraw All" Logic
- Directly calculated the final amount by subtracting gas fees from the total balance
- Added a check to ensure we're not trying to send a negative or zero amount
- Simplified the logic flow for better readability and maintainability

```typescript
// Calculate final amount to transfer
let finalAmount: bigint;
if (amount === "withdraw-all") {
  // For withdraw-all, subtract gas fee from balance
  finalAmount = senderBalance - gasFee;
  console.log(`Adjusted "withdraw-all" amount to leave gas fee: ${formatUnits(finalAmount, 18)} ${userConfig.chain.nativeCurrency.symbol}`);
} else {
  finalAmount = amountBigInt;
  
  // Calculate total cost (amount + gas fees)
  const totalCost = finalAmount + gasFee;

  // Ensure sender has enough balance
  if (senderBalance < totalCost) {
    const shortfall = formatUnits(totalCost - senderBalance, 18);
    throw new Error(
      `Insufficient funds. You need an additional ${shortfall} ${userConfig.chain.nativeCurrency.symbol} to cover the transfer and gas fees.`
    );
  }
}

// Make sure we're not trying to send a negative or zero amount
if (finalAmount <= BigInt(0)) {
  throw new Error(
    `Insufficient funds to cover gas fees. Cannot complete the transfer.`
  );
}
```

## Benefits
1. **Reliable "Withdraw All" Functionality**: The "withdraw-all" option now works correctly without parsing errors
2. **Better Error Handling**: Added specific error messages for different failure scenarios
3. **Improved Code Structure**: Clearer separation of logic for different transfer types
4. **Safety Checks**: Added checks to prevent sending negative or zero amounts

## Technical Details
The key insight was to handle the special "withdraw-all" string separately from regular numeric amounts. By retrieving the balance first and then handling the different cases appropriately, we can ensure that the "withdraw-all" functionality works correctly without trying to parse an invalid string as a number.
