interface MessagePayload{
    chat_id: string;
    text: string;
    parse_mode?: string;
    reply_markup?: string;
    enableWebPreview?: boolean;
}

export const sendMessage = async ({
    chat_id,
    text,
    parse_mode = "HTML",
    reply_markup,
    enableWebPreview,
}: MessagePayload) => {
    try {
        const response = await fetch(`https://api.telegram.org/bot${process.env.BOT_TOKEN}/sendMessage`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                chat_id,
                text,
                parse_mode,
                reply_markup,
                disable_web_page_preview: !enableWebPreview,
            }),
        });
        return response.json();
    } catch (error) {
        console.error(error);
    }
}