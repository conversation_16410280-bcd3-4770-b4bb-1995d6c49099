// This is the function to add to index.ts after the deleteMessage function
async function showMainMenu(ctx: BotContext) {
  const messageLines = [
    `${ctx.me.first_name}!`,
    "Anubis is a powerful and intuitive trading bot designed to revolutionize your decentralized trading experience.",
    "",
    "*🚀 Main Menu*",
    "Select an option to get started or paste a token address.",
  ];

  // Delete the current message
  const [chatId, messageId] = ctx.match?.split("::") ?? [];
  if (chatId && messageId) {
    await ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => {});
  }
  
  // Send the main menu
  return send(ctx, messageLines, {
    reply_markup: mainMenu,
    parse_mode: "MarkdownV2",
  });
}

// Update the mainMenu close button to use showMainMenu instead of deleteMessage
// Change this line in the mainMenu definition:
//  .text(
//    {
//      text: "❌ Close",
//      payload: getClosePayload,
//    },
//    deleteMessage
//  );
// 
// To:
//  .text(
//    {
//      text: "🏠 Main Menu",
//      payload: getClosePayload,
//    },
//    showMainMenu
//  );

// Also update the tokenMenu close button:
//  .text(
//    {
//      text: "❌ Close",
//      payload: getClosePayload,
//    },
//    deleteMessage
//  );
// 
// To:
//  .text(
//    {
//      text: "🏠 Main Menu",
//      payload: getClosePayload,
//    },
//    showMainMenu
//  );

// And update the settingsMenu close button:
//  .text(
//    {
//      text() {
//        return "❌ Close";
//      },
//      payload(ctx) {
//        const payload = getClosePayload(ctx);
//        return payload;
//      },
//    },
//    (ctx) => {
//      const [chatId, messageId] = ctx.match.split("::");
//      if (chatId && messageId) {
//        ctx.api.deleteMessage(chatId, Number(messageId)).catch(() => { });
//        ctx.deleteMessage().catch(() => { });
//      } else {
//        ctx.menu.back();
//      }
//    }
//  );
//
// To:
//  .text(
//    {
//      text() {
//        return "🏠 Main Menu";
//      },
//      payload(ctx) {
//        const payload = getClosePayload(ctx);
//        return payload;
//      },
//    },
//    showMainMenu
//  );
