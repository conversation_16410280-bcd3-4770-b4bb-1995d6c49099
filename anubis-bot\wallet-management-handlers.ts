// Function to generate a new wallet
async function handleGenerateNewWallet(ctx: BotContext) {
  // Create a new random wallet
  const wallet = ethers.Wallet.createRandom();
  
  // Add the wallet to the user's wallets
  ctx.session.settings.wallets.push({
    address: wallet.address as Address,
    phrase: wallet.mnemonic?.phrase,
    privateKey: wallet.privateKey as Address,
  });
  
  // Show success message with private key
  await send(ctx, [
    "✅ *New Wallet Generated*",
    "",
    "Your new wallet has been created and added to your wallet list.",
    "",
    "🔑 *IMPORTANT: Save this private key in a secure location!*",
    `\`${wallet.privateKey}\``,
    "",
    "🔒 *Mnemonic Phrase (Backup):*",
    `\`${wallet.mnemonic?.phrase}\``,
    "",
    "Address:",
    `\`${wallet.address}\``,
  ], {
    parse_mode: "MarkdownV2",
  });
  
  // Show the updated wallet list
  await handleWallets(ctx);
}

// Function to handle wallet management
async function handleManageWallet(ctx: BotContext, walletIndex: number) {
  // Set the selected wallet as active
  ctx.session.walletId = walletIndex;
  const wallet = ctx.session.settings.wallets[walletIndex];
  
  if (!wallet) {
    await send(ctx, ["❌ Wallet not found"]);
    return;
  }
  
  // Get wallet balance
  const balance = await getBalance(
    wallet.address,
    getConfig(ctx.session.settings.chainId).nativeCurrencyAddress,
    ctx.session.settings.chainId
  );
  
  const symbol = getConfig(ctx.session.settings.chainId).chain.nativeCurrency.symbol;
  
  // Create message with wallet details
  const messageLines = [
    "🔒 *Wallet Details*",
    "",
    `Address: \`${wallet.address}\``,
    `Balance: ${balance} ${symbol}`,
    "",
    "Select an action:"
  ];
  
  // Create inline keyboard with wallet actions
  const inlineKeyboard = [
    [
      { text: "✅ Set as Active", callback_data: `wallet_action:set_active:${walletIndex}` },
      { text: "🔑 Show Private Key", callback_data: `wallet_action:show_key:${walletIndex}` }
    ],
    [
      { text: "🔄 Transfer Tokens", callback_data: "main_menu_action:transfer" },
      { text: "❌ Remove Wallet", callback_data: `wallet_action:remove:${walletIndex}` }
    ],
    [
      { text: "🔙 Back to Wallets", callback_data: "main_menu_action:wallets" },
      { text: "🏠 Main Menu", callback_data: "main_menu_action:main_menu" }
    ]
  ];
  
  // Send the message with inline keyboard
  await send(ctx, messageLines, {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: inlineKeyboard
    }
  });
}

// Function to show wallet private key
async function handleShowWalletKey(ctx: BotContext, walletIndex: number) {
  const wallet = ctx.session.settings.wallets[walletIndex];
  
  if (!wallet) {
    await send(ctx, ["❌ Wallet not found"]);
    return;
  }
  
  // Show private key with warning
  await send(ctx, [
    "🔑 *Private Key*",
    "",
    "⚠️ *CAUTION: Never share your private key with anyone!*",
    "",
    `\`${wallet.privateKey}\``,
    "",
    "Mnemonic Phrase (if available):",
    wallet.phrase ? `\`${wallet.phrase}\`` : "Not available"
  ], {
    parse_mode: "MarkdownV2",
    reply_markup: {
      inline_keyboard: [
        [{ text: "🔙 Back to Wallet", callback_data: `wallet_action:manage:${walletIndex}` }]
      ]
    }
  });
}

// Function to remove a wallet
async function handleRemoveWallet(ctx: BotContext, walletIndex: number) {
  // Check if this is the only wallet
  if (ctx.session.settings.wallets.length <= 1) {
    await send(ctx, [
      "❌ Cannot remove wallet",
      "",
      "You must have at least one wallet. Please create or import another wallet before removing this one."
    ], {
      parse_mode: "MarkdownV2",
      reply_markup: {
        inline_keyboard: [
          [{ text: "🔙 Back to Wallet", callback_data: `wallet_action:manage:${walletIndex}` }]
        ]
      }
    });
    return;
  }
  
  // Remove the wallet
  ctx.session.settings.wallets = ctx.session.settings.wallets.filter((_, index) => index !== walletIndex);
  
  // If the removed wallet was active, set the first wallet as active
  if (ctx.session.walletId === walletIndex) {
    ctx.session.walletId = 0;
  } else if (ctx.session.walletId > walletIndex) {
    // Adjust the active wallet index if it was after the removed wallet
    ctx.session.walletId--;
  }
  
  // Show success message
  await send(ctx, [
    "✅ Wallet removed successfully"
  ], {
    parse_mode: "MarkdownV2"
  });
  
  // Show the updated wallet list
  await handleWallets(ctx);
}

// Function to set a wallet as active
async function handleSetActiveWallet(ctx: BotContext, walletIndex: number) {
  // Set the selected wallet as active
  ctx.session.walletId = walletIndex;
  
  // Show success message
  await send(ctx, [
    "✅ Wallet set as active"
  ], {
    parse_mode: "MarkdownV2"
  });
  
  // Show the updated wallet list
  await handleWallets(ctx);
}
