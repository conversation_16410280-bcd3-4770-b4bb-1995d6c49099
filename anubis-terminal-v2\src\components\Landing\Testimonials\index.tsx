"use client"
import { motion } from "framer-motion";
import { useState, useEffect } from "react";

interface TestimonialProps {
  quote: string;
  author: string;
  role: string;
}

const testimonials: TestimonialProps[] = [
  {
    quote: "Anubis Terminal has completely transformed my trading experience. The speed and security are unmatched in the industry.",
    author: "<PERSON>",
    role: "Professional Trader"
  },
  {
    quote: "The MEV protection alone is worth it. I've saved thousands in potential front-running losses since switching to Anubis.",
    author: "<PERSON>",
    role: "DeFi Investor"
  },
  {
    quote: "Cross-chain trading has never been easier. I can manage all my positions from a single interface with minimal fees.",
    author: "<PERSON>",
    role: "Crypto Fund Manager"
  }
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0); // -1 for left, 1 for right, 0 for initial

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handlePrev = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  const handleNext = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  };

  return (
    <section className="py-20 px-4 md:px-8 lg:px-12 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#0a0a0a] to-[#0f0f0f] z-0"></div>
      
      {/* Animated background lines */}
      <div className="absolute inset-0 z-0 opacity-20">
        {Array.from({ length: 10 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-px bg-white/30 w-full"
            style={{ top: `${i * 10}%` }}
            animate={{
              opacity: [0.1, 0.3, 0.1],
              width: ["0%", "100%", "0%"]
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              delay: i * 0.5,
              repeatType: "reverse"
            }}
          />
        ))}
      </div>

      <div className="max-w-4xl mx-auto relative z-10">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
            Trader Testimonials
          </h2>
          <p className="text-gray-400 font-space-grotesk max-w-2xl mx-auto text-lg">
            See what professional traders are saying about Anubis Terminal
          </p>
        </motion.div>

        <div className="relative h-[300px] md:h-[250px]">
          <motion.div
            key={currentIndex}
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 }
            }}
            className="absolute w-full"
          >
            <div className="bg-gradient-to-br from-[#0f0f0f] to-[#1a1a1a] p-8 rounded-lg border border-white/10">
              <div className="flex flex-col items-center text-center">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <svg className="w-10 h-10 text-white/30 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                </motion.div>
                <p className="text-white font-space-grotesk text-lg mb-6">{testimonials[currentIndex].quote}</p>
                <div>
                  <h4 className="font-orbitron font-bold text-white">{testimonials[currentIndex].author}</h4>
                  <p className="text-gray-400 font-space-grotesk">{testimonials[currentIndex].role}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="flex justify-center mt-8 gap-4">
          <button 
            onClick={handlePrev}
            className="w-10 h-10 rounded-full border border-white/20 flex items-center justify-center text-white hover:bg-white/10 transition-colors"
          >
            &larr;
          </button>
          <div className="flex gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setDirection(index > currentIndex ? 1 : -1);
                  setCurrentIndex(index);
                }}
                className={`w-3 h-3 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/30"
                } transition-colors`}
              />
            ))}
          </div>
          <button 
            onClick={handleNext}
            className="w-10 h-10 rounded-full border border-white/20 flex items-center justify-center text-white hover:bg-white/10 transition-colors"
          >
            &rarr;
          </button>
        </div>
      </div>
    </section>
  );
}
