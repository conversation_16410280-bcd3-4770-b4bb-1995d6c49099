"use client"
import { useState } from "react"
import { globalNavLinks, GlobalNavLinks } from "@/utils/data"
import Link from "next/link"
import { GiHamburgerMenu } from "react-icons/gi";
import { motion } from "framer-motion";
import { IoMdClose } from "react-icons/io";

export default function Navbar() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    return (
        <nav className="max-w-full flex justify-between items-center py-5 px-4 md:px-8 lg:px-12 relative">
            {/* Logo */}
            <Link href="/" className="flex items-center justify-start z-10 gap-2">
                <img src="/logo/icon.png" className="w-8 h-8 object-contain" alt="Anubis Logo" />
                <span className="font-orbitron font-bold text-xl">Anubis</span>
            </Link>

            {/* Desktop Links */}
            <div className="hidden md:flex items-center gap-x-5 capitalize py-1">
                {globalNavLinks.map((link: GlobalNavLinks) => (
                    <Link
                        href={link.url}
                        key={link.title}
                        className="font-space-grotesk font-medium text-gray-400 hover:text-white relative group transition-all duration-300 text-md"
                        target={link.url[0] !== "#" ? "_blank" : "_self"}
                    >
                        {link.title}
                        <span className="absolute -bottom-1 left-0 w-0 h-[2px] bg-white transition-all duration-300 group-hover:w-full"></span>
                    </Link>
                ))}
            </div>

            {/* Desktop CTA */}
            <div className="hidden md:block">
                <Link href="/login" className="
                 cursor-pointer" target="_blank">
                    <motion.button
                        className="font-orbitron font-semibold bg-white text-black px-6 sm:px-8 py-2 sm:py-3 rounded-md text-sm sm:text-base hover:shadow-lg hover:shadow-white/20 w-full sm:w-auto"
                        whileHover={{
                            boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                            scale: 1.05
                        }}
                        whileTap={{
                            backgroundColor: "#f0f0f0",
                            scale: 1.03
                        }}
                    >
                        Trade Now
                    </motion.button>
                </Link>
            </div>

            {/* Mobile Menu Button */}
            <motion.button
                className="md:hidden z-10 text-white text-2xl p-2 rounded-md"
                onClick={toggleMenu}
                aria-label="Toggle menu"
                whileTap={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
            >
                {isMenuOpen ? <IoMdClose /> : <GiHamburgerMenu />}
            </motion.button>

            {/* Mobile Menu */}
            <motion.div
                className={`fixed inset-0 bg-[#0a0a0a] flex flex-col justify-between transition-all duration-300 ease-in-out z-50 ${isMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
                    } md:hidden`}
                initial={{ opacity: 0, y: -20 }}
                animate={{
                    opacity: isMenuOpen ? 1 : 0,
                    y: isMenuOpen ? 0 : -20
                }}
                transition={{ duration: 0.3 }}
            >
                {/* Close button at the top */}
                <div className="flex justify-between items-center w-full px-6 py-5 border-b border-gray-800">
                    <Link href="/" className="flex items-center gap-2" onClick={() => setIsMenuOpen(false)}>
                        <img src="/logo/icon.png" className="w-8 h-8 object-contain" alt="Anubis Logo" />
                        <span className="font-orbitron uppercase font-bold text-xl">Anubis</span>
                    </Link>
                    <button
                        className="text-white text-2xl p-2"
                        onClick={toggleMenu}
                        aria-label="Close menu"
                    >
                        <IoMdClose />
                    </button>
                </div>

                {/* Menu links */}
                <div className="flex-1 flex flex-col items-center justify-center gap-y-4 w-full px-8 py-10 overflow-y-auto">
                    {globalNavLinks.map((link: GlobalNavLinks, index) => (
                        <motion.div
                            key={link.title}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isMenuOpen ? 1 : 0,
                                y: isMenuOpen ? 0 : 20
                            }}
                            transition={{
                                duration: 0.3,
                                delay: isMenuOpen ? 0.1 + (index * 0.1) : 0
                            }}
                            className="w-full"
                        >
                            <Link
                                href={link.url}
                                className="font-space-grotesk font-medium text-gray-300 hover:text-white text-xl w-full text-center py-3 border-b border-gray-800 block"
                                target={link.url[0] !== "#" ? "_blank" : "_self"}
                                onClick={() => setIsMenuOpen(false)}
                            >
                                {link.title}
                            </Link>
                        </motion.div>
                    ))}
                </div>

                {/* CTA at the bottom */}
                <div className="w-full px-8 py-6 flex justify-center border-t border-gray-800">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                            opacity: isMenuOpen ? 1 : 0,
                            y: isMenuOpen ? 0 : 20
                        }}
                        transition={{
                            duration: 0.3,
                            delay: isMenuOpen ? 0.5 : 0
                        }}
                        className="w-full max-w-xs mx-auto"
                    >
                        <Link
                            href="/terminal/auth"
                            target="_blank"
                            className="w-full block cursor-pointer"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            <motion.button
                                className="font-orbitron font-semibold bg-white text-black px-8 py-3 rounded-md hover:shadow-lg hover:shadow-white/20 w-full"
                                whileHover={{
                                    boxShadow: "0 0 15px rgba(255, 255, 255, 0.5)",
                                    scale: 1.05
                                }}
                                whileTap={{
                                    backgroundColor: "#f0f0f0",
                                    scale: 1.03
                                }}
                            >
                                Trade Now
                            </motion.button>
                        </Link>
                    </motion.div>
                </div>
            </motion.div>
        </nav>
    )
}