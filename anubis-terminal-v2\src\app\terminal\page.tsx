"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { TailSpin } from "react-loader-spinner";

export default function Terminal() {
    const router = useRouter();

    useEffect(() => {
        document.title = "🖥️ Welcome to Anubis Terminal";

        // Redirect after a brief delay to show welcome message
        const timeout = setTimeout(() => {
            router.push('/terminal/trending');
        }, 2000);

        return () => clearTimeout(timeout);
    }, [router]);

    return (
        <div className="min-h-[calc(100vh-80px)] flex flex-col items-center justify-center relative overflow-hidden px-4 bg-gradient-to-b from-[#0a0a0a] to-[#181818]">
            <div className="absolute inset-0 bg-gradient-to-b from-[#0a0a0a] to-[#1a1a1a] opacity-90 z-0"></div>
            <div className="relative z-10 text-center flex flex-col items-center gap-6">
                <div className="flex flex-col items-center gap-2">
                    <h1 className="text-5xl md:text-6xl font-orbitron font-extrabold mb-2 text-white drop-shadow-lg animate-fade-in">
                        Welcome to Anubis Terminal
                    </h1>
                    <p className="text-lg md:text-xl text-gray-300 max-w-xl mx-auto animate-fade-in delay-100">
                        Your all-in-one crypto trading dashboard. Get real-time market insights, manage your portfolio, and discover trending tokens—all in one place.
                    </p>
                </div>
                <div className="flex flex-col items-center gap-2 animate-fade-in delay-200">
                    <TailSpin
                        height="48"
                        width="48"
                        color="#4fa94d"
                        ariaLabel="tail-spin-loading"
                        radius="1"
                        visible={true}
                    />
                    <span className="text-sm text-gray-400 mt-2">Loading your personalized experience...</span>
                </div>
            </div>
        </div>
    )
}