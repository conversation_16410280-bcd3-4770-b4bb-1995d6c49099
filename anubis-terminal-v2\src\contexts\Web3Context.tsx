"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { chains } from '@/utils/data';
import { useAuth } from "./AuthContext";
import { getTokenData } from '@/services/api/flooz';
import { useUserData } from '@/hooks/useUserData';

// Use the type from data.ts
export type Chain = typeof chains[number];

interface Web3ContextType {
    chainList: Chain[];
    selectedChain: Chain | undefined;
    setSelectedChain: (chain: Chain) => void;
    recentlyViewed: { address: `0x${string}`, chain: string, data: any }[];
    addRecentlyViewed: (address: `0x${string}`, chain: string) => Promise<void>;
    removeRecentlyViewed: (index: number) => void;
}

const Web3Context = createContext<Web3ContextType | undefined>(undefined);

export const Web3Provider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [chainList] = useState<Chain[]>(chains);
    const { token } = useAuth();
    const { data } = useUserData(token);
    const [selectedChain, setSelectedChain] = useState<Chain | undefined>(undefined);

    // UseEffect to set selected chain from API
    useEffect(() => {
        if (data?.settings?.chainId) {
            const locatedChain = chainList.find(c => c.chainId === data.settings.chainId);
            if (locatedChain) {
                setSelectedChain(locatedChain);
            } else {
                setSelectedChain(chains[0]); // fallback if chainId is invalid
            }
        } else if (data && !data?.settings?.chainId) {
            setSelectedChain(chains[0]); // fallback if no chainId at all
        }
        // else: leave as undefined (loading)
    }, [data?.settings?.chainId, data, chainList]);

    // Initialize state from localStorage
    const [recentlyViewed, setRecentlyViewed] = useState<{ address: `0x${string}`, chain: string, data: any }[]>(() => {
        if (typeof window !== 'undefined') {
            const stored = localStorage.getItem('recentlyViewed');
            return stored ? JSON.parse(stored) : [];
        }
        return [];
    });

    // Update localStorage whenever recentlyViewed changes
    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem('recentlyViewed', JSON.stringify(recentlyViewed));
        }
    }, [recentlyViewed]);

    const addRecentlyViewed = useCallback(async (address: `0x${string}`, chain: string) => {
        setRecentlyViewed(prev => {
            // Check if item with same address and chain already exists
            const exists = prev.some(item => item.address === address && item.chain === chain);
            if (exists) {
                return prev;
            }
            // Optimistically add a placeholder while fetching
            return [...prev, { address, chain, data: null }];
        });
        try {
            const data = await getTokenData(chain, address);
            setRecentlyViewed(prev => {
                // Replace the placeholder with the real data
                return prev.map(item =>
                    item.address === address && item.chain === chain
                        ? { ...item, data }
                        : item
                );
            });
        } catch (error) {
            // Optionally handle error (e.g., remove placeholder)
            setRecentlyViewed(prev => prev.filter(item => !(item.address === address && item.chain === chain && item.data === null)));
        }
    }, []);

    const removeRecentlyViewed = (index: number) => {
        setRecentlyViewed(prev => {
            const newState = prev.filter((_, i) => i !== index);
            if (typeof window !== 'undefined') {
                localStorage.setItem('recentlyViewed', JSON.stringify(newState));
            }
            return newState;
        });
    }

    return (
        <Web3Context.Provider value={{ chainList, selectedChain, setSelectedChain, recentlyViewed, addRecentlyViewed, removeRecentlyViewed }}>
            {children}
        </Web3Context.Provider>
    );
};

export const useWeb3 = (): Web3ContextType => {
    const context = useContext(Web3Context);
    if (!context) {
        throw new Error('useWeb3 must be used within a Web3Provider');
    }
    return context;
};